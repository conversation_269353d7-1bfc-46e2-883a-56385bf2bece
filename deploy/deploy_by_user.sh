#!/bin/bash
# 指定的人，享受 build + deploy 二合一

user="$1"
branch="$2"
host=""

# 用户输入 author，转换为公网IP，然后调用 deploy_ci.sh
echo "提交者： ${user}"
echo "提交的分支：${branch}"
#if [[ $user == "leHaoTian <<EMAIL>>" ]];then
#  host="*************"
#elif [[ $user == "xiesang <<EMAIL>>" ]];then
#  host="*************"

#elif [[ $user == "<PERSON>.Luo <<EMAIL>>" ]];then
#  host="*************"
#else
#  echo "未登记的提交者：${user} , 无法匹配 IP"
#  exit 0
#fi

echo "匹配的公网IP：${host}"
sudo ./deploy/deploy_ci.sh ${host} ${branch}
