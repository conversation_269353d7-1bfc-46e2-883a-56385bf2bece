/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * 联盟建筑类型
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsAllianceFeastStatus implements org.apache.thrift.TEnum {
  /**
   * 空
   */
  NONE(1),
  /**
   * 预备
   */
  READY(2),
  /**
   * 开启中
   */
  OPENING(3),
  /**
   * 冷却中
   */
  COOLING(4);

  private final int value;

  private PsAllianceFeastStatus(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsAllianceFeastStatus findByValue(int value) { 
    switch (value) {
      case 1:
        return NONE;
      case 2:
        return READY;
      case 3:
        return OPENING;
      case 4:
        return COOLING;
      default:
        return null;
    }
  }
}
