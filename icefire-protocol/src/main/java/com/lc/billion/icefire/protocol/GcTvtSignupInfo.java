/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * TVT活动个人报名信息
 * @Message(7162)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcTvtSignupInfo implements org.apache.thrift.TBase<GcTvtSignupInfo, GcTvtSignupInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcTvtSignupInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcTvtSignupInfo");

  private static final org.apache.thrift.protocol.TField SIGNUP_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("signupStatus", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField FIGHT_POWER_FIELD_DESC = new org.apache.thrift.protocol.TField("fightPower", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField TVT_SIDE_FIELD_DESC = new org.apache.thrift.protocol.TField("tvtSide", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TEAMMATES_FIELD_DESC = new org.apache.thrift.protocol.TField("teammates", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField BATTLE_START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("battleStartTime", org.apache.thrift.protocol.TType.I64, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcTvtSignupInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcTvtSignupInfoTupleSchemeFactory();

  /**
   * 状态 0-未报名 1-已报名 2-匹配成功等待入场 3-匹配失败
   */
  public int signupStatus; // optional
  public long fightPower; // optional
  /**
   * tvt阵营（红蓝方）1-红方 2-蓝方
   */
  public int tvtSide; // optional
  /**
   * 队友信息
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo> teammates; // optional
  /**
   * 战斗开始时间
   */
  public long battleStartTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 状态 0-未报名 1-已报名 2-匹配成功等待入场 3-匹配失败
     */
    SIGNUP_STATUS((short)1, "signupStatus"),
    FIGHT_POWER((short)2, "fightPower"),
    /**
     * tvt阵营（红蓝方）1-红方 2-蓝方
     */
    TVT_SIDE((short)3, "tvtSide"),
    /**
     * 队友信息
     */
    TEAMMATES((short)4, "teammates"),
    /**
     * 战斗开始时间
     */
    BATTLE_START_TIME((short)5, "battleStartTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SIGNUP_STATUS
          return SIGNUP_STATUS;
        case 2: // FIGHT_POWER
          return FIGHT_POWER;
        case 3: // TVT_SIDE
          return TVT_SIDE;
        case 4: // TEAMMATES
          return TEAMMATES;
        case 5: // BATTLE_START_TIME
          return BATTLE_START_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SIGNUPSTATUS_ISSET_ID = 0;
  private static final int __FIGHTPOWER_ISSET_ID = 1;
  private static final int __TVTSIDE_ISSET_ID = 2;
  private static final int __BATTLESTARTTIME_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.SIGNUP_STATUS,_Fields.FIGHT_POWER,_Fields.TVT_SIDE,_Fields.TEAMMATES,_Fields.BATTLE_START_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SIGNUP_STATUS, new org.apache.thrift.meta_data.FieldMetaData("signupStatus", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FIGHT_POWER, new org.apache.thrift.meta_data.FieldMetaData("fightPower", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TVT_SIDE, new org.apache.thrift.meta_data.FieldMetaData("tvtSide", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TEAMMATES, new org.apache.thrift.meta_data.FieldMetaData("teammates", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo.class))));
    tmpMap.put(_Fields.BATTLE_START_TIME, new org.apache.thrift.meta_data.FieldMetaData("battleStartTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcTvtSignupInfo.class, metaDataMap);
  }

  public GcTvtSignupInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcTvtSignupInfo(GcTvtSignupInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.signupStatus = other.signupStatus;
    this.fightPower = other.fightPower;
    this.tvtSide = other.tvtSide;
    if (other.isSetTeammates()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo> __this__teammates = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo>(other.teammates.size());
      for (com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo other_element : other.teammates) {
        __this__teammates.add(new com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo(other_element));
      }
      this.teammates = __this__teammates;
    }
    this.battleStartTime = other.battleStartTime;
  }

  public GcTvtSignupInfo deepCopy() {
    return new GcTvtSignupInfo(this);
  }

  @Override
  public void clear() {
    setSignupStatusIsSet(false);
    this.signupStatus = 0;
    setFightPowerIsSet(false);
    this.fightPower = 0;
    setTvtSideIsSet(false);
    this.tvtSide = 0;
    this.teammates = null;
    setBattleStartTimeIsSet(false);
    this.battleStartTime = 0;
  }

  /**
   * 状态 0-未报名 1-已报名 2-匹配成功等待入场 3-匹配失败
   */
  public int getSignupStatus() {
    return this.signupStatus;
  }

  /**
   * 状态 0-未报名 1-已报名 2-匹配成功等待入场 3-匹配失败
   */
  public GcTvtSignupInfo setSignupStatus(int signupStatus) {
    this.signupStatus = signupStatus;
    setSignupStatusIsSet(true);
    return this;
  }

  public void unsetSignupStatus() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SIGNUPSTATUS_ISSET_ID);
  }

  /** Returns true if field signupStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetSignupStatus() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SIGNUPSTATUS_ISSET_ID);
  }

  public void setSignupStatusIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SIGNUPSTATUS_ISSET_ID, value);
  }

  public long getFightPower() {
    return this.fightPower;
  }

  public GcTvtSignupInfo setFightPower(long fightPower) {
    this.fightPower = fightPower;
    setFightPowerIsSet(true);
    return this;
  }

  public void unsetFightPower() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __FIGHTPOWER_ISSET_ID);
  }

  /** Returns true if field fightPower is set (has been assigned a value) and false otherwise */
  public boolean isSetFightPower() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __FIGHTPOWER_ISSET_ID);
  }

  public void setFightPowerIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __FIGHTPOWER_ISSET_ID, value);
  }

  /**
   * tvt阵营（红蓝方）1-红方 2-蓝方
   */
  public int getTvtSide() {
    return this.tvtSide;
  }

  /**
   * tvt阵营（红蓝方）1-红方 2-蓝方
   */
  public GcTvtSignupInfo setTvtSide(int tvtSide) {
    this.tvtSide = tvtSide;
    setTvtSideIsSet(true);
    return this;
  }

  public void unsetTvtSide() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TVTSIDE_ISSET_ID);
  }

  /** Returns true if field tvtSide is set (has been assigned a value) and false otherwise */
  public boolean isSetTvtSide() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TVTSIDE_ISSET_ID);
  }

  public void setTvtSideIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TVTSIDE_ISSET_ID, value);
  }

  public int getTeammatesSize() {
    return (this.teammates == null) ? 0 : this.teammates.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo> getTeammatesIterator() {
    return (this.teammates == null) ? null : this.teammates.iterator();
  }

  public void addToTeammates(com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo elem) {
    if (this.teammates == null) {
      this.teammates = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo>();
    }
    this.teammates.add(elem);
  }

  /**
   * 队友信息
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo> getTeammates() {
    return this.teammates;
  }

  /**
   * 队友信息
   */
  public GcTvtSignupInfo setTeammates(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo> teammates) {
    this.teammates = teammates;
    return this;
  }

  public void unsetTeammates() {
    this.teammates = null;
  }

  /** Returns true if field teammates is set (has been assigned a value) and false otherwise */
  public boolean isSetTeammates() {
    return this.teammates != null;
  }

  public void setTeammatesIsSet(boolean value) {
    if (!value) {
      this.teammates = null;
    }
  }

  /**
   * 战斗开始时间
   */
  public long getBattleStartTime() {
    return this.battleStartTime;
  }

  /**
   * 战斗开始时间
   */
  public GcTvtSignupInfo setBattleStartTime(long battleStartTime) {
    this.battleStartTime = battleStartTime;
    setBattleStartTimeIsSet(true);
    return this;
  }

  public void unsetBattleStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BATTLESTARTTIME_ISSET_ID);
  }

  /** Returns true if field battleStartTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBattleStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BATTLESTARTTIME_ISSET_ID);
  }

  public void setBattleStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BATTLESTARTTIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case SIGNUP_STATUS:
      if (value == null) {
        unsetSignupStatus();
      } else {
        setSignupStatus((java.lang.Integer)value);
      }
      break;

    case FIGHT_POWER:
      if (value == null) {
        unsetFightPower();
      } else {
        setFightPower((java.lang.Long)value);
      }
      break;

    case TVT_SIDE:
      if (value == null) {
        unsetTvtSide();
      } else {
        setTvtSide((java.lang.Integer)value);
      }
      break;

    case TEAMMATES:
      if (value == null) {
        unsetTeammates();
      } else {
        setTeammates((java.util.List<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo>)value);
      }
      break;

    case BATTLE_START_TIME:
      if (value == null) {
        unsetBattleStartTime();
      } else {
        setBattleStartTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case SIGNUP_STATUS:
      return getSignupStatus();

    case FIGHT_POWER:
      return getFightPower();

    case TVT_SIDE:
      return getTvtSide();

    case TEAMMATES:
      return getTeammates();

    case BATTLE_START_TIME:
      return getBattleStartTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case SIGNUP_STATUS:
      return isSetSignupStatus();
    case FIGHT_POWER:
      return isSetFightPower();
    case TVT_SIDE:
      return isSetTvtSide();
    case TEAMMATES:
      return isSetTeammates();
    case BATTLE_START_TIME:
      return isSetBattleStartTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcTvtSignupInfo)
      return this.equals((GcTvtSignupInfo)that);
    return false;
  }

  public boolean equals(GcTvtSignupInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_signupStatus = true && this.isSetSignupStatus();
    boolean that_present_signupStatus = true && that.isSetSignupStatus();
    if (this_present_signupStatus || that_present_signupStatus) {
      if (!(this_present_signupStatus && that_present_signupStatus))
        return false;
      if (this.signupStatus != that.signupStatus)
        return false;
    }

    boolean this_present_fightPower = true && this.isSetFightPower();
    boolean that_present_fightPower = true && that.isSetFightPower();
    if (this_present_fightPower || that_present_fightPower) {
      if (!(this_present_fightPower && that_present_fightPower))
        return false;
      if (this.fightPower != that.fightPower)
        return false;
    }

    boolean this_present_tvtSide = true && this.isSetTvtSide();
    boolean that_present_tvtSide = true && that.isSetTvtSide();
    if (this_present_tvtSide || that_present_tvtSide) {
      if (!(this_present_tvtSide && that_present_tvtSide))
        return false;
      if (this.tvtSide != that.tvtSide)
        return false;
    }

    boolean this_present_teammates = true && this.isSetTeammates();
    boolean that_present_teammates = true && that.isSetTeammates();
    if (this_present_teammates || that_present_teammates) {
      if (!(this_present_teammates && that_present_teammates))
        return false;
      if (!this.teammates.equals(that.teammates))
        return false;
    }

    boolean this_present_battleStartTime = true && this.isSetBattleStartTime();
    boolean that_present_battleStartTime = true && that.isSetBattleStartTime();
    if (this_present_battleStartTime || that_present_battleStartTime) {
      if (!(this_present_battleStartTime && that_present_battleStartTime))
        return false;
      if (this.battleStartTime != that.battleStartTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetSignupStatus()) ? 131071 : 524287);
    if (isSetSignupStatus())
      hashCode = hashCode * 8191 + signupStatus;

    hashCode = hashCode * 8191 + ((isSetFightPower()) ? 131071 : 524287);
    if (isSetFightPower())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(fightPower);

    hashCode = hashCode * 8191 + ((isSetTvtSide()) ? 131071 : 524287);
    if (isSetTvtSide())
      hashCode = hashCode * 8191 + tvtSide;

    hashCode = hashCode * 8191 + ((isSetTeammates()) ? 131071 : 524287);
    if (isSetTeammates())
      hashCode = hashCode * 8191 + teammates.hashCode();

    hashCode = hashCode * 8191 + ((isSetBattleStartTime()) ? 131071 : 524287);
    if (isSetBattleStartTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(battleStartTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcTvtSignupInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetSignupStatus(), other.isSetSignupStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignupStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signupStatus, other.signupStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetFightPower(), other.isSetFightPower());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFightPower()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fightPower, other.fightPower);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTvtSide(), other.isSetTvtSide());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTvtSide()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tvtSide, other.tvtSide);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTeammates(), other.isSetTeammates());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTeammates()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.teammates, other.teammates);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBattleStartTime(), other.isSetBattleStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBattleStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.battleStartTime, other.battleStartTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcTvtSignupInfo(");
    boolean first = true;

    if (isSetSignupStatus()) {
      sb.append("signupStatus:");
      sb.append(this.signupStatus);
      first = false;
    }
    if (isSetFightPower()) {
      if (!first) sb.append(", ");
      sb.append("fightPower:");
      sb.append(this.fightPower);
      first = false;
    }
    if (isSetTvtSide()) {
      if (!first) sb.append(", ");
      sb.append("tvtSide:");
      sb.append(this.tvtSide);
      first = false;
    }
    if (isSetTeammates()) {
      if (!first) sb.append(", ");
      sb.append("teammates:");
      if (this.teammates == null) {
        sb.append("null");
      } else {
        sb.append(this.teammates);
      }
      first = false;
    }
    if (isSetBattleStartTime()) {
      if (!first) sb.append(", ");
      sb.append("battleStartTime:");
      sb.append(this.battleStartTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcTvtSignupInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcTvtSignupInfoStandardScheme getScheme() {
      return new GcTvtSignupInfoStandardScheme();
    }
  }

  private static class GcTvtSignupInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcTvtSignupInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcTvtSignupInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SIGNUP_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.signupStatus = iprot.readI32();
              struct.setSignupStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // FIGHT_POWER
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fightPower = iprot.readI64();
              struct.setFightPowerIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TVT_SIDE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.tvtSide = iprot.readI32();
              struct.setTvtSideIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TEAMMATES
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.teammates = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo();
                  _elem1.read(iprot);
                  struct.teammates.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setTeammatesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // BATTLE_START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.battleStartTime = iprot.readI64();
              struct.setBattleStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcTvtSignupInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetSignupStatus()) {
        oprot.writeFieldBegin(SIGNUP_STATUS_FIELD_DESC);
        oprot.writeI32(struct.signupStatus);
        oprot.writeFieldEnd();
      }
      if (struct.isSetFightPower()) {
        oprot.writeFieldBegin(FIGHT_POWER_FIELD_DESC);
        oprot.writeI64(struct.fightPower);
        oprot.writeFieldEnd();
      }
      if (struct.isSetTvtSide()) {
        oprot.writeFieldBegin(TVT_SIDE_FIELD_DESC);
        oprot.writeI32(struct.tvtSide);
        oprot.writeFieldEnd();
      }
      if (struct.teammates != null) {
        if (struct.isSetTeammates()) {
          oprot.writeFieldBegin(TEAMMATES_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.teammates.size()));
            for (com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo _iter3 : struct.teammates)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetBattleStartTime()) {
        oprot.writeFieldBegin(BATTLE_START_TIME_FIELD_DESC);
        oprot.writeI64(struct.battleStartTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcTvtSignupInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcTvtSignupInfoTupleScheme getScheme() {
      return new GcTvtSignupInfoTupleScheme();
    }
  }

  private static class GcTvtSignupInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcTvtSignupInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcTvtSignupInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetSignupStatus()) {
        optionals.set(0);
      }
      if (struct.isSetFightPower()) {
        optionals.set(1);
      }
      if (struct.isSetTvtSide()) {
        optionals.set(2);
      }
      if (struct.isSetTeammates()) {
        optionals.set(3);
      }
      if (struct.isSetBattleStartTime()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetSignupStatus()) {
        oprot.writeI32(struct.signupStatus);
      }
      if (struct.isSetFightPower()) {
        oprot.writeI64(struct.fightPower);
      }
      if (struct.isSetTvtSide()) {
        oprot.writeI32(struct.tvtSide);
      }
      if (struct.isSetTeammates()) {
        {
          oprot.writeI32(struct.teammates.size());
          for (com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo _iter4 : struct.teammates)
          {
            _iter4.write(oprot);
          }
        }
      }
      if (struct.isSetBattleStartTime()) {
        oprot.writeI64(struct.battleStartTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcTvtSignupInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.signupStatus = iprot.readI32();
        struct.setSignupStatusIsSet(true);
      }
      if (incoming.get(1)) {
        struct.fightPower = iprot.readI64();
        struct.setFightPowerIsSet(true);
      }
      if (incoming.get(2)) {
        struct.tvtSide = iprot.readI32();
        struct.setTvtSideIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.teammates = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsTvtPlayerSimpleInfo();
            _elem6.read(iprot);
            struct.teammates.add(_elem6);
          }
        }
        struct.setTeammatesIsSet(true);
      }
      if (incoming.get(4)) {
        struct.battleStartTime = iprot.readI64();
        struct.setBattleStartTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

