/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 领取免费宝箱 应答
 * @Message(973)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcFreeTreasureBoxGetReward implements org.apache.thrift.TBase<GcFreeTreasureBoxGetReward, GcFreeTreasureBoxGetReward._Fields>, java.io.Serializable, Cloneable, Comparable<GcFreeTreasureBoxGetReward> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcFreeTreasureBoxGetReward");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField OK_FIELD_DESC = new org.apache.thrift.protocol.TField("ok", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcFreeTreasureBoxGetRewardStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcFreeTreasureBoxGetRewardTupleSchemeFactory();

  /**
   * FreeBox表的id列
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String id; // required
  /**
   * 是否成功 1: 成功，0: 失败
   */
  public int ok; // required
  /**
   * 失败信息
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String reason; // optional
  /**
   * 如果成功的话有奖励
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * FreeBox表的id列
     */
    ID((short)1, "id"),
    /**
     * 是否成功 1: 成功，0: 失败
     */
    OK((short)2, "ok"),
    /**
     * 失败信息
     */
    REASON((short)3, "reason"),
    /**
     * 如果成功的话有奖励
     */
    REWARDS((short)4, "rewards");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // OK
          return OK;
        case 3: // REASON
          return REASON;
        case 4: // REWARDS
          return REWARDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __OK_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REASON,_Fields.REWARDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.OK, new org.apache.thrift.meta_data.FieldMetaData("ok", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcFreeTreasureBoxGetReward.class, metaDataMap);
  }

  public GcFreeTreasureBoxGetReward() {
  }

  public GcFreeTreasureBoxGetReward(
    java.lang.String id,
    int ok)
  {
    this();
    this.id = id;
    this.ok = ok;
    setOkIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcFreeTreasureBoxGetReward(GcFreeTreasureBoxGetReward other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetId()) {
      this.id = other.id;
    }
    this.ok = other.ok;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
    if (other.isSetRewards()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.rewards.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
  }

  public GcFreeTreasureBoxGetReward deepCopy() {
    return new GcFreeTreasureBoxGetReward(this);
  }

  @Override
  public void clear() {
    this.id = null;
    setOkIsSet(false);
    this.ok = 0;
    this.reason = null;
    this.rewards = null;
  }

  /**
   * FreeBox表的id列
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getId() {
    return this.id;
  }

  /**
   * FreeBox表的id列
   */
  public GcFreeTreasureBoxGetReward setId(@org.apache.thrift.annotation.Nullable java.lang.String id) {
    this.id = id;
    return this;
  }

  public void unsetId() {
    this.id = null;
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return this.id != null;
  }

  public void setIdIsSet(boolean value) {
    if (!value) {
      this.id = null;
    }
  }

  /**
   * 是否成功 1: 成功，0: 失败
   */
  public int getOk() {
    return this.ok;
  }

  /**
   * 是否成功 1: 成功，0: 失败
   */
  public GcFreeTreasureBoxGetReward setOk(int ok) {
    this.ok = ok;
    setOkIsSet(true);
    return this;
  }

  public void unsetOk() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OK_ISSET_ID);
  }

  /** Returns true if field ok is set (has been assigned a value) and false otherwise */
  public boolean isSetOk() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OK_ISSET_ID);
  }

  public void setOkIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OK_ISSET_ID, value);
  }

  /**
   * 失败信息
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getReason() {
    return this.reason;
  }

  /**
   * 失败信息
   */
  public GcFreeTreasureBoxGetReward setReason(@org.apache.thrift.annotation.Nullable java.lang.String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  /**
   * 如果成功的话有奖励
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewards() {
    return this.rewards;
  }

  /**
   * 如果成功的话有奖励
   */
  public GcFreeTreasureBoxGetReward setRewards(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((java.lang.String)value);
      }
      break;

    case OK:
      if (value == null) {
        unsetOk();
      } else {
        setOk((java.lang.Integer)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((java.lang.String)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case OK:
      return getOk();

    case REASON:
      return getReason();

    case REWARDS:
      return getRewards();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case OK:
      return isSetOk();
    case REASON:
      return isSetReason();
    case REWARDS:
      return isSetRewards();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcFreeTreasureBoxGetReward)
      return this.equals((GcFreeTreasureBoxGetReward)that);
    return false;
  }

  public boolean equals(GcFreeTreasureBoxGetReward that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_id = true && this.isSetId();
    boolean that_present_id = true && that.isSetId();
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (!this.id.equals(that.id))
        return false;
    }

    boolean this_present_ok = true;
    boolean that_present_ok = true;
    if (this_present_ok || that_present_ok) {
      if (!(this_present_ok && that_present_ok))
        return false;
      if (this.ok != that.ok)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetId()) ? 131071 : 524287);
    if (isSetId())
      hashCode = hashCode * 8191 + id.hashCode();

    hashCode = hashCode * 8191 + ok;

    hashCode = hashCode * 8191 + ((isSetReason()) ? 131071 : 524287);
    if (isSetReason())
      hashCode = hashCode * 8191 + reason.hashCode();

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcFreeTreasureBoxGetReward other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetId(), other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOk(), other.isSetOk());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOk()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ok, other.ok);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetReason(), other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcFreeTreasureBoxGetReward(");
    boolean first = true;

    sb.append("id:");
    if (this.id == null) {
      sb.append("null");
    } else {
      sb.append(this.id);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ok:");
    sb.append(this.ok);
    first = false;
    if (isSetReason()) {
      if (!first) sb.append(", ");
      sb.append("reason:");
      if (this.reason == null) {
        sb.append("null");
      } else {
        sb.append(this.reason);
      }
      first = false;
    }
    if (isSetRewards()) {
      if (!first) sb.append(", ");
      sb.append("rewards:");
      if (this.rewards == null) {
        sb.append("null");
      } else {
        sb.append(this.rewards);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (id == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'id' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'ok' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcFreeTreasureBoxGetRewardStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcFreeTreasureBoxGetRewardStandardScheme getScheme() {
      return new GcFreeTreasureBoxGetRewardStandardScheme();
    }
  }

  private static class GcFreeTreasureBoxGetRewardStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcFreeTreasureBoxGetReward> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcFreeTreasureBoxGetReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.id = iprot.readString();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // OK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.ok = iprot.readI32();
              struct.setOkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.rewards.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetOk()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'ok' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcFreeTreasureBoxGetReward struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.id != null) {
        oprot.writeFieldBegin(ID_FIELD_DESC);
        oprot.writeString(struct.id);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(OK_FIELD_DESC);
      oprot.writeI32(struct.ok);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        if (struct.isSetReason()) {
          oprot.writeFieldBegin(REASON_FIELD_DESC);
          oprot.writeString(struct.reason);
          oprot.writeFieldEnd();
        }
      }
      if (struct.rewards != null) {
        if (struct.isSetRewards()) {
          oprot.writeFieldBegin(REWARDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter3 : struct.rewards)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcFreeTreasureBoxGetRewardTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcFreeTreasureBoxGetRewardTupleScheme getScheme() {
      return new GcFreeTreasureBoxGetRewardTupleScheme();
    }
  }

  private static class GcFreeTreasureBoxGetRewardTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcFreeTreasureBoxGetReward> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcFreeTreasureBoxGetReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.id);
      oprot.writeI32(struct.ok);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetReason()) {
        optionals.set(0);
      }
      if (struct.isSetRewards()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
      if (struct.isSetRewards()) {
        {
          oprot.writeI32(struct.rewards.size());
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter4 : struct.rewards)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcFreeTreasureBoxGetReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.id = iprot.readString();
      struct.setIdIsSet(true);
      struct.ok = iprot.readI32();
      struct.setOkIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
            _elem6.read(iprot);
            struct.rewards.add(_elem6);
          }
        }
        struct.setRewardsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

