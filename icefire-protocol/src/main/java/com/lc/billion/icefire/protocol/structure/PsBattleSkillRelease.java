/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsBattleSkillRelease implements org.apache.thrift.TBase<PsBattleSkillRelease, PsBattleSkillRelease._Fields>, java.io.Serializable, Cloneable, Comparable<PsBattleSkillRelease> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsBattleSkillRelease");

  private static final org.apache.thrift.protocol.TField HERO_SOURCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("heroSourceId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField HERO_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("heroMetaId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField SKILL_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("skillMetaId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField SKILL_LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("skillLevel", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField EFFECT_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("effectMetaId", org.apache.thrift.protocol.TType.STRING, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsBattleSkillReleaseStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsBattleSkillReleaseTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String heroSourceId; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String heroMetaId; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String skillMetaId; // required
  public int skillLevel; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String effectMetaId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    HERO_SOURCE_ID((short)1, "heroSourceId"),
    HERO_META_ID((short)2, "heroMetaId"),
    SKILL_META_ID((short)3, "skillMetaId"),
    SKILL_LEVEL((short)4, "skillLevel"),
    EFFECT_META_ID((short)5, "effectMetaId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HERO_SOURCE_ID
          return HERO_SOURCE_ID;
        case 2: // HERO_META_ID
          return HERO_META_ID;
        case 3: // SKILL_META_ID
          return SKILL_META_ID;
        case 4: // SKILL_LEVEL
          return SKILL_LEVEL;
        case 5: // EFFECT_META_ID
          return EFFECT_META_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SKILLLEVEL_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.HERO_SOURCE_ID,_Fields.HERO_META_ID,_Fields.EFFECT_META_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HERO_SOURCE_ID, new org.apache.thrift.meta_data.FieldMetaData("heroSourceId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.HERO_META_ID, new org.apache.thrift.meta_data.FieldMetaData("heroMetaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SKILL_META_ID, new org.apache.thrift.meta_data.FieldMetaData("skillMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SKILL_LEVEL, new org.apache.thrift.meta_data.FieldMetaData("skillLevel", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EFFECT_META_ID, new org.apache.thrift.meta_data.FieldMetaData("effectMetaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsBattleSkillRelease.class, metaDataMap);
  }

  public PsBattleSkillRelease() {
  }

  public PsBattleSkillRelease(
    java.lang.String skillMetaId,
    int skillLevel)
  {
    this();
    this.skillMetaId = skillMetaId;
    this.skillLevel = skillLevel;
    setSkillLevelIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsBattleSkillRelease(PsBattleSkillRelease other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetHeroSourceId()) {
      this.heroSourceId = other.heroSourceId;
    }
    if (other.isSetHeroMetaId()) {
      this.heroMetaId = other.heroMetaId;
    }
    if (other.isSetSkillMetaId()) {
      this.skillMetaId = other.skillMetaId;
    }
    this.skillLevel = other.skillLevel;
    if (other.isSetEffectMetaId()) {
      this.effectMetaId = other.effectMetaId;
    }
  }

  public PsBattleSkillRelease deepCopy() {
    return new PsBattleSkillRelease(this);
  }

  @Override
  public void clear() {
    this.heroSourceId = null;
    this.heroMetaId = null;
    this.skillMetaId = null;
    setSkillLevelIsSet(false);
    this.skillLevel = 0;
    this.effectMetaId = null;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHeroSourceId() {
    return this.heroSourceId;
  }

  public PsBattleSkillRelease setHeroSourceId(@org.apache.thrift.annotation.Nullable java.lang.String heroSourceId) {
    this.heroSourceId = heroSourceId;
    return this;
  }

  public void unsetHeroSourceId() {
    this.heroSourceId = null;
  }

  /** Returns true if field heroSourceId is set (has been assigned a value) and false otherwise */
  public boolean isSetHeroSourceId() {
    return this.heroSourceId != null;
  }

  public void setHeroSourceIdIsSet(boolean value) {
    if (!value) {
      this.heroSourceId = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHeroMetaId() {
    return this.heroMetaId;
  }

  public PsBattleSkillRelease setHeroMetaId(@org.apache.thrift.annotation.Nullable java.lang.String heroMetaId) {
    this.heroMetaId = heroMetaId;
    return this;
  }

  public void unsetHeroMetaId() {
    this.heroMetaId = null;
  }

  /** Returns true if field heroMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetHeroMetaId() {
    return this.heroMetaId != null;
  }

  public void setHeroMetaIdIsSet(boolean value) {
    if (!value) {
      this.heroMetaId = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getSkillMetaId() {
    return this.skillMetaId;
  }

  public PsBattleSkillRelease setSkillMetaId(@org.apache.thrift.annotation.Nullable java.lang.String skillMetaId) {
    this.skillMetaId = skillMetaId;
    return this;
  }

  public void unsetSkillMetaId() {
    this.skillMetaId = null;
  }

  /** Returns true if field skillMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetSkillMetaId() {
    return this.skillMetaId != null;
  }

  public void setSkillMetaIdIsSet(boolean value) {
    if (!value) {
      this.skillMetaId = null;
    }
  }

  public int getSkillLevel() {
    return this.skillLevel;
  }

  public PsBattleSkillRelease setSkillLevel(int skillLevel) {
    this.skillLevel = skillLevel;
    setSkillLevelIsSet(true);
    return this;
  }

  public void unsetSkillLevel() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SKILLLEVEL_ISSET_ID);
  }

  /** Returns true if field skillLevel is set (has been assigned a value) and false otherwise */
  public boolean isSetSkillLevel() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SKILLLEVEL_ISSET_ID);
  }

  public void setSkillLevelIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SKILLLEVEL_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getEffectMetaId() {
    return this.effectMetaId;
  }

  public PsBattleSkillRelease setEffectMetaId(@org.apache.thrift.annotation.Nullable java.lang.String effectMetaId) {
    this.effectMetaId = effectMetaId;
    return this;
  }

  public void unsetEffectMetaId() {
    this.effectMetaId = null;
  }

  /** Returns true if field effectMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetEffectMetaId() {
    return this.effectMetaId != null;
  }

  public void setEffectMetaIdIsSet(boolean value) {
    if (!value) {
      this.effectMetaId = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case HERO_SOURCE_ID:
      if (value == null) {
        unsetHeroSourceId();
      } else {
        setHeroSourceId((java.lang.String)value);
      }
      break;

    case HERO_META_ID:
      if (value == null) {
        unsetHeroMetaId();
      } else {
        setHeroMetaId((java.lang.String)value);
      }
      break;

    case SKILL_META_ID:
      if (value == null) {
        unsetSkillMetaId();
      } else {
        setSkillMetaId((java.lang.String)value);
      }
      break;

    case SKILL_LEVEL:
      if (value == null) {
        unsetSkillLevel();
      } else {
        setSkillLevel((java.lang.Integer)value);
      }
      break;

    case EFFECT_META_ID:
      if (value == null) {
        unsetEffectMetaId();
      } else {
        setEffectMetaId((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case HERO_SOURCE_ID:
      return getHeroSourceId();

    case HERO_META_ID:
      return getHeroMetaId();

    case SKILL_META_ID:
      return getSkillMetaId();

    case SKILL_LEVEL:
      return getSkillLevel();

    case EFFECT_META_ID:
      return getEffectMetaId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case HERO_SOURCE_ID:
      return isSetHeroSourceId();
    case HERO_META_ID:
      return isSetHeroMetaId();
    case SKILL_META_ID:
      return isSetSkillMetaId();
    case SKILL_LEVEL:
      return isSetSkillLevel();
    case EFFECT_META_ID:
      return isSetEffectMetaId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsBattleSkillRelease)
      return this.equals((PsBattleSkillRelease)that);
    return false;
  }

  public boolean equals(PsBattleSkillRelease that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_heroSourceId = true && this.isSetHeroSourceId();
    boolean that_present_heroSourceId = true && that.isSetHeroSourceId();
    if (this_present_heroSourceId || that_present_heroSourceId) {
      if (!(this_present_heroSourceId && that_present_heroSourceId))
        return false;
      if (!this.heroSourceId.equals(that.heroSourceId))
        return false;
    }

    boolean this_present_heroMetaId = true && this.isSetHeroMetaId();
    boolean that_present_heroMetaId = true && that.isSetHeroMetaId();
    if (this_present_heroMetaId || that_present_heroMetaId) {
      if (!(this_present_heroMetaId && that_present_heroMetaId))
        return false;
      if (!this.heroMetaId.equals(that.heroMetaId))
        return false;
    }

    boolean this_present_skillMetaId = true && this.isSetSkillMetaId();
    boolean that_present_skillMetaId = true && that.isSetSkillMetaId();
    if (this_present_skillMetaId || that_present_skillMetaId) {
      if (!(this_present_skillMetaId && that_present_skillMetaId))
        return false;
      if (!this.skillMetaId.equals(that.skillMetaId))
        return false;
    }

    boolean this_present_skillLevel = true;
    boolean that_present_skillLevel = true;
    if (this_present_skillLevel || that_present_skillLevel) {
      if (!(this_present_skillLevel && that_present_skillLevel))
        return false;
      if (this.skillLevel != that.skillLevel)
        return false;
    }

    boolean this_present_effectMetaId = true && this.isSetEffectMetaId();
    boolean that_present_effectMetaId = true && that.isSetEffectMetaId();
    if (this_present_effectMetaId || that_present_effectMetaId) {
      if (!(this_present_effectMetaId && that_present_effectMetaId))
        return false;
      if (!this.effectMetaId.equals(that.effectMetaId))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetHeroSourceId()) ? 131071 : 524287);
    if (isSetHeroSourceId())
      hashCode = hashCode * 8191 + heroSourceId.hashCode();

    hashCode = hashCode * 8191 + ((isSetHeroMetaId()) ? 131071 : 524287);
    if (isSetHeroMetaId())
      hashCode = hashCode * 8191 + heroMetaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetSkillMetaId()) ? 131071 : 524287);
    if (isSetSkillMetaId())
      hashCode = hashCode * 8191 + skillMetaId.hashCode();

    hashCode = hashCode * 8191 + skillLevel;

    hashCode = hashCode * 8191 + ((isSetEffectMetaId()) ? 131071 : 524287);
    if (isSetEffectMetaId())
      hashCode = hashCode * 8191 + effectMetaId.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsBattleSkillRelease other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetHeroSourceId(), other.isSetHeroSourceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeroSourceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.heroSourceId, other.heroSourceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHeroMetaId(), other.isSetHeroMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeroMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.heroMetaId, other.heroMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSkillMetaId(), other.isSetSkillMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSkillMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.skillMetaId, other.skillMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSkillLevel(), other.isSetSkillLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSkillLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.skillLevel, other.skillLevel);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEffectMetaId(), other.isSetEffectMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEffectMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.effectMetaId, other.effectMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsBattleSkillRelease(");
    boolean first = true;

    if (isSetHeroSourceId()) {
      sb.append("heroSourceId:");
      if (this.heroSourceId == null) {
        sb.append("null");
      } else {
        sb.append(this.heroSourceId);
      }
      first = false;
    }
    if (isSetHeroMetaId()) {
      if (!first) sb.append(", ");
      sb.append("heroMetaId:");
      if (this.heroMetaId == null) {
        sb.append("null");
      } else {
        sb.append(this.heroMetaId);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("skillMetaId:");
    if (this.skillMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.skillMetaId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("skillLevel:");
    sb.append(this.skillLevel);
    first = false;
    if (isSetEffectMetaId()) {
      if (!first) sb.append(", ");
      sb.append("effectMetaId:");
      if (this.effectMetaId == null) {
        sb.append("null");
      } else {
        sb.append(this.effectMetaId);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (skillMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'skillMetaId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'skillLevel' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsBattleSkillReleaseStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsBattleSkillReleaseStandardScheme getScheme() {
      return new PsBattleSkillReleaseStandardScheme();
    }
  }

  private static class PsBattleSkillReleaseStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsBattleSkillRelease> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsBattleSkillRelease struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HERO_SOURCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.heroSourceId = iprot.readString();
              struct.setHeroSourceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // HERO_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.heroMetaId = iprot.readString();
              struct.setHeroMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SKILL_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.skillMetaId = iprot.readString();
              struct.setSkillMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SKILL_LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.skillLevel = iprot.readI32();
              struct.setSkillLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // EFFECT_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.effectMetaId = iprot.readString();
              struct.setEffectMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetSkillLevel()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'skillLevel' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsBattleSkillRelease struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.heroSourceId != null) {
        if (struct.isSetHeroSourceId()) {
          oprot.writeFieldBegin(HERO_SOURCE_ID_FIELD_DESC);
          oprot.writeString(struct.heroSourceId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.heroMetaId != null) {
        if (struct.isSetHeroMetaId()) {
          oprot.writeFieldBegin(HERO_META_ID_FIELD_DESC);
          oprot.writeString(struct.heroMetaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.skillMetaId != null) {
        oprot.writeFieldBegin(SKILL_META_ID_FIELD_DESC);
        oprot.writeString(struct.skillMetaId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SKILL_LEVEL_FIELD_DESC);
      oprot.writeI32(struct.skillLevel);
      oprot.writeFieldEnd();
      if (struct.effectMetaId != null) {
        if (struct.isSetEffectMetaId()) {
          oprot.writeFieldBegin(EFFECT_META_ID_FIELD_DESC);
          oprot.writeString(struct.effectMetaId);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsBattleSkillReleaseTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsBattleSkillReleaseTupleScheme getScheme() {
      return new PsBattleSkillReleaseTupleScheme();
    }
  }

  private static class PsBattleSkillReleaseTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsBattleSkillRelease> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsBattleSkillRelease struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.skillMetaId);
      oprot.writeI32(struct.skillLevel);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetHeroSourceId()) {
        optionals.set(0);
      }
      if (struct.isSetHeroMetaId()) {
        optionals.set(1);
      }
      if (struct.isSetEffectMetaId()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetHeroSourceId()) {
        oprot.writeString(struct.heroSourceId);
      }
      if (struct.isSetHeroMetaId()) {
        oprot.writeString(struct.heroMetaId);
      }
      if (struct.isSetEffectMetaId()) {
        oprot.writeString(struct.effectMetaId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsBattleSkillRelease struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.skillMetaId = iprot.readString();
      struct.setSkillMetaIdIsSet(true);
      struct.skillLevel = iprot.readI32();
      struct.setSkillLevelIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.heroSourceId = iprot.readString();
        struct.setHeroSourceIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.heroMetaId = iprot.readString();
        struct.setHeroMetaIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.effectMetaId = iprot.readString();
        struct.setEffectMetaIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

