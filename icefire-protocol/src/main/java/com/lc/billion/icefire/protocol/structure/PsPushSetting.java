/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 订阅推送配置信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsPushSetting implements org.apache.thrift.TBase<PsPushSetting, PsPushSetting._Fields>, java.io.Serializable, Cloneable, Comparable<PsPushSetting> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsPushSetting");

  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField PUSH_SWITCH_FIELD_DESC = new org.apache.thrift.protocol.TField("pushSwitch", org.apache.thrift.protocol.TType.BOOL, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsPushSettingStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsPushSettingTupleSchemeFactory();

  /**
   * 属性
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsPushType
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsPushType type; // required
  /**
   * 推送开关
   */
  public boolean pushSwitch; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 属性
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsPushType
     */
    TYPE((short)1, "type"),
    /**
     * 推送开关
     */
    PUSH_SWITCH((short)2, "pushSwitch");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TYPE
          return TYPE;
        case 2: // PUSH_SWITCH
          return PUSH_SWITCH;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PUSHSWITCH_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsPushType.class)));
    tmpMap.put(_Fields.PUSH_SWITCH, new org.apache.thrift.meta_data.FieldMetaData("pushSwitch", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsPushSetting.class, metaDataMap);
  }

  public PsPushSetting() {
  }

  public PsPushSetting(
    com.lc.billion.icefire.protocol.constant.PsPushType type,
    boolean pushSwitch)
  {
    this();
    this.type = type;
    this.pushSwitch = pushSwitch;
    setPushSwitchIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsPushSetting(PsPushSetting other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetType()) {
      this.type = other.type;
    }
    this.pushSwitch = other.pushSwitch;
  }

  public PsPushSetting deepCopy() {
    return new PsPushSetting(this);
  }

  @Override
  public void clear() {
    this.type = null;
    setPushSwitchIsSet(false);
    this.pushSwitch = false;
  }

  /**
   * 属性
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsPushType
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsPushType getType() {
    return this.type;
  }

  /**
   * 属性
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsPushType
   */
  public PsPushSetting setType(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsPushType type) {
    this.type = type;
    return this;
  }

  public void unsetType() {
    this.type = null;
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return this.type != null;
  }

  public void setTypeIsSet(boolean value) {
    if (!value) {
      this.type = null;
    }
  }

  /**
   * 推送开关
   */
  public boolean isPushSwitch() {
    return this.pushSwitch;
  }

  /**
   * 推送开关
   */
  public PsPushSetting setPushSwitch(boolean pushSwitch) {
    this.pushSwitch = pushSwitch;
    setPushSwitchIsSet(true);
    return this;
  }

  public void unsetPushSwitch() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PUSHSWITCH_ISSET_ID);
  }

  /** Returns true if field pushSwitch is set (has been assigned a value) and false otherwise */
  public boolean isSetPushSwitch() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PUSHSWITCH_ISSET_ID);
  }

  public void setPushSwitchIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PUSHSWITCH_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((com.lc.billion.icefire.protocol.constant.PsPushType)value);
      }
      break;

    case PUSH_SWITCH:
      if (value == null) {
        unsetPushSwitch();
      } else {
        setPushSwitch((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case TYPE:
      return getType();

    case PUSH_SWITCH:
      return isPushSwitch();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case TYPE:
      return isSetType();
    case PUSH_SWITCH:
      return isSetPushSwitch();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsPushSetting)
      return this.equals((PsPushSetting)that);
    return false;
  }

  public boolean equals(PsPushSetting that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_type = true && this.isSetType();
    boolean that_present_type = true && that.isSetType();
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (!this.type.equals(that.type))
        return false;
    }

    boolean this_present_pushSwitch = true;
    boolean that_present_pushSwitch = true;
    if (this_present_pushSwitch || that_present_pushSwitch) {
      if (!(this_present_pushSwitch && that_present_pushSwitch))
        return false;
      if (this.pushSwitch != that.pushSwitch)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetType()) ? 131071 : 524287);
    if (isSetType())
      hashCode = hashCode * 8191 + type.getValue();

    hashCode = hashCode * 8191 + ((pushSwitch) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(PsPushSetting other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetType(), other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPushSwitch(), other.isSetPushSwitch());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPushSwitch()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pushSwitch, other.pushSwitch);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsPushSetting(");
    boolean first = true;

    sb.append("type:");
    if (this.type == null) {
      sb.append("null");
    } else {
      sb.append(this.type);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pushSwitch:");
    sb.append(this.pushSwitch);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (type == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'type' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'pushSwitch' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsPushSettingStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsPushSettingStandardScheme getScheme() {
      return new PsPushSettingStandardScheme();
    }
  }

  private static class PsPushSettingStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsPushSetting> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsPushSetting struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.type = com.lc.billion.icefire.protocol.constant.PsPushType.findByValue(iprot.readI32());
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PUSH_SWITCH
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.pushSwitch = iprot.readBool();
              struct.setPushSwitchIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetPushSwitch()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pushSwitch' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsPushSetting struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.type != null) {
        oprot.writeFieldBegin(TYPE_FIELD_DESC);
        oprot.writeI32(struct.type.getValue());
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PUSH_SWITCH_FIELD_DESC);
      oprot.writeBool(struct.pushSwitch);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsPushSettingTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsPushSettingTupleScheme getScheme() {
      return new PsPushSettingTupleScheme();
    }
  }

  private static class PsPushSettingTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsPushSetting> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsPushSetting struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.type.getValue());
      oprot.writeBool(struct.pushSwitch);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsPushSetting struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.type = com.lc.billion.icefire.protocol.constant.PsPushType.findByValue(iprot.readI32());
      struct.setTypeIsSet(true);
      struct.pushSwitch = iprot.readBool();
      struct.setPushSwitchIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

