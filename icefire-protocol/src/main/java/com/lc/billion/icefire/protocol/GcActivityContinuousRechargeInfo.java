/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcActivityContinuousRechargeInfo implements org.apache.thrift.TBase<GcActivityContinuousRechargeInfo, GcActivityContinuousRechargeInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcActivityContinuousRechargeInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcActivityContinuousRechargeInfo");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField CHARGE_DIAMOND_FIELD_DESC = new org.apache.thrift.protocol.TField("chargeDiamond", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField GOAL_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("goalInfos", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField METAS_FIELD_DESC = new org.apache.thrift.protocol.TField("metas", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField CURRENT_DAY_FIELD_DESC = new org.apache.thrift.protocol.TField("currentDay", org.apache.thrift.protocol.TType.I32, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcActivityContinuousRechargeInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcActivityContinuousRechargeInfoTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // required
  /**
   * 当前充值金额*
   */
  public int chargeDiamond; // required
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos; // required
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> metas; // required
  /**
   * 当前正在进行的天数*
   */
  public int currentDay; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACTIVITY_META_ID((short)1, "activityMetaId"),
    /**
     * 当前充值金额*
     */
    CHARGE_DIAMOND((short)2, "chargeDiamond"),
    GOAL_INFOS((short)3, "goalInfos"),
    METAS((short)4, "metas"),
    /**
     * 当前正在进行的天数*
     */
    CURRENT_DAY((short)5, "currentDay");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        case 2: // CHARGE_DIAMOND
          return CHARGE_DIAMOND;
        case 3: // GOAL_INFOS
          return GOAL_INFOS;
        case 4: // METAS
          return METAS;
        case 5: // CURRENT_DAY
          return CURRENT_DAY;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CHARGEDIAMOND_ISSET_ID = 0;
  private static final int __CURRENTDAY_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CHARGE_DIAMOND, new org.apache.thrift.meta_data.FieldMetaData("chargeDiamond", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GOAL_INFOS, new org.apache.thrift.meta_data.FieldMetaData("goalInfos", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo.class))));
    tmpMap.put(_Fields.METAS, new org.apache.thrift.meta_data.FieldMetaData("metas", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo.class))));
    tmpMap.put(_Fields.CURRENT_DAY, new org.apache.thrift.meta_data.FieldMetaData("currentDay", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcActivityContinuousRechargeInfo.class, metaDataMap);
  }

  public GcActivityContinuousRechargeInfo() {
  }

  public GcActivityContinuousRechargeInfo(
    java.lang.String activityMetaId,
    int chargeDiamond,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> metas,
    int currentDay)
  {
    this();
    this.activityMetaId = activityMetaId;
    this.chargeDiamond = chargeDiamond;
    setChargeDiamondIsSet(true);
    this.goalInfos = goalInfos;
    this.metas = metas;
    this.currentDay = currentDay;
    setCurrentDayIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcActivityContinuousRechargeInfo(GcActivityContinuousRechargeInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
    this.chargeDiamond = other.chargeDiamond;
    if (other.isSetGoalInfos()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> __this__goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(other.goalInfos.size());
      for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo other_element : other.goalInfos) {
        __this__goalInfos.add(new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo(other_element));
      }
      this.goalInfos = __this__goalInfos;
    }
    if (other.isSetMetas()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> __this__metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo>(other.metas.size());
      for (com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo other_element : other.metas) {
        __this__metas.add(new com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo(other_element));
      }
      this.metas = __this__metas;
    }
    this.currentDay = other.currentDay;
  }

  public GcActivityContinuousRechargeInfo deepCopy() {
    return new GcActivityContinuousRechargeInfo(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
    setChargeDiamondIsSet(false);
    this.chargeDiamond = 0;
    this.goalInfos = null;
    this.metas = null;
    setCurrentDayIsSet(false);
    this.currentDay = 0;
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  public GcActivityContinuousRechargeInfo setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  /**
   * 当前充值金额*
   */
  public int getChargeDiamond() {
    return this.chargeDiamond;
  }

  /**
   * 当前充值金额*
   */
  public GcActivityContinuousRechargeInfo setChargeDiamond(int chargeDiamond) {
    this.chargeDiamond = chargeDiamond;
    setChargeDiamondIsSet(true);
    return this;
  }

  public void unsetChargeDiamond() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CHARGEDIAMOND_ISSET_ID);
  }

  /** Returns true if field chargeDiamond is set (has been assigned a value) and false otherwise */
  public boolean isSetChargeDiamond() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CHARGEDIAMOND_ISSET_ID);
  }

  public void setChargeDiamondIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CHARGEDIAMOND_ISSET_ID, value);
  }

  public int getGoalInfosSize() {
    return (this.goalInfos == null) ? 0 : this.goalInfos.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> getGoalInfosIterator() {
    return (this.goalInfos == null) ? null : this.goalInfos.iterator();
  }

  public void addToGoalInfos(com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo elem) {
    if (this.goalInfos == null) {
      this.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>();
    }
    this.goalInfos.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> getGoalInfos() {
    return this.goalInfos;
  }

  public GcActivityContinuousRechargeInfo setGoalInfos(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos) {
    this.goalInfos = goalInfos;
    return this;
  }

  public void unsetGoalInfos() {
    this.goalInfos = null;
  }

  /** Returns true if field goalInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetGoalInfos() {
    return this.goalInfos != null;
  }

  public void setGoalInfosIsSet(boolean value) {
    if (!value) {
      this.goalInfos = null;
    }
  }

  public int getMetasSize() {
    return (this.metas == null) ? 0 : this.metas.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> getMetasIterator() {
    return (this.metas == null) ? null : this.metas.iterator();
  }

  public void addToMetas(com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo elem) {
    if (this.metas == null) {
      this.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo>();
    }
    this.metas.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> getMetas() {
    return this.metas;
  }

  public GcActivityContinuousRechargeInfo setMetas(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo> metas) {
    this.metas = metas;
    return this;
  }

  public void unsetMetas() {
    this.metas = null;
  }

  /** Returns true if field metas is set (has been assigned a value) and false otherwise */
  public boolean isSetMetas() {
    return this.metas != null;
  }

  public void setMetasIsSet(boolean value) {
    if (!value) {
      this.metas = null;
    }
  }

  /**
   * 当前正在进行的天数*
   */
  public int getCurrentDay() {
    return this.currentDay;
  }

  /**
   * 当前正在进行的天数*
   */
  public GcActivityContinuousRechargeInfo setCurrentDay(int currentDay) {
    this.currentDay = currentDay;
    setCurrentDayIsSet(true);
    return this;
  }

  public void unsetCurrentDay() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURRENTDAY_ISSET_ID);
  }

  /** Returns true if field currentDay is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrentDay() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURRENTDAY_ISSET_ID);
  }

  public void setCurrentDayIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURRENTDAY_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    case CHARGE_DIAMOND:
      if (value == null) {
        unsetChargeDiamond();
      } else {
        setChargeDiamond((java.lang.Integer)value);
      }
      break;

    case GOAL_INFOS:
      if (value == null) {
        unsetGoalInfos();
      } else {
        setGoalInfos((java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>)value);
      }
      break;

    case METAS:
      if (value == null) {
        unsetMetas();
      } else {
        setMetas((java.util.List<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo>)value);
      }
      break;

    case CURRENT_DAY:
      if (value == null) {
        unsetCurrentDay();
      } else {
        setCurrentDay((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    case CHARGE_DIAMOND:
      return getChargeDiamond();

    case GOAL_INFOS:
      return getGoalInfos();

    case METAS:
      return getMetas();

    case CURRENT_DAY:
      return getCurrentDay();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    case CHARGE_DIAMOND:
      return isSetChargeDiamond();
    case GOAL_INFOS:
      return isSetGoalInfos();
    case METAS:
      return isSetMetas();
    case CURRENT_DAY:
      return isSetCurrentDay();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcActivityContinuousRechargeInfo)
      return this.equals((GcActivityContinuousRechargeInfo)that);
    return false;
  }

  public boolean equals(GcActivityContinuousRechargeInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    boolean this_present_chargeDiamond = true;
    boolean that_present_chargeDiamond = true;
    if (this_present_chargeDiamond || that_present_chargeDiamond) {
      if (!(this_present_chargeDiamond && that_present_chargeDiamond))
        return false;
      if (this.chargeDiamond != that.chargeDiamond)
        return false;
    }

    boolean this_present_goalInfos = true && this.isSetGoalInfos();
    boolean that_present_goalInfos = true && that.isSetGoalInfos();
    if (this_present_goalInfos || that_present_goalInfos) {
      if (!(this_present_goalInfos && that_present_goalInfos))
        return false;
      if (!this.goalInfos.equals(that.goalInfos))
        return false;
    }

    boolean this_present_metas = true && this.isSetMetas();
    boolean that_present_metas = true && that.isSetMetas();
    if (this_present_metas || that_present_metas) {
      if (!(this_present_metas && that_present_metas))
        return false;
      if (!this.metas.equals(that.metas))
        return false;
    }

    boolean this_present_currentDay = true;
    boolean that_present_currentDay = true;
    if (this_present_currentDay || that_present_currentDay) {
      if (!(this_present_currentDay && that_present_currentDay))
        return false;
      if (this.currentDay != that.currentDay)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    hashCode = hashCode * 8191 + chargeDiamond;

    hashCode = hashCode * 8191 + ((isSetGoalInfos()) ? 131071 : 524287);
    if (isSetGoalInfos())
      hashCode = hashCode * 8191 + goalInfos.hashCode();

    hashCode = hashCode * 8191 + ((isSetMetas()) ? 131071 : 524287);
    if (isSetMetas())
      hashCode = hashCode * 8191 + metas.hashCode();

    hashCode = hashCode * 8191 + currentDay;

    return hashCode;
  }

  @Override
  public int compareTo(GcActivityContinuousRechargeInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetChargeDiamond(), other.isSetChargeDiamond());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChargeDiamond()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.chargeDiamond, other.chargeDiamond);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetGoalInfos(), other.isSetGoalInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGoalInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goalInfos, other.goalInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMetas(), other.isSetMetas());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetas()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metas, other.metas);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrentDay(), other.isSetCurrentDay());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrentDay()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currentDay, other.currentDay);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcActivityContinuousRechargeInfo(");
    boolean first = true;

    sb.append("activityMetaId:");
    if (this.activityMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.activityMetaId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("chargeDiamond:");
    sb.append(this.chargeDiamond);
    first = false;
    if (!first) sb.append(", ");
    sb.append("goalInfos:");
    if (this.goalInfos == null) {
      sb.append("null");
    } else {
      sb.append(this.goalInfos);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("metas:");
    if (this.metas == null) {
      sb.append("null");
    } else {
      sb.append(this.metas);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("currentDay:");
    sb.append(this.currentDay);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (activityMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'activityMetaId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'chargeDiamond' because it's a primitive and you chose the non-beans generator.
    if (goalInfos == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'goalInfos' was not present! Struct: " + toString());
    }
    if (metas == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'metas' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'currentDay' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcActivityContinuousRechargeInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityContinuousRechargeInfoStandardScheme getScheme() {
      return new GcActivityContinuousRechargeInfoStandardScheme();
    }
  }

  private static class GcActivityContinuousRechargeInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcActivityContinuousRechargeInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcActivityContinuousRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CHARGE_DIAMOND
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.chargeDiamond = iprot.readI32();
              struct.setChargeDiamondIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // GOAL_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo();
                  _elem1.read(iprot);
                  struct.goalInfos.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setGoalInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // METAS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo();
                  _elem4.read(iprot);
                  struct.metas.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setMetasIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CURRENT_DAY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.currentDay = iprot.readI32();
              struct.setCurrentDayIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetChargeDiamond()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'chargeDiamond' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetCurrentDay()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'currentDay' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcActivityContinuousRechargeInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
        oprot.writeString(struct.activityMetaId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CHARGE_DIAMOND_FIELD_DESC);
      oprot.writeI32(struct.chargeDiamond);
      oprot.writeFieldEnd();
      if (struct.goalInfos != null) {
        oprot.writeFieldBegin(GOAL_INFOS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.goalInfos.size()));
          for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _iter6 : struct.goalInfos)
          {
            _iter6.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.metas != null) {
        oprot.writeFieldBegin(METAS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.metas.size()));
          for (com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo _iter7 : struct.metas)
          {
            _iter7.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CURRENT_DAY_FIELD_DESC);
      oprot.writeI32(struct.currentDay);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcActivityContinuousRechargeInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivityContinuousRechargeInfoTupleScheme getScheme() {
      return new GcActivityContinuousRechargeInfoTupleScheme();
    }
  }

  private static class GcActivityContinuousRechargeInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcActivityContinuousRechargeInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcActivityContinuousRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.activityMetaId);
      oprot.writeI32(struct.chargeDiamond);
      {
        oprot.writeI32(struct.goalInfos.size());
        for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _iter8 : struct.goalInfos)
        {
          _iter8.write(oprot);
        }
      }
      {
        oprot.writeI32(struct.metas.size());
        for (com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo _iter9 : struct.metas)
        {
          _iter9.write(oprot);
        }
      }
      oprot.writeI32(struct.currentDay);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcActivityContinuousRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.activityMetaId = iprot.readString();
      struct.setActivityMetaIdIsSet(true);
      struct.chargeDiamond = iprot.readI32();
      struct.setChargeDiamondIsSet(true);
      {
        org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(_list10.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _elem11;
        for (int _i12 = 0; _i12 < _list10.size; ++_i12)
        {
          _elem11 = new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo();
          _elem11.read(iprot);
          struct.goalInfos.add(_elem11);
        }
      }
      struct.setGoalInfosIsSet(true);
      {
        org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo>(_list13.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo _elem14;
        for (int _i15 = 0; _i15 < _list13.size; ++_i15)
        {
          _elem14 = new com.lc.billion.icefire.protocol.structure.PsContinuousRechargeMetaInfo();
          _elem14.read(iprot);
          struct.metas.add(_elem14);
        }
      }
      struct.setMetasIsSet(true);
      struct.currentDay = iprot.readI32();
      struct.setCurrentDayIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

