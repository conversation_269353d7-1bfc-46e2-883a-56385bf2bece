/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 红包-红包列表(全量、登录或换盟下发)
 * @Message(7686)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcRedPackList implements org.apache.thrift.TBase<GcRedPackList, GcRedPackList._Fields>, java.io.Serializable, Cloneable, Comparable<GcRedPackList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcRedPackList");

  private static final org.apache.thrift.protocol.TField RED_PACKS_FIELD_DESC = new org.apache.thrift.protocol.TField("redPacks", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField DAILY_TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("dailyTimes", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField MAX_TIMES_FIELD_DESC = new org.apache.thrift.protocol.TField("maxTimes", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcRedPackListStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcRedPackListTupleSchemeFactory();

  /**
   * 现有可见的红包列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsRedPackPersonal> redPacks; // optional
  /**
   * 每日领取次数
   */
  public int dailyTimes; // optional
  /**
   * 每日最大领取次数
   */
  public int maxTimes; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 现有可见的红包列表
     */
    RED_PACKS((short)1, "redPacks"),
    /**
     * 每日领取次数
     */
    DAILY_TIMES((short)2, "dailyTimes"),
    /**
     * 每日最大领取次数
     */
    MAX_TIMES((short)3, "maxTimes");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RED_PACKS
          return RED_PACKS;
        case 2: // DAILY_TIMES
          return DAILY_TIMES;
        case 3: // MAX_TIMES
          return MAX_TIMES;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __DAILYTIMES_ISSET_ID = 0;
  private static final int __MAXTIMES_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.RED_PACKS,_Fields.DAILY_TIMES,_Fields.MAX_TIMES};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RED_PACKS, new org.apache.thrift.meta_data.FieldMetaData("redPacks", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "PsRedPackPersonal"))));
    tmpMap.put(_Fields.DAILY_TIMES, new org.apache.thrift.meta_data.FieldMetaData("dailyTimes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MAX_TIMES, new org.apache.thrift.meta_data.FieldMetaData("maxTimes", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcRedPackList.class, metaDataMap);
  }

  public GcRedPackList() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcRedPackList(GcRedPackList other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetRedPacks()) {
      java.util.List<PsRedPackPersonal> __this__redPacks = new java.util.ArrayList<PsRedPackPersonal>(other.redPacks.size());
      for (PsRedPackPersonal other_element : other.redPacks) {
        __this__redPacks.add(new PsRedPackPersonal(other_element));
      }
      this.redPacks = __this__redPacks;
    }
    this.dailyTimes = other.dailyTimes;
    this.maxTimes = other.maxTimes;
  }

  public GcRedPackList deepCopy() {
    return new GcRedPackList(this);
  }

  @Override
  public void clear() {
    this.redPacks = null;
    setDailyTimesIsSet(false);
    this.dailyTimes = 0;
    setMaxTimesIsSet(false);
    this.maxTimes = 0;
  }

  public int getRedPacksSize() {
    return (this.redPacks == null) ? 0 : this.redPacks.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsRedPackPersonal> getRedPacksIterator() {
    return (this.redPacks == null) ? null : this.redPacks.iterator();
  }

  public void addToRedPacks(PsRedPackPersonal elem) {
    if (this.redPacks == null) {
      this.redPacks = new java.util.ArrayList<PsRedPackPersonal>();
    }
    this.redPacks.add(elem);
  }

  /**
   * 现有可见的红包列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsRedPackPersonal> getRedPacks() {
    return this.redPacks;
  }

  /**
   * 现有可见的红包列表
   */
  public GcRedPackList setRedPacks(@org.apache.thrift.annotation.Nullable java.util.List<PsRedPackPersonal> redPacks) {
    this.redPacks = redPacks;
    return this;
  }

  public void unsetRedPacks() {
    this.redPacks = null;
  }

  /** Returns true if field redPacks is set (has been assigned a value) and false otherwise */
  public boolean isSetRedPacks() {
    return this.redPacks != null;
  }

  public void setRedPacksIsSet(boolean value) {
    if (!value) {
      this.redPacks = null;
    }
  }

  /**
   * 每日领取次数
   */
  public int getDailyTimes() {
    return this.dailyTimes;
  }

  /**
   * 每日领取次数
   */
  public GcRedPackList setDailyTimes(int dailyTimes) {
    this.dailyTimes = dailyTimes;
    setDailyTimesIsSet(true);
    return this;
  }

  public void unsetDailyTimes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DAILYTIMES_ISSET_ID);
  }

  /** Returns true if field dailyTimes is set (has been assigned a value) and false otherwise */
  public boolean isSetDailyTimes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DAILYTIMES_ISSET_ID);
  }

  public void setDailyTimesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DAILYTIMES_ISSET_ID, value);
  }

  /**
   * 每日最大领取次数
   */
  public int getMaxTimes() {
    return this.maxTimes;
  }

  /**
   * 每日最大领取次数
   */
  public GcRedPackList setMaxTimes(int maxTimes) {
    this.maxTimes = maxTimes;
    setMaxTimesIsSet(true);
    return this;
  }

  public void unsetMaxTimes() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MAXTIMES_ISSET_ID);
  }

  /** Returns true if field maxTimes is set (has been assigned a value) and false otherwise */
  public boolean isSetMaxTimes() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MAXTIMES_ISSET_ID);
  }

  public void setMaxTimesIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MAXTIMES_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case RED_PACKS:
      if (value == null) {
        unsetRedPacks();
      } else {
        setRedPacks((java.util.List<PsRedPackPersonal>)value);
      }
      break;

    case DAILY_TIMES:
      if (value == null) {
        unsetDailyTimes();
      } else {
        setDailyTimes((java.lang.Integer)value);
      }
      break;

    case MAX_TIMES:
      if (value == null) {
        unsetMaxTimes();
      } else {
        setMaxTimes((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case RED_PACKS:
      return getRedPacks();

    case DAILY_TIMES:
      return getDailyTimes();

    case MAX_TIMES:
      return getMaxTimes();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case RED_PACKS:
      return isSetRedPacks();
    case DAILY_TIMES:
      return isSetDailyTimes();
    case MAX_TIMES:
      return isSetMaxTimes();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcRedPackList)
      return this.equals((GcRedPackList)that);
    return false;
  }

  public boolean equals(GcRedPackList that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_redPacks = true && this.isSetRedPacks();
    boolean that_present_redPacks = true && that.isSetRedPacks();
    if (this_present_redPacks || that_present_redPacks) {
      if (!(this_present_redPacks && that_present_redPacks))
        return false;
      if (!this.redPacks.equals(that.redPacks))
        return false;
    }

    boolean this_present_dailyTimes = true && this.isSetDailyTimes();
    boolean that_present_dailyTimes = true && that.isSetDailyTimes();
    if (this_present_dailyTimes || that_present_dailyTimes) {
      if (!(this_present_dailyTimes && that_present_dailyTimes))
        return false;
      if (this.dailyTimes != that.dailyTimes)
        return false;
    }

    boolean this_present_maxTimes = true && this.isSetMaxTimes();
    boolean that_present_maxTimes = true && that.isSetMaxTimes();
    if (this_present_maxTimes || that_present_maxTimes) {
      if (!(this_present_maxTimes && that_present_maxTimes))
        return false;
      if (this.maxTimes != that.maxTimes)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRedPacks()) ? 131071 : 524287);
    if (isSetRedPacks())
      hashCode = hashCode * 8191 + redPacks.hashCode();

    hashCode = hashCode * 8191 + ((isSetDailyTimes()) ? 131071 : 524287);
    if (isSetDailyTimes())
      hashCode = hashCode * 8191 + dailyTimes;

    hashCode = hashCode * 8191 + ((isSetMaxTimes()) ? 131071 : 524287);
    if (isSetMaxTimes())
      hashCode = hashCode * 8191 + maxTimes;

    return hashCode;
  }

  @Override
  public int compareTo(GcRedPackList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRedPacks(), other.isSetRedPacks());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRedPacks()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.redPacks, other.redPacks);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDailyTimes(), other.isSetDailyTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDailyTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dailyTimes, other.dailyTimes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMaxTimes(), other.isSetMaxTimes());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMaxTimes()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.maxTimes, other.maxTimes);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcRedPackList(");
    boolean first = true;

    if (isSetRedPacks()) {
      sb.append("redPacks:");
      if (this.redPacks == null) {
        sb.append("null");
      } else {
        sb.append(this.redPacks);
      }
      first = false;
    }
    if (isSetDailyTimes()) {
      if (!first) sb.append(", ");
      sb.append("dailyTimes:");
      sb.append(this.dailyTimes);
      first = false;
    }
    if (isSetMaxTimes()) {
      if (!first) sb.append(", ");
      sb.append("maxTimes:");
      sb.append(this.maxTimes);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcRedPackListStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRedPackListStandardScheme getScheme() {
      return new GcRedPackListStandardScheme();
    }
  }

  private static class GcRedPackListStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcRedPackList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcRedPackList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RED_PACKS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.redPacks = new java.util.ArrayList<PsRedPackPersonal>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsRedPackPersonal _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsRedPackPersonal();
                  _elem1.read(iprot);
                  struct.redPacks.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRedPacksIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // DAILY_TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.dailyTimes = iprot.readI32();
              struct.setDailyTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // MAX_TIMES
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.maxTimes = iprot.readI32();
              struct.setMaxTimesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcRedPackList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.redPacks != null) {
        if (struct.isSetRedPacks()) {
          oprot.writeFieldBegin(RED_PACKS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.redPacks.size()));
            for (PsRedPackPersonal _iter3 : struct.redPacks)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetDailyTimes()) {
        oprot.writeFieldBegin(DAILY_TIMES_FIELD_DESC);
        oprot.writeI32(struct.dailyTimes);
        oprot.writeFieldEnd();
      }
      if (struct.isSetMaxTimes()) {
        oprot.writeFieldBegin(MAX_TIMES_FIELD_DESC);
        oprot.writeI32(struct.maxTimes);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcRedPackListTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRedPackListTupleScheme getScheme() {
      return new GcRedPackListTupleScheme();
    }
  }

  private static class GcRedPackListTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcRedPackList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcRedPackList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRedPacks()) {
        optionals.set(0);
      }
      if (struct.isSetDailyTimes()) {
        optionals.set(1);
      }
      if (struct.isSetMaxTimes()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetRedPacks()) {
        {
          oprot.writeI32(struct.redPacks.size());
          for (PsRedPackPersonal _iter4 : struct.redPacks)
          {
            _iter4.write(oprot);
          }
        }
      }
      if (struct.isSetDailyTimes()) {
        oprot.writeI32(struct.dailyTimes);
      }
      if (struct.isSetMaxTimes()) {
        oprot.writeI32(struct.maxTimes);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcRedPackList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.redPacks = new java.util.ArrayList<PsRedPackPersonal>(_list5.size);
          @org.apache.thrift.annotation.Nullable PsRedPackPersonal _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new PsRedPackPersonal();
            _elem6.read(iprot);
            struct.redPacks.add(_elem6);
          }
        }
        struct.setRedPacksIsSet(true);
      }
      if (incoming.get(1)) {
        struct.dailyTimes = iprot.readI32();
        struct.setDailyTimesIsSet(true);
      }
      if (incoming.get(2)) {
        struct.maxTimes = iprot.readI32();
        struct.setMaxTimesIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

