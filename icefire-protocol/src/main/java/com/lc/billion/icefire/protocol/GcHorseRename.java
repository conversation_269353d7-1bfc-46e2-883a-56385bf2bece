/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 坐骑改名返回
 * @Message(7488)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcHorseRename implements org.apache.thrift.TBase<GcHorseRename, GcHorseRename._Fields>, java.io.Serializable, Cloneable, Comparable<GcHorseRename> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcHorseRename");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField HORSE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("horseId", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField LAST_RENAME_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("lastRenameTime", org.apache.thrift.protocol.TType.I64, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcHorseRenameStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcHorseRenameTupleSchemeFactory();

  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode code; // optional
  /**
   * 改后的名字
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String name; // optional
  /**
   * 坐骑id
   */
  public int horseId; // optional
  /**
   * 上次改名时间戳
   */
  public long lastRenameTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 错误码
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
     */
    CODE((short)1, "code"),
    /**
     * 改后的名字
     */
    NAME((short)2, "name"),
    /**
     * 坐骑id
     */
    HORSE_ID((short)3, "horseId"),
    /**
     * 上次改名时间戳
     */
    LAST_RENAME_TIME((short)4, "lastRenameTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // NAME
          return NAME;
        case 3: // HORSE_ID
          return HORSE_ID;
        case 4: // LAST_RENAME_TIME
          return LAST_RENAME_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __HORSEID_ISSET_ID = 0;
  private static final int __LASTRENAMETIME_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CODE,_Fields.NAME,_Fields.HORSE_ID,_Fields.LAST_RENAME_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsErrorCode.class)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.HORSE_ID, new org.apache.thrift.meta_data.FieldMetaData("horseId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.LAST_RENAME_TIME, new org.apache.thrift.meta_data.FieldMetaData("lastRenameTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcHorseRename.class, metaDataMap);
  }

  public GcHorseRename() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcHorseRename(GcHorseRename other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetCode()) {
      this.code = other.code;
    }
    if (other.isSetName()) {
      this.name = other.name;
    }
    this.horseId = other.horseId;
    this.lastRenameTime = other.lastRenameTime;
  }

  public GcHorseRename deepCopy() {
    return new GcHorseRename(this);
  }

  @Override
  public void clear() {
    this.code = null;
    this.name = null;
    setHorseIdIsSet(false);
    this.horseId = 0;
    setLastRenameTimeIsSet(false);
    this.lastRenameTime = 0;
  }

  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsErrorCode getCode() {
    return this.code;
  }

  /**
   * 错误码
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsErrorCode
   */
  public GcHorseRename setCode(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsErrorCode code) {
    this.code = code;
    return this;
  }

  public void unsetCode() {
    this.code = null;
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return this.code != null;
  }

  public void setCodeIsSet(boolean value) {
    if (!value) {
      this.code = null;
    }
  }

  /**
   * 改后的名字
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getName() {
    return this.name;
  }

  /**
   * 改后的名字
   */
  public GcHorseRename setName(@org.apache.thrift.annotation.Nullable java.lang.String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  /**
   * 坐骑id
   */
  public int getHorseId() {
    return this.horseId;
  }

  /**
   * 坐骑id
   */
  public GcHorseRename setHorseId(int horseId) {
    this.horseId = horseId;
    setHorseIdIsSet(true);
    return this;
  }

  public void unsetHorseId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __HORSEID_ISSET_ID);
  }

  /** Returns true if field horseId is set (has been assigned a value) and false otherwise */
  public boolean isSetHorseId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __HORSEID_ISSET_ID);
  }

  public void setHorseIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __HORSEID_ISSET_ID, value);
  }

  /**
   * 上次改名时间戳
   */
  public long getLastRenameTime() {
    return this.lastRenameTime;
  }

  /**
   * 上次改名时间戳
   */
  public GcHorseRename setLastRenameTime(long lastRenameTime) {
    this.lastRenameTime = lastRenameTime;
    setLastRenameTimeIsSet(true);
    return this;
  }

  public void unsetLastRenameTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LASTRENAMETIME_ISSET_ID);
  }

  /** Returns true if field lastRenameTime is set (has been assigned a value) and false otherwise */
  public boolean isSetLastRenameTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LASTRENAMETIME_ISSET_ID);
  }

  public void setLastRenameTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LASTRENAMETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((com.lc.billion.icefire.protocol.constant.PsErrorCode)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((java.lang.String)value);
      }
      break;

    case HORSE_ID:
      if (value == null) {
        unsetHorseId();
      } else {
        setHorseId((java.lang.Integer)value);
      }
      break;

    case LAST_RENAME_TIME:
      if (value == null) {
        unsetLastRenameTime();
      } else {
        setLastRenameTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case NAME:
      return getName();

    case HORSE_ID:
      return getHorseId();

    case LAST_RENAME_TIME:
      return getLastRenameTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case NAME:
      return isSetName();
    case HORSE_ID:
      return isSetHorseId();
    case LAST_RENAME_TIME:
      return isSetLastRenameTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcHorseRename)
      return this.equals((GcHorseRename)that);
    return false;
  }

  public boolean equals(GcHorseRename that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_code = true && this.isSetCode();
    boolean that_present_code = true && that.isSetCode();
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (!this.code.equals(that.code))
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_horseId = true && this.isSetHorseId();
    boolean that_present_horseId = true && that.isSetHorseId();
    if (this_present_horseId || that_present_horseId) {
      if (!(this_present_horseId && that_present_horseId))
        return false;
      if (this.horseId != that.horseId)
        return false;
    }

    boolean this_present_lastRenameTime = true && this.isSetLastRenameTime();
    boolean that_present_lastRenameTime = true && that.isSetLastRenameTime();
    if (this_present_lastRenameTime || that_present_lastRenameTime) {
      if (!(this_present_lastRenameTime && that_present_lastRenameTime))
        return false;
      if (this.lastRenameTime != that.lastRenameTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetCode()) ? 131071 : 524287);
    if (isSetCode())
      hashCode = hashCode * 8191 + code.getValue();

    hashCode = hashCode * 8191 + ((isSetName()) ? 131071 : 524287);
    if (isSetName())
      hashCode = hashCode * 8191 + name.hashCode();

    hashCode = hashCode * 8191 + ((isSetHorseId()) ? 131071 : 524287);
    if (isSetHorseId())
      hashCode = hashCode * 8191 + horseId;

    hashCode = hashCode * 8191 + ((isSetLastRenameTime()) ? 131071 : 524287);
    if (isSetLastRenameTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(lastRenameTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcHorseRename other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetCode(), other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetName(), other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHorseId(), other.isSetHorseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHorseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.horseId, other.horseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLastRenameTime(), other.isSetLastRenameTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLastRenameTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastRenameTime, other.lastRenameTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcHorseRename(");
    boolean first = true;

    if (isSetCode()) {
      sb.append("code:");
      if (this.code == null) {
        sb.append("null");
      } else {
        sb.append(this.code);
      }
      first = false;
    }
    if (isSetName()) {
      if (!first) sb.append(", ");
      sb.append("name:");
      if (this.name == null) {
        sb.append("null");
      } else {
        sb.append(this.name);
      }
      first = false;
    }
    if (isSetHorseId()) {
      if (!first) sb.append(", ");
      sb.append("horseId:");
      sb.append(this.horseId);
      first = false;
    }
    if (isSetLastRenameTime()) {
      if (!first) sb.append(", ");
      sb.append("lastRenameTime:");
      sb.append(this.lastRenameTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcHorseRenameStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHorseRenameStandardScheme getScheme() {
      return new GcHorseRenameStandardScheme();
    }
  }

  private static class GcHorseRenameStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcHorseRename> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcHorseRename struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // HORSE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.horseId = iprot.readI32();
              struct.setHorseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // LAST_RENAME_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.lastRenameTime = iprot.readI64();
              struct.setLastRenameTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcHorseRename struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.code != null) {
        if (struct.isSetCode()) {
          oprot.writeFieldBegin(CODE_FIELD_DESC);
          oprot.writeI32(struct.code.getValue());
          oprot.writeFieldEnd();
        }
      }
      if (struct.name != null) {
        if (struct.isSetName()) {
          oprot.writeFieldBegin(NAME_FIELD_DESC);
          oprot.writeString(struct.name);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetHorseId()) {
        oprot.writeFieldBegin(HORSE_ID_FIELD_DESC);
        oprot.writeI32(struct.horseId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetLastRenameTime()) {
        oprot.writeFieldBegin(LAST_RENAME_TIME_FIELD_DESC);
        oprot.writeI64(struct.lastRenameTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcHorseRenameTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcHorseRenameTupleScheme getScheme() {
      return new GcHorseRenameTupleScheme();
    }
  }

  private static class GcHorseRenameTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcHorseRename> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcHorseRename struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetName()) {
        optionals.set(1);
      }
      if (struct.isSetHorseId()) {
        optionals.set(2);
      }
      if (struct.isSetLastRenameTime()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code.getValue());
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetHorseId()) {
        oprot.writeI32(struct.horseId);
      }
      if (struct.isSetLastRenameTime()) {
        oprot.writeI64(struct.lastRenameTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcHorseRename struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.code = com.lc.billion.icefire.protocol.constant.PsErrorCode.findByValue(iprot.readI32());
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.horseId = iprot.readI32();
        struct.setHorseIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.lastRenameTime = iprot.readI64();
        struct.setLastRenameTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

