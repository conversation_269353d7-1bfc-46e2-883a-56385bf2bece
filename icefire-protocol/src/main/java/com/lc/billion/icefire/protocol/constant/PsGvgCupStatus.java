/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * *
 * GvG杯赛 各阶段定义
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsGvgCupStatus implements org.apache.thrift.TEnum {
  /**
   * 未开启
   */
  NOT_OPEN(0),
  /**
   * 预热阶段
   */
  PREHEAT(1),
  /**
   * 申请阶段
   */
  APPLY(2),
  /**
   * 小组赛入围名单展示阶段
   */
  SHORTLISTED_ANNOUNCE(3),
  /**
   * 小组赛比赛阶段
   */
  GROUP_MARCH(4),
  /**
   * 淘汰赛晋级名单展示阶段
   */
  PROMOTED_ANNOUNCE(5),
  /**
   * 淘汰赛比赛阶段
   */
  KNOCKOUT_MARCH(6),
  /**
   * 赛事结束
   */
  CLOSING(7);

  private final int value;

  private PsGvgCupStatus(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsGvgCupStatus findByValue(int value) { 
    switch (value) {
      case 0:
        return NOT_OPEN;
      case 1:
        return PREHEAT;
      case 2:
        return APPLY;
      case 3:
        return SHORTLISTED_ANNOUNCE;
      case 4:
        return GROUP_MARCH;
      case 5:
        return PROMOTED_ANNOUNCE;
      case 6:
        return KNOCKOUT_MARCH;
      case 7:
        return CLOSING;
      default:
        return null;
    }
  }
}
