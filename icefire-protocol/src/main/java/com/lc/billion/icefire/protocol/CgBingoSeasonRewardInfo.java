/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * bingo 季奖励展示数据
 * @Message(5969)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgBingoSeasonRewardInfo implements org.apache.thrift.TBase<CgBingoSeasonRewardInfo, CgBingoSeasonRewardInfo._Fields>, java.io.Serializable, Cloneable, Comparable<CgBingoSeasonRewardInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgBingoSeasonRewardInfo");

  private static final org.apache.thrift.protocol.TField BINGO_SEASON_FIELD_DESC = new org.apache.thrift.protocol.TField("bingoSeason", org.apache.thrift.protocol.TType.I32, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgBingoSeasonRewardInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgBingoSeasonRewardInfoTupleSchemeFactory();

  /**
   * 季数
   */
  public int bingoSeason; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 季数
     */
    BINGO_SEASON((short)1, "bingoSeason");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BINGO_SEASON
          return BINGO_SEASON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BINGOSEASON_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.BINGO_SEASON};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BINGO_SEASON, new org.apache.thrift.meta_data.FieldMetaData("bingoSeason", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgBingoSeasonRewardInfo.class, metaDataMap);
  }

  public CgBingoSeasonRewardInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgBingoSeasonRewardInfo(CgBingoSeasonRewardInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.bingoSeason = other.bingoSeason;
  }

  public CgBingoSeasonRewardInfo deepCopy() {
    return new CgBingoSeasonRewardInfo(this);
  }

  @Override
  public void clear() {
    setBingoSeasonIsSet(false);
    this.bingoSeason = 0;
  }

  /**
   * 季数
   */
  public int getBingoSeason() {
    return this.bingoSeason;
  }

  /**
   * 季数
   */
  public CgBingoSeasonRewardInfo setBingoSeason(int bingoSeason) {
    this.bingoSeason = bingoSeason;
    setBingoSeasonIsSet(true);
    return this;
  }

  public void unsetBingoSeason() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BINGOSEASON_ISSET_ID);
  }

  /** Returns true if field bingoSeason is set (has been assigned a value) and false otherwise */
  public boolean isSetBingoSeason() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BINGOSEASON_ISSET_ID);
  }

  public void setBingoSeasonIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BINGOSEASON_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case BINGO_SEASON:
      if (value == null) {
        unsetBingoSeason();
      } else {
        setBingoSeason((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case BINGO_SEASON:
      return getBingoSeason();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case BINGO_SEASON:
      return isSetBingoSeason();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgBingoSeasonRewardInfo)
      return this.equals((CgBingoSeasonRewardInfo)that);
    return false;
  }

  public boolean equals(CgBingoSeasonRewardInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_bingoSeason = true && this.isSetBingoSeason();
    boolean that_present_bingoSeason = true && that.isSetBingoSeason();
    if (this_present_bingoSeason || that_present_bingoSeason) {
      if (!(this_present_bingoSeason && that_present_bingoSeason))
        return false;
      if (this.bingoSeason != that.bingoSeason)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetBingoSeason()) ? 131071 : 524287);
    if (isSetBingoSeason())
      hashCode = hashCode * 8191 + bingoSeason;

    return hashCode;
  }

  @Override
  public int compareTo(CgBingoSeasonRewardInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetBingoSeason(), other.isSetBingoSeason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBingoSeason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.bingoSeason, other.bingoSeason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgBingoSeasonRewardInfo(");
    boolean first = true;

    if (isSetBingoSeason()) {
      sb.append("bingoSeason:");
      sb.append(this.bingoSeason);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgBingoSeasonRewardInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBingoSeasonRewardInfoStandardScheme getScheme() {
      return new CgBingoSeasonRewardInfoStandardScheme();
    }
  }

  private static class CgBingoSeasonRewardInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgBingoSeasonRewardInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgBingoSeasonRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BINGO_SEASON
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.bingoSeason = iprot.readI32();
              struct.setBingoSeasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgBingoSeasonRewardInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetBingoSeason()) {
        oprot.writeFieldBegin(BINGO_SEASON_FIELD_DESC);
        oprot.writeI32(struct.bingoSeason);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgBingoSeasonRewardInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBingoSeasonRewardInfoTupleScheme getScheme() {
      return new CgBingoSeasonRewardInfoTupleScheme();
    }
  }

  private static class CgBingoSeasonRewardInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgBingoSeasonRewardInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgBingoSeasonRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetBingoSeason()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetBingoSeason()) {
        oprot.writeI32(struct.bingoSeason);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgBingoSeasonRewardInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.bingoSeason = iprot.readI32();
        struct.setBingoSeasonIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

