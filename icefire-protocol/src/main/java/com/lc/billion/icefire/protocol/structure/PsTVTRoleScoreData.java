/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * TVT战斗结束：玩家积分数据对比
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsTVTRoleScoreData implements org.apache.thrift.TBase<PsTVTRoleScoreData, PsTVTRoleScoreData._Fields>, java.io.Serializable, Cloneable, Comparable<PsTVTRoleScoreData> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsTVTRoleScoreData");

  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField HEAD_FIELD_DESC = new org.apache.thrift.protocol.TField("head", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField MVP_FIELD_DESC = new org.apache.thrift.protocol.TField("mvp", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField SEX_FIELD_DESC = new org.apache.thrift.protocol.TField("sex", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField ROLE_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("roleInfo", org.apache.thrift.protocol.TType.STRUCT, (short)7);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsTVTRoleScoreDataStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsTVTRoleScoreDataTupleSchemeFactory();

  /**
   * id
   */
  public long roleId; // optional
  /**
   * 头像
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String head; // optional
  /**
   * 名字
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String name; // optional
  /**
   * 积分
   */
  public int score; // optional
  /**
   * 类型：0 非mvp或非svp 1:MVP 2:SVP
   */
  public int mvp; // optional
  public int sex; // optional
  public @org.apache.thrift.annotation.Nullable PsRoleInfo roleInfo; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    ROLE_ID((short)1, "roleId"),
    /**
     * 头像
     */
    HEAD((short)2, "head"),
    /**
     * 名字
     */
    NAME((short)3, "name"),
    /**
     * 积分
     */
    SCORE((short)4, "score"),
    /**
     * 类型：0 非mvp或非svp 1:MVP 2:SVP
     */
    MVP((short)5, "mvp"),
    SEX((short)6, "sex"),
    ROLE_INFO((short)7, "roleInfo");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_ID
          return ROLE_ID;
        case 2: // HEAD
          return HEAD;
        case 3: // NAME
          return NAME;
        case 4: // SCORE
          return SCORE;
        case 5: // MVP
          return MVP;
        case 6: // SEX
          return SEX;
        case 7: // ROLE_INFO
          return ROLE_INFO;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLEID_ISSET_ID = 0;
  private static final int __SCORE_ISSET_ID = 1;
  private static final int __MVP_ISSET_ID = 2;
  private static final int __SEX_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ROLE_ID,_Fields.HEAD,_Fields.NAME,_Fields.SCORE,_Fields.MVP,_Fields.SEX,_Fields.ROLE_INFO};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.HEAD, new org.apache.thrift.meta_data.FieldMetaData("head", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MVP, new org.apache.thrift.meta_data.FieldMetaData("mvp", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SEX, new org.apache.thrift.meta_data.FieldMetaData("sex", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROLE_INFO, new org.apache.thrift.meta_data.FieldMetaData("roleInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsRoleInfo.class)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsTVTRoleScoreData.class, metaDataMap);
  }

  public PsTVTRoleScoreData() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsTVTRoleScoreData(PsTVTRoleScoreData other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roleId = other.roleId;
    if (other.isSetHead()) {
      this.head = other.head;
    }
    if (other.isSetName()) {
      this.name = other.name;
    }
    this.score = other.score;
    this.mvp = other.mvp;
    this.sex = other.sex;
    if (other.isSetRoleInfo()) {
      this.roleInfo = new PsRoleInfo(other.roleInfo);
    }
  }

  public PsTVTRoleScoreData deepCopy() {
    return new PsTVTRoleScoreData(this);
  }

  @Override
  public void clear() {
    setRoleIdIsSet(false);
    this.roleId = 0;
    this.head = null;
    this.name = null;
    setScoreIsSet(false);
    this.score = 0;
    setMvpIsSet(false);
    this.mvp = 0;
    setSexIsSet(false);
    this.sex = 0;
    this.roleInfo = null;
  }

  /**
   * id
   */
  public long getRoleId() {
    return this.roleId;
  }

  /**
   * id
   */
  public PsTVTRoleScoreData setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  /**
   * 头像
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHead() {
    return this.head;
  }

  /**
   * 头像
   */
  public PsTVTRoleScoreData setHead(@org.apache.thrift.annotation.Nullable java.lang.String head) {
    this.head = head;
    return this;
  }

  public void unsetHead() {
    this.head = null;
  }

  /** Returns true if field head is set (has been assigned a value) and false otherwise */
  public boolean isSetHead() {
    return this.head != null;
  }

  public void setHeadIsSet(boolean value) {
    if (!value) {
      this.head = null;
    }
  }

  /**
   * 名字
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getName() {
    return this.name;
  }

  /**
   * 名字
   */
  public PsTVTRoleScoreData setName(@org.apache.thrift.annotation.Nullable java.lang.String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  /**
   * 积分
   */
  public int getScore() {
    return this.score;
  }

  /**
   * 积分
   */
  public PsTVTRoleScoreData setScore(int score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  /**
   * 类型：0 非mvp或非svp 1:MVP 2:SVP
   */
  public int getMvp() {
    return this.mvp;
  }

  /**
   * 类型：0 非mvp或非svp 1:MVP 2:SVP
   */
  public PsTVTRoleScoreData setMvp(int mvp) {
    this.mvp = mvp;
    setMvpIsSet(true);
    return this;
  }

  public void unsetMvp() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MVP_ISSET_ID);
  }

  /** Returns true if field mvp is set (has been assigned a value) and false otherwise */
  public boolean isSetMvp() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MVP_ISSET_ID);
  }

  public void setMvpIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MVP_ISSET_ID, value);
  }

  public int getSex() {
    return this.sex;
  }

  public PsTVTRoleScoreData setSex(int sex) {
    this.sex = sex;
    setSexIsSet(true);
    return this;
  }

  public void unsetSex() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEX_ISSET_ID);
  }

  /** Returns true if field sex is set (has been assigned a value) and false otherwise */
  public boolean isSetSex() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEX_ISSET_ID);
  }

  public void setSexIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEX_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public PsRoleInfo getRoleInfo() {
    return this.roleInfo;
  }

  public PsTVTRoleScoreData setRoleInfo(@org.apache.thrift.annotation.Nullable PsRoleInfo roleInfo) {
    this.roleInfo = roleInfo;
    return this;
  }

  public void unsetRoleInfo() {
    this.roleInfo = null;
  }

  /** Returns true if field roleInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleInfo() {
    return this.roleInfo != null;
  }

  public void setRoleInfoIsSet(boolean value) {
    if (!value) {
      this.roleInfo = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((java.lang.Long)value);
      }
      break;

    case HEAD:
      if (value == null) {
        unsetHead();
      } else {
        setHead((java.lang.String)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((java.lang.String)value);
      }
      break;

    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((java.lang.Integer)value);
      }
      break;

    case MVP:
      if (value == null) {
        unsetMvp();
      } else {
        setMvp((java.lang.Integer)value);
      }
      break;

    case SEX:
      if (value == null) {
        unsetSex();
      } else {
        setSex((java.lang.Integer)value);
      }
      break;

    case ROLE_INFO:
      if (value == null) {
        unsetRoleInfo();
      } else {
        setRoleInfo((PsRoleInfo)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_ID:
      return getRoleId();

    case HEAD:
      return getHead();

    case NAME:
      return getName();

    case SCORE:
      return getScore();

    case MVP:
      return getMvp();

    case SEX:
      return getSex();

    case ROLE_INFO:
      return getRoleInfo();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROLE_ID:
      return isSetRoleId();
    case HEAD:
      return isSetHead();
    case NAME:
      return isSetName();
    case SCORE:
      return isSetScore();
    case MVP:
      return isSetMvp();
    case SEX:
      return isSetSex();
    case ROLE_INFO:
      return isSetRoleInfo();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsTVTRoleScoreData)
      return this.equals((PsTVTRoleScoreData)that);
    return false;
  }

  public boolean equals(PsTVTRoleScoreData that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roleId = true && this.isSetRoleId();
    boolean that_present_roleId = true && that.isSetRoleId();
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    boolean this_present_head = true && this.isSetHead();
    boolean that_present_head = true && that.isSetHead();
    if (this_present_head || that_present_head) {
      if (!(this_present_head && that_present_head))
        return false;
      if (!this.head.equals(that.head))
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_score = true && this.isSetScore();
    boolean that_present_score = true && that.isSetScore();
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_mvp = true && this.isSetMvp();
    boolean that_present_mvp = true && that.isSetMvp();
    if (this_present_mvp || that_present_mvp) {
      if (!(this_present_mvp && that_present_mvp))
        return false;
      if (this.mvp != that.mvp)
        return false;
    }

    boolean this_present_sex = true && this.isSetSex();
    boolean that_present_sex = true && that.isSetSex();
    if (this_present_sex || that_present_sex) {
      if (!(this_present_sex && that_present_sex))
        return false;
      if (this.sex != that.sex)
        return false;
    }

    boolean this_present_roleInfo = true && this.isSetRoleInfo();
    boolean that_present_roleInfo = true && that.isSetRoleInfo();
    if (this_present_roleInfo || that_present_roleInfo) {
      if (!(this_present_roleInfo && that_present_roleInfo))
        return false;
      if (!this.roleInfo.equals(that.roleInfo))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRoleId()) ? 131071 : 524287);
    if (isSetRoleId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roleId);

    hashCode = hashCode * 8191 + ((isSetHead()) ? 131071 : 524287);
    if (isSetHead())
      hashCode = hashCode * 8191 + head.hashCode();

    hashCode = hashCode * 8191 + ((isSetName()) ? 131071 : 524287);
    if (isSetName())
      hashCode = hashCode * 8191 + name.hashCode();

    hashCode = hashCode * 8191 + ((isSetScore()) ? 131071 : 524287);
    if (isSetScore())
      hashCode = hashCode * 8191 + score;

    hashCode = hashCode * 8191 + ((isSetMvp()) ? 131071 : 524287);
    if (isSetMvp())
      hashCode = hashCode * 8191 + mvp;

    hashCode = hashCode * 8191 + ((isSetSex()) ? 131071 : 524287);
    if (isSetSex())
      hashCode = hashCode * 8191 + sex;

    hashCode = hashCode * 8191 + ((isSetRoleInfo()) ? 131071 : 524287);
    if (isSetRoleInfo())
      hashCode = hashCode * 8191 + roleInfo.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsTVTRoleScoreData other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoleId(), other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetHead(), other.isSetHead());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHead()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.head, other.head);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetName(), other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetScore(), other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMvp(), other.isSetMvp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMvp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mvp, other.mvp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSex(), other.isSetSex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sex, other.sex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRoleInfo(), other.isSetRoleInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleInfo, other.roleInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsTVTRoleScoreData(");
    boolean first = true;

    if (isSetRoleId()) {
      sb.append("roleId:");
      sb.append(this.roleId);
      first = false;
    }
    if (isSetHead()) {
      if (!first) sb.append(", ");
      sb.append("head:");
      if (this.head == null) {
        sb.append("null");
      } else {
        sb.append(this.head);
      }
      first = false;
    }
    if (isSetName()) {
      if (!first) sb.append(", ");
      sb.append("name:");
      if (this.name == null) {
        sb.append("null");
      } else {
        sb.append(this.name);
      }
      first = false;
    }
    if (isSetScore()) {
      if (!first) sb.append(", ");
      sb.append("score:");
      sb.append(this.score);
      first = false;
    }
    if (isSetMvp()) {
      if (!first) sb.append(", ");
      sb.append("mvp:");
      sb.append(this.mvp);
      first = false;
    }
    if (isSetSex()) {
      if (!first) sb.append(", ");
      sb.append("sex:");
      sb.append(this.sex);
      first = false;
    }
    if (isSetRoleInfo()) {
      if (!first) sb.append(", ");
      sb.append("roleInfo:");
      if (this.roleInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.roleInfo);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (roleInfo != null) {
      roleInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsTVTRoleScoreDataStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsTVTRoleScoreDataStandardScheme getScheme() {
      return new PsTVTRoleScoreDataStandardScheme();
    }
  }

  private static class PsTVTRoleScoreDataStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsTVTRoleScoreData> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsTVTRoleScoreData struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // HEAD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.head = iprot.readString();
              struct.setHeadIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.score = iprot.readI32();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // MVP
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.mvp = iprot.readI32();
              struct.setMvpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SEX
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.sex = iprot.readI32();
              struct.setSexIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ROLE_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.roleInfo = new PsRoleInfo();
              struct.roleInfo.read(iprot);
              struct.setRoleInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsTVTRoleScoreData struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetRoleId()) {
        oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
        oprot.writeI64(struct.roleId);
        oprot.writeFieldEnd();
      }
      if (struct.head != null) {
        if (struct.isSetHead()) {
          oprot.writeFieldBegin(HEAD_FIELD_DESC);
          oprot.writeString(struct.head);
          oprot.writeFieldEnd();
        }
      }
      if (struct.name != null) {
        if (struct.isSetName()) {
          oprot.writeFieldBegin(NAME_FIELD_DESC);
          oprot.writeString(struct.name);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetScore()) {
        oprot.writeFieldBegin(SCORE_FIELD_DESC);
        oprot.writeI32(struct.score);
        oprot.writeFieldEnd();
      }
      if (struct.isSetMvp()) {
        oprot.writeFieldBegin(MVP_FIELD_DESC);
        oprot.writeI32(struct.mvp);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSex()) {
        oprot.writeFieldBegin(SEX_FIELD_DESC);
        oprot.writeI32(struct.sex);
        oprot.writeFieldEnd();
      }
      if (struct.roleInfo != null) {
        if (struct.isSetRoleInfo()) {
          oprot.writeFieldBegin(ROLE_INFO_FIELD_DESC);
          struct.roleInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsTVTRoleScoreDataTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsTVTRoleScoreDataTupleScheme getScheme() {
      return new PsTVTRoleScoreDataTupleScheme();
    }
  }

  private static class PsTVTRoleScoreDataTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsTVTRoleScoreData> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsTVTRoleScoreData struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRoleId()) {
        optionals.set(0);
      }
      if (struct.isSetHead()) {
        optionals.set(1);
      }
      if (struct.isSetName()) {
        optionals.set(2);
      }
      if (struct.isSetScore()) {
        optionals.set(3);
      }
      if (struct.isSetMvp()) {
        optionals.set(4);
      }
      if (struct.isSetSex()) {
        optionals.set(5);
      }
      if (struct.isSetRoleInfo()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetRoleId()) {
        oprot.writeI64(struct.roleId);
      }
      if (struct.isSetHead()) {
        oprot.writeString(struct.head);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetScore()) {
        oprot.writeI32(struct.score);
      }
      if (struct.isSetMvp()) {
        oprot.writeI32(struct.mvp);
      }
      if (struct.isSetSex()) {
        oprot.writeI32(struct.sex);
      }
      if (struct.isSetRoleInfo()) {
        struct.roleInfo.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsTVTRoleScoreData struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.roleId = iprot.readI64();
        struct.setRoleIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.head = iprot.readString();
        struct.setHeadIsSet(true);
      }
      if (incoming.get(2)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.score = iprot.readI32();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(4)) {
        struct.mvp = iprot.readI32();
        struct.setMvpIsSet(true);
      }
      if (incoming.get(5)) {
        struct.sex = iprot.readI32();
        struct.setSexIsSet(true);
      }
      if (incoming.get(6)) {
        struct.roleInfo = new PsRoleInfo();
        struct.roleInfo.read(iprot);
        struct.setRoleInfoIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

