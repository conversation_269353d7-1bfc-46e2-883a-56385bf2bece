/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 官职任命信息请求
 * @Message(4781)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcOfficialAppointmentInfo implements org.apache.thrift.TBase<GcOfficialAppointmentInfo, GcOfficialAppointmentInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcOfficialAppointmentInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcOfficialAppointmentInfo");

  private static final org.apache.thrift.protocol.TField OFFICIAL_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("officialInfo", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPOINT_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("appointList", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField NEXT_APPLY_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("nextApplyTime", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField MAX_APPOINT_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("maxAppointNum", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField SEASON_FIELD_DESC = new org.apache.thrift.protocol.TField("season", org.apache.thrift.protocol.TType.I32, (short)5);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcOfficialAppointmentInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcOfficialAppointmentInfoTupleSchemeFactory();

  /**
   * 官职id*
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsOfficial officialInfo; // optional
  /**
   * 预上任列表*
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsOfficialApply> appointList; // optional
  /**
   * 下次可申请时间*
   */
  public long nextApplyTime; // optional
  /**
   * 最大预约列表*
   */
  public int maxAppointNum; // optional
  /**
   * 是否是k服*
   */
  public int season; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 官职id*
     */
    OFFICIAL_INFO((short)1, "officialInfo"),
    /**
     * 预上任列表*
     */
    APPOINT_LIST((short)2, "appointList"),
    /**
     * 下次可申请时间*
     */
    NEXT_APPLY_TIME((short)3, "nextApplyTime"),
    /**
     * 最大预约列表*
     */
    MAX_APPOINT_NUM((short)4, "maxAppointNum"),
    /**
     * 是否是k服*
     */
    SEASON((short)5, "season");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // OFFICIAL_INFO
          return OFFICIAL_INFO;
        case 2: // APPOINT_LIST
          return APPOINT_LIST;
        case 3: // NEXT_APPLY_TIME
          return NEXT_APPLY_TIME;
        case 4: // MAX_APPOINT_NUM
          return MAX_APPOINT_NUM;
        case 5: // SEASON
          return SEASON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __NEXTAPPLYTIME_ISSET_ID = 0;
  private static final int __MAXAPPOINTNUM_ISSET_ID = 1;
  private static final int __SEASON_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.OFFICIAL_INFO,_Fields.APPOINT_LIST,_Fields.NEXT_APPLY_TIME,_Fields.MAX_APPOINT_NUM,_Fields.SEASON};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.OFFICIAL_INFO, new org.apache.thrift.meta_data.FieldMetaData("officialInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsOfficial.class)));
    tmpMap.put(_Fields.APPOINT_LIST, new org.apache.thrift.meta_data.FieldMetaData("appointList", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsOfficialApply.class))));
    tmpMap.put(_Fields.NEXT_APPLY_TIME, new org.apache.thrift.meta_data.FieldMetaData("nextApplyTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MAX_APPOINT_NUM, new org.apache.thrift.meta_data.FieldMetaData("maxAppointNum", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SEASON, new org.apache.thrift.meta_data.FieldMetaData("season", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcOfficialAppointmentInfo.class, metaDataMap);
  }

  public GcOfficialAppointmentInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcOfficialAppointmentInfo(GcOfficialAppointmentInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetOfficialInfo()) {
      this.officialInfo = new com.lc.billion.icefire.protocol.structure.PsOfficial(other.officialInfo);
    }
    if (other.isSetAppointList()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsOfficialApply> __this__appointList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsOfficialApply>(other.appointList.size());
      for (com.lc.billion.icefire.protocol.structure.PsOfficialApply other_element : other.appointList) {
        __this__appointList.add(new com.lc.billion.icefire.protocol.structure.PsOfficialApply(other_element));
      }
      this.appointList = __this__appointList;
    }
    this.nextApplyTime = other.nextApplyTime;
    this.maxAppointNum = other.maxAppointNum;
    this.season = other.season;
  }

  public GcOfficialAppointmentInfo deepCopy() {
    return new GcOfficialAppointmentInfo(this);
  }

  @Override
  public void clear() {
    this.officialInfo = null;
    this.appointList = null;
    setNextApplyTimeIsSet(false);
    this.nextApplyTime = 0;
    setMaxAppointNumIsSet(false);
    this.maxAppointNum = 0;
    setSeasonIsSet(false);
    this.season = 0;
  }

  /**
   * 官职id*
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsOfficial getOfficialInfo() {
    return this.officialInfo;
  }

  /**
   * 官职id*
   */
  public GcOfficialAppointmentInfo setOfficialInfo(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsOfficial officialInfo) {
    this.officialInfo = officialInfo;
    return this;
  }

  public void unsetOfficialInfo() {
    this.officialInfo = null;
  }

  /** Returns true if field officialInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetOfficialInfo() {
    return this.officialInfo != null;
  }

  public void setOfficialInfoIsSet(boolean value) {
    if (!value) {
      this.officialInfo = null;
    }
  }

  public int getAppointListSize() {
    return (this.appointList == null) ? 0 : this.appointList.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsOfficialApply> getAppointListIterator() {
    return (this.appointList == null) ? null : this.appointList.iterator();
  }

  public void addToAppointList(com.lc.billion.icefire.protocol.structure.PsOfficialApply elem) {
    if (this.appointList == null) {
      this.appointList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsOfficialApply>();
    }
    this.appointList.add(elem);
  }

  /**
   * 预上任列表*
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsOfficialApply> getAppointList() {
    return this.appointList;
  }

  /**
   * 预上任列表*
   */
  public GcOfficialAppointmentInfo setAppointList(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsOfficialApply> appointList) {
    this.appointList = appointList;
    return this;
  }

  public void unsetAppointList() {
    this.appointList = null;
  }

  /** Returns true if field appointList is set (has been assigned a value) and false otherwise */
  public boolean isSetAppointList() {
    return this.appointList != null;
  }

  public void setAppointListIsSet(boolean value) {
    if (!value) {
      this.appointList = null;
    }
  }

  /**
   * 下次可申请时间*
   */
  public long getNextApplyTime() {
    return this.nextApplyTime;
  }

  /**
   * 下次可申请时间*
   */
  public GcOfficialAppointmentInfo setNextApplyTime(long nextApplyTime) {
    this.nextApplyTime = nextApplyTime;
    setNextApplyTimeIsSet(true);
    return this;
  }

  public void unsetNextApplyTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEXTAPPLYTIME_ISSET_ID);
  }

  /** Returns true if field nextApplyTime is set (has been assigned a value) and false otherwise */
  public boolean isSetNextApplyTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEXTAPPLYTIME_ISSET_ID);
  }

  public void setNextApplyTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEXTAPPLYTIME_ISSET_ID, value);
  }

  /**
   * 最大预约列表*
   */
  public int getMaxAppointNum() {
    return this.maxAppointNum;
  }

  /**
   * 最大预约列表*
   */
  public GcOfficialAppointmentInfo setMaxAppointNum(int maxAppointNum) {
    this.maxAppointNum = maxAppointNum;
    setMaxAppointNumIsSet(true);
    return this;
  }

  public void unsetMaxAppointNum() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MAXAPPOINTNUM_ISSET_ID);
  }

  /** Returns true if field maxAppointNum is set (has been assigned a value) and false otherwise */
  public boolean isSetMaxAppointNum() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MAXAPPOINTNUM_ISSET_ID);
  }

  public void setMaxAppointNumIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MAXAPPOINTNUM_ISSET_ID, value);
  }

  /**
   * 是否是k服*
   */
  public int getSeason() {
    return this.season;
  }

  /**
   * 是否是k服*
   */
  public GcOfficialAppointmentInfo setSeason(int season) {
    this.season = season;
    setSeasonIsSet(true);
    return this;
  }

  public void unsetSeason() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SEASON_ISSET_ID);
  }

  /** Returns true if field season is set (has been assigned a value) and false otherwise */
  public boolean isSetSeason() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SEASON_ISSET_ID);
  }

  public void setSeasonIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SEASON_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case OFFICIAL_INFO:
      if (value == null) {
        unsetOfficialInfo();
      } else {
        setOfficialInfo((com.lc.billion.icefire.protocol.structure.PsOfficial)value);
      }
      break;

    case APPOINT_LIST:
      if (value == null) {
        unsetAppointList();
      } else {
        setAppointList((java.util.List<com.lc.billion.icefire.protocol.structure.PsOfficialApply>)value);
      }
      break;

    case NEXT_APPLY_TIME:
      if (value == null) {
        unsetNextApplyTime();
      } else {
        setNextApplyTime((java.lang.Long)value);
      }
      break;

    case MAX_APPOINT_NUM:
      if (value == null) {
        unsetMaxAppointNum();
      } else {
        setMaxAppointNum((java.lang.Integer)value);
      }
      break;

    case SEASON:
      if (value == null) {
        unsetSeason();
      } else {
        setSeason((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case OFFICIAL_INFO:
      return getOfficialInfo();

    case APPOINT_LIST:
      return getAppointList();

    case NEXT_APPLY_TIME:
      return getNextApplyTime();

    case MAX_APPOINT_NUM:
      return getMaxAppointNum();

    case SEASON:
      return getSeason();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case OFFICIAL_INFO:
      return isSetOfficialInfo();
    case APPOINT_LIST:
      return isSetAppointList();
    case NEXT_APPLY_TIME:
      return isSetNextApplyTime();
    case MAX_APPOINT_NUM:
      return isSetMaxAppointNum();
    case SEASON:
      return isSetSeason();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcOfficialAppointmentInfo)
      return this.equals((GcOfficialAppointmentInfo)that);
    return false;
  }

  public boolean equals(GcOfficialAppointmentInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_officialInfo = true && this.isSetOfficialInfo();
    boolean that_present_officialInfo = true && that.isSetOfficialInfo();
    if (this_present_officialInfo || that_present_officialInfo) {
      if (!(this_present_officialInfo && that_present_officialInfo))
        return false;
      if (!this.officialInfo.equals(that.officialInfo))
        return false;
    }

    boolean this_present_appointList = true && this.isSetAppointList();
    boolean that_present_appointList = true && that.isSetAppointList();
    if (this_present_appointList || that_present_appointList) {
      if (!(this_present_appointList && that_present_appointList))
        return false;
      if (!this.appointList.equals(that.appointList))
        return false;
    }

    boolean this_present_nextApplyTime = true && this.isSetNextApplyTime();
    boolean that_present_nextApplyTime = true && that.isSetNextApplyTime();
    if (this_present_nextApplyTime || that_present_nextApplyTime) {
      if (!(this_present_nextApplyTime && that_present_nextApplyTime))
        return false;
      if (this.nextApplyTime != that.nextApplyTime)
        return false;
    }

    boolean this_present_maxAppointNum = true && this.isSetMaxAppointNum();
    boolean that_present_maxAppointNum = true && that.isSetMaxAppointNum();
    if (this_present_maxAppointNum || that_present_maxAppointNum) {
      if (!(this_present_maxAppointNum && that_present_maxAppointNum))
        return false;
      if (this.maxAppointNum != that.maxAppointNum)
        return false;
    }

    boolean this_present_season = true && this.isSetSeason();
    boolean that_present_season = true && that.isSetSeason();
    if (this_present_season || that_present_season) {
      if (!(this_present_season && that_present_season))
        return false;
      if (this.season != that.season)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetOfficialInfo()) ? 131071 : 524287);
    if (isSetOfficialInfo())
      hashCode = hashCode * 8191 + officialInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetAppointList()) ? 131071 : 524287);
    if (isSetAppointList())
      hashCode = hashCode * 8191 + appointList.hashCode();

    hashCode = hashCode * 8191 + ((isSetNextApplyTime()) ? 131071 : 524287);
    if (isSetNextApplyTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(nextApplyTime);

    hashCode = hashCode * 8191 + ((isSetMaxAppointNum()) ? 131071 : 524287);
    if (isSetMaxAppointNum())
      hashCode = hashCode * 8191 + maxAppointNum;

    hashCode = hashCode * 8191 + ((isSetSeason()) ? 131071 : 524287);
    if (isSetSeason())
      hashCode = hashCode * 8191 + season;

    return hashCode;
  }

  @Override
  public int compareTo(GcOfficialAppointmentInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetOfficialInfo(), other.isSetOfficialInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOfficialInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.officialInfo, other.officialInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAppointList(), other.isSetAppointList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppointList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appointList, other.appointList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNextApplyTime(), other.isSetNextApplyTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNextApplyTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nextApplyTime, other.nextApplyTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMaxAppointNum(), other.isSetMaxAppointNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMaxAppointNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.maxAppointNum, other.maxAppointNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSeason(), other.isSetSeason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.season, other.season);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcOfficialAppointmentInfo(");
    boolean first = true;

    if (isSetOfficialInfo()) {
      sb.append("officialInfo:");
      if (this.officialInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.officialInfo);
      }
      first = false;
    }
    if (isSetAppointList()) {
      if (!first) sb.append(", ");
      sb.append("appointList:");
      if (this.appointList == null) {
        sb.append("null");
      } else {
        sb.append(this.appointList);
      }
      first = false;
    }
    if (isSetNextApplyTime()) {
      if (!first) sb.append(", ");
      sb.append("nextApplyTime:");
      sb.append(this.nextApplyTime);
      first = false;
    }
    if (isSetMaxAppointNum()) {
      if (!first) sb.append(", ");
      sb.append("maxAppointNum:");
      sb.append(this.maxAppointNum);
      first = false;
    }
    if (isSetSeason()) {
      if (!first) sb.append(", ");
      sb.append("season:");
      sb.append(this.season);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (officialInfo != null) {
      officialInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcOfficialAppointmentInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcOfficialAppointmentInfoStandardScheme getScheme() {
      return new GcOfficialAppointmentInfoStandardScheme();
    }
  }

  private static class GcOfficialAppointmentInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcOfficialAppointmentInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcOfficialAppointmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // OFFICIAL_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.officialInfo = new com.lc.billion.icefire.protocol.structure.PsOfficial();
              struct.officialInfo.read(iprot);
              struct.setOfficialInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPOINT_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.appointList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsOfficialApply>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsOfficialApply _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsOfficialApply();
                  _elem1.read(iprot);
                  struct.appointList.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setAppointListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NEXT_APPLY_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.nextApplyTime = iprot.readI64();
              struct.setNextApplyTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // MAX_APPOINT_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.maxAppointNum = iprot.readI32();
              struct.setMaxAppointNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SEASON
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.season = iprot.readI32();
              struct.setSeasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcOfficialAppointmentInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.officialInfo != null) {
        if (struct.isSetOfficialInfo()) {
          oprot.writeFieldBegin(OFFICIAL_INFO_FIELD_DESC);
          struct.officialInfo.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.appointList != null) {
        if (struct.isSetAppointList()) {
          oprot.writeFieldBegin(APPOINT_LIST_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.appointList.size()));
            for (com.lc.billion.icefire.protocol.structure.PsOfficialApply _iter3 : struct.appointList)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetNextApplyTime()) {
        oprot.writeFieldBegin(NEXT_APPLY_TIME_FIELD_DESC);
        oprot.writeI64(struct.nextApplyTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetMaxAppointNum()) {
        oprot.writeFieldBegin(MAX_APPOINT_NUM_FIELD_DESC);
        oprot.writeI32(struct.maxAppointNum);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSeason()) {
        oprot.writeFieldBegin(SEASON_FIELD_DESC);
        oprot.writeI32(struct.season);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcOfficialAppointmentInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcOfficialAppointmentInfoTupleScheme getScheme() {
      return new GcOfficialAppointmentInfoTupleScheme();
    }
  }

  private static class GcOfficialAppointmentInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcOfficialAppointmentInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcOfficialAppointmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetOfficialInfo()) {
        optionals.set(0);
      }
      if (struct.isSetAppointList()) {
        optionals.set(1);
      }
      if (struct.isSetNextApplyTime()) {
        optionals.set(2);
      }
      if (struct.isSetMaxAppointNum()) {
        optionals.set(3);
      }
      if (struct.isSetSeason()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetOfficialInfo()) {
        struct.officialInfo.write(oprot);
      }
      if (struct.isSetAppointList()) {
        {
          oprot.writeI32(struct.appointList.size());
          for (com.lc.billion.icefire.protocol.structure.PsOfficialApply _iter4 : struct.appointList)
          {
            _iter4.write(oprot);
          }
        }
      }
      if (struct.isSetNextApplyTime()) {
        oprot.writeI64(struct.nextApplyTime);
      }
      if (struct.isSetMaxAppointNum()) {
        oprot.writeI32(struct.maxAppointNum);
      }
      if (struct.isSetSeason()) {
        oprot.writeI32(struct.season);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcOfficialAppointmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.officialInfo = new com.lc.billion.icefire.protocol.structure.PsOfficial();
        struct.officialInfo.read(iprot);
        struct.setOfficialInfoIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.appointList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsOfficialApply>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsOfficialApply _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsOfficialApply();
            _elem6.read(iprot);
            struct.appointList.add(_elem6);
          }
        }
        struct.setAppointListIsSet(true);
      }
      if (incoming.get(2)) {
        struct.nextApplyTime = iprot.readI64();
        struct.setNextApplyTimeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.maxAppointNum = iprot.readI32();
        struct.setMaxAppointNumIsSet(true);
      }
      if (incoming.get(4)) {
        struct.season = iprot.readI32();
        struct.setSeasonIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

