package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightContext;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.buff.effect.ProtectionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleScoreService;
import com.lc.billion.icefire.game.biz.service.impl.role.RoleCityServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 集结GVG玩家
 *
 * <AUTHOR>
 * @date 2021.02.22
 */
@Service
public class GVGCityRallyOperation extends AbstractGvgFightOperation {
    @Autowired
    private AllianceServiceImpl allianceService;
    @Autowired
    private ProtectionServiceImpl protectionService;
    @Autowired
    private RoleCityServiceImpl roleCityService;
    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    private GVGBattleScoreService gvgBattleScoreService;

    @Override
    public ArmyType getArmyType() {
        return ArmyType.GVG_RALLY_ATTACK_PLAYER;
    }

    @Override
    public boolean check(ArmyInfo army, SceneNode targetNode) {
        if (!super.check(army, targetNode)) {
            armyManager.sendPVPTargetChangeMail(army, EmailConstants.ARMY_TARGET_DISAPPEAR);
            return false;
        }
        if (protectionService.hasProtection((RoleCity) targetNode)) {
            armyManager.sendPVPTargetChangeMail(army, EmailConstants.ARMY_TARGET_PROTECTED_RALLY);
            return false;
        }
        if (allianceService.isSameAlliance(army.getRoleId(), targetNode.getPersistKey())) {
            return false;
        }
        return true;
    }

    @Override
    public void finishWork(ArmyInfo attackerMainArmy) {
        super.finishWork(attackerMainArmy);
        armyManager.marchRetBILog(attackerMainArmy);
        armyManager.returnArmy(attackerMainArmy);
    }

    @Override
    protected void endBattle(ArmyInfo army) {
        FightContext fightContext = army.getFightContext();
        if (fightContext.isWin()) {
            winBattle(army);
        }
        gvgBattleScoreService.onGVGFightEnd(army);
    }

    private void winBattle(ArmyInfo army) {
        // 结算奖励
        FightArmy defenderArmy = army.getFightContext().getDefender();
        RoleCity targetCity = null;
        if (defenderArmy.getTargetId() != null) {
            targetCity = roleCityManager.getRoleCity(defenderArmy.getTargetId());
        }
        if (targetCity == null) {
            ErrorLogUtil.errorLog("战斗异常|不应该进到这里,战斗结束了,但是目标玩家没有主城了");
            return;
        }
        // 败者食尘
        roleCityService.addCastleBroken(targetCity, army);
        // 按文云豪策划要求，在20211015 gvg战场集结不能掠夺玩家资源
        //pvpDespoil(army);
    }
}
