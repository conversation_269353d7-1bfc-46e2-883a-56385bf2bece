package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.MissionConfig.MissionMeta;
import com.lc.billion.icefire.game.biz.manager.RoleCityManager;
import com.lc.billion.icefire.game.biz.manager.WorkProgressManager;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.CityBuild;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.work.WorkProgress;

/**
 * 提升{0}点建筑战斗力
 */
public class BuildingAddPowerMissionEvent extends AbstractMissionEvent {

	@Override
	public MissionType getType() {
		return MissionType.BUILDING_ADD_POWER;
	}

	@Override
	public void start(Role role, IMissionItem item, MissionMeta meta) {
		setAmount(item, meta, 0, false);
	}

	@Override
	public boolean effect(IMissionItem item, MissionMeta meta, Object... params) {
		int power = (Integer) params[0];
		if (power <= 0) {
			return false;
		}
		setAmount(item, meta, item.getProgress() + power, false);
		return true;
	}
}
