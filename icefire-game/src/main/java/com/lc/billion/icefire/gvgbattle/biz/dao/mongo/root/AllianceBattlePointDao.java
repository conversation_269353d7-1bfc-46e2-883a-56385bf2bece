package com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;

/**
 * <AUTHOR>
 *
 */
@Repository
public class AllianceBattlePointDao extends RootDao<AllianceBattlePoint> {

	public AllianceBattlePointDao() {
		super(AllianceBattlePoint.class, false);
	}

	@Override
	protected MongoCursor<AllianceBattlePoint> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(AllianceBattlePoint entity) {

	}

	@Override
	protected void removeMemoryIndexes(AllianceBattlePoint entity) {

	}

	@Override
	public AllianceBattlePoint createById(Long entityId) {
		int db = Application.getServerId();
		AllianceBattlePoint allianceBattlePoint = newEntityInstance();
		allianceBattlePoint.setPointSpeed(0);
		allianceBattlePoint.setPersistKey(entityId);
		return createEntity(db, allianceBattlePoint);
	}

	public void deleteAll() {
		delete(findAll());
	}
}
