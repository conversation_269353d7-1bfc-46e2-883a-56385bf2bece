package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.lc.billion.icefire.core.common.PlatformType;
import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

import java.util.Map;

public enum BuildingType implements IntEnum {

    /**
     * 兵工厂
     * */
    ARM_FACTORY(1),

    /**
     * 指挥中心
     * */
    COMMAND_CENTER(3),

    /**
     * 车间
     * */
    WORK_SHOP(4),

    /**
     * 加油站
     * */
    FILLING_STATION(5),

    /**
     * 医院
     * */
    HOSPITAL(6),

    /**
     * 武器库
     * */
    ARSENAL(7),

    /**
     * 物资点
     * */
    RESOURCE_STATION(8),

    /**
     * 怪物
     * */
    NPC(9),

    <PERSON><PERSON><PERSON>(101),        // 乌巢
    XiaoLiangCang(102), // 小粮仓
    LiangCang(103),     // 粮仓
    BingQiFang(104),    // 兵器坊
    GuanDu(105),        // 官渡
    PiLiChe(106),       // 霹雳车
    DaYing(107),        // 大营
    ZiYuan(108),        // 资源
    Invalid(-1),        // 错误类型
    ;

    private static final Map<Integer, BuildingType> INDEXES_MAP = EnumUtils.toMap(values());

    private final int type;

    private BuildingType(int type) {
        this.type = type;
    }

    @Override
    public int getId() {
        return type;
    }

    public int getPsType() { return  type; }

    public static BuildingType findById(int id) {
        BuildingType result = INDEXES_MAP.get(id);
        if (result == null) {
            result = BuildingType.Invalid;
        }
        return result;
    }
}
