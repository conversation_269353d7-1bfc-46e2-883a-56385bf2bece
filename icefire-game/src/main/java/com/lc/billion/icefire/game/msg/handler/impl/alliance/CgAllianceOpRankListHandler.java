package com.lc.billion.icefire.game.msg.handler.impl.alliance;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.alliance.AllianceOpRecordEnum;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceOpRecordService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgAllianceOpRankList;

/**
 * 获取联盟排行榜
 * 
 * <AUTHOR>
 */
@Controller
public class CgAllianceOpRankListHandler extends CgAbstractMessageHandler<CgAllianceOpRankList> {

	@Autowired
	private AllianceOpRecordService allianceOpRecordService;

	@Override
	public void handle(Role role, CgAllianceOpRankList message) {
		AllianceOpRecordEnum opRecordEnum = AllianceOpRecordEnum.getByPsType(message.getOpType());
		allianceOpRecordService.rankGet(role, opRecordEnum);
	}
}
