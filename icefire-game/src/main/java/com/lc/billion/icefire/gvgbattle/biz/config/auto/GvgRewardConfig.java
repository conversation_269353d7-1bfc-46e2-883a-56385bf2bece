package com.lc.billion.icefire.gvgbattle.biz.config.auto;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "gvgReward", metaClass = GvgRewardConfig.GvgRewardMeta.class)
public class GvgRewardConfig {
	@MetaMap
	private final Map<String, GvgRewardMeta> metaMap = new HashMap<>();

	private final Map<Integer, List<GvgRewardMeta>> metaListByType = new HashMap<>();

	public void init(List<GvgRewardMeta> list) {
		for(var metaData: list) {
			metaListByType.compute(metaData.getType(), (k, v) -> v == null ? new ArrayList<>() : v).add(metaData);
		}
	}

	public Map<String, GvgRewardMeta> getMetaMap() {
		return metaMap;
	}

	public GvgRewardMeta get(String id) {
		return metaMap.get(id);
	}

	public List<GvgRewardMeta> getMetaListByType(int type) {
		return metaListByType.get(type);
	}

	public static class GvgRewardMeta extends AbstractMeta {
		private int type;

		/*
		 *  发奖需要的积分
		 */
		private String point;

		/*
		 *  对应奖励的dropgroupID
		 */
		private String reward;

		/*
		 *  发送的邮件ID
		 */
		private String mail;

		public void init(JsonNode json) {
		}

		public int getType() {
			return type;
		}

		public String getPoint() {
			return point;
		}

		public String getReward() {
			return reward;
		}

		public String getMail() {
			return mail;
		}
	}

	/**
	 * "1.获胜联盟中的所有成员都将获得奖励（未参战的也可以获得）
	 *
	 * 2.落败联盟中的所有成员都将获得奖励（未参战的也可以获得）
	 *
	 * 3.获胜联盟中所有报名成员都将获得奖励（包括替补）
	 *
	 * 4.落败联盟中所有报名成员都将获得奖励（包括替补）
	 *
	 * 5.获胜方的参战成员将根据个人胜利积分获得额外奖励（不包括替补）
	 *
	 * 6.落败方的参战成员将根据个人胜利积分获得额外奖励（不包括替补）
	 *
	 * "
	 */

	public GvgRewardMeta getWinAllianceMemberRewardMeta() {
		return getMetaTypeDefault(1);
	}

	public GvgRewardMeta getLoseAllianceMemberRewardMeta() {
		return getMetaTypeDefault(2);
	}

	public GvgRewardMeta getByeAllianceMemberRewardMeta() {
		return getMetaTypeDefault(7);
	}

	public GvgRewardMeta getWinAllianceSignUpMemberRewardMeta() {
		return getMetaTypeDefault(3);
	}

	public GvgRewardMeta getLoseAllianceSignUpMemberRewardMeta() {
		return getMetaTypeDefault(4);
	}

	private GvgRewardMeta getMetaTypeDefault(int type) {
		List<GvgRewardMeta> foo = getMetaListByType(type);
		if (foo == null || foo.isEmpty()) {
			return null;
		}
		return foo.get(0);
	}

	/**
	 * 胜利方根据个人积分获得对应的奖励配置
	 * 
	 * @param personalScore
	 * @return
	 */
	public GvgRewardMeta getWinAlliancePersonalScoreRankReward(int personalScore) {
		List<GvgRewardMeta> metaListByType = getMetaListByType(5);
		if (metaListByType == null)
			return null;
		for (GvgRewardMeta meta : metaListByType) {
			String[] split = StringUtils.split(meta.getPoint(), "|");
			if (split != null && split.length == 2 && Integer.parseInt(split[0]) <= personalScore && personalScore <= Integer.parseInt(split[1])) {
				return meta;
			}
		}
		return null;
	}


	/**
	 * 胜利方根据个人积分获得对应的奖励配置
	 *
	 * @return
	 */
	public List<GvgRewardMeta> getWinAlliancePersonalScoreRankRewardList() {
		 return getMetaListByType(5);
	}

	/**
	 * 失败方根据个人积分获得对应的奖励配置
	 *
	 * @return
	 */
	public List<GvgRewardMeta> getLoseAlliancePersonalScoreRankRewardList() {
		return getMetaListByType(6);
	}

	/**
	 * 失败方根据个人积分获得对应的奖励配置
	 *
	 * @param personalScore
	 * @return
	 */
	public GvgRewardMeta getLoseAlliancePersonalScoreRankReward(int personalScore) {
		List<GvgRewardMeta> metaListByType = getMetaListByType(6);
		if (metaListByType == null)
			return null;
		for (GvgRewardMeta meta : metaListByType) {
			String[] split = StringUtils.split(meta.getPoint(), "|");
			if (split != null && split.length == 2 && Integer.parseInt(split[0]) <= personalScore && personalScore <= Integer.parseInt(split[1])) {
				return meta;
			}
		}
		return null;
	}
}
