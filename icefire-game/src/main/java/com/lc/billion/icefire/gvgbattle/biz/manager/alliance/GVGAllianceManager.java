package com.lc.billion.icefire.gvgbattle.biz.manager.alliance;

import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceSettingDao;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceTechDao;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceSetting;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceMemberDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExtraDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.AbstractRoleManager;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.manager.IMigrateForGVGManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceLeader;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceConstants;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.gvg.model.MigrateForGVGContext;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * <AUTHOR>
 * 	1.在GVG战场中，联盟是玩家原服联盟：进入战场加载原联盟和联盟成员
 * 	2.在TVT战场中，联盟是玩家临时虚拟联盟：进入战场创建临时联盟并创建联盟成员，原服联盟和联盟成员不加载
 *
 */
@Component
public class GVGAllianceManager extends AbstractRoleManager implements IMigrateForGVGManager {

	@Autowired
	private AllianceDao allianceDao;
	@Autowired
	private AllianceTechDao allianceTechDao;
	@Autowired
	private AllianceMemberDao allianceMemberDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private RoleServerInfoDao roleServerInfoDao;
	@Autowired
	private ConfigCenter configCenter;
	@Autowired
	private GVGBattleDataVoManager gvgBattleDataVoManager;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private RoleExtraDao roleExtraDao;
	@Autowired
	private RoleDao roleDao;
    @Autowired
    private AllianceSettingDao allianceSettingDao;

	@Override
	public void onCreateRole(Role role, Player player) {

	}

	@Override
	protected void onCreateRoleFailed(Role role) {

	}

	@Override
	public void beforeLogin(Role role) {
		// 如果是GVG战斗服要加载玩家联盟
		ServerType serverType = Application.getServerType();
		if (serverType == ServerType.GVG_BATTLE) {
			Long allianceId = role.getAllianceId();
			if (JavaUtils.bool(allianceId)) {
				Long roleId = role.getId();
				// 本服的RoleServerInfo
				RoleServerInfo roleServerInfo = roleServerInfoDao.findById(roleId);
				int allianceServerId = roleServerInfo.getRegisterServerId();
				ServerType serverType2 = configCenter.getServerType(allianceServerId);
				// 如果是赛季服，还要找一层
				if (serverType2 == ServerType.KVK_SEASON) {
					roleServerInfo = roleServerInfoDao.findRoleServerInfoFromDB(allianceServerId, roleId);
					allianceServerId = roleServerInfo.getKvkHomeServerId();
				}
				AllianceMember allianceMember = allianceMemberDao.findById(roleId);
				if (allianceMember == null) {
					allianceMemberDao.loadAllianceMemberFromOtherDB(allianceServerId, roleId);
				}
			} else {
				ErrorLogUtil.errorLog("玩家进入战斗服没有联盟", "roleId",role.getId());
			}
		}else if(serverType == ServerType.TVT_BATTLE){
			beforeLoginByTVT(role);
		}
	}

	/**
	 * TVT登陆前处理
	 * */
	public void beforeLoginByTVT(Role role){
		// 本服的RoleServerInfo
		Long roleId = role.getId();
		RoleServerInfo roleServerInfo = roleServerInfoDao.findById(roleId);
		RoleExtra roleExtra = roleExtraDao.findById(roleId);
		logger.info("玩家TVT登陆前处理 Role:{}/{} RoleServerInfo空否:{} RoleExtra空否:{}",
				role.getId(), Application.getServerType(), roleServerInfo != null ? 0 : 1, roleExtra != null ? 0 : 1);

		int roleServerId = roleServerInfo.getHomeServerId();
		ServerType serverType2 = configCenter.getServerType(roleServerId);

		// 如果是赛季服，还要找一层
		if (serverType2 == ServerType.KVK_SEASON) {
			roleServerInfo = roleServerInfoDao.findRoleServerInfoFromDB(roleServerId, roleId);
			roleServerId = roleServerInfo.getKvkHomeServerId();
		}

		// 获取玩家的队伍Id
		int teamId = roleExtra.getTvtTeamId();
		if(teamId > 0){
			Long tempAllianceId = gvgBattleDataVoManager.getAllianceIdByTeamId(teamId);
			if(JavaUtils.bool(tempAllianceId)){
				role.setTempAllianceId(tempAllianceId);
				roleDao.save(role);

				// 每次进来，判断是否加入联盟
				Alliance tempAlliance = allianceService.getAllianceById(tempAllianceId);
				AllianceMember tempAllianceMember = allianceMemberManager.getMember(roleId);
				if(tempAllianceMember == null){
					allianceService.createMember(role, tempAlliance, AllianceConstants.RANK_1, false);
					logger.info("TVT战场 玩家:{}/{}/{} 进入战场加入临时联盟:{}", role.getId(), teamId, role.getTempAllianceId(), tempAlliance.getId());
				}else {
					logger.info("TVT战场 玩家:{}/{}/{} 进入战场 临时联盟:{}", role.getId(), teamId, role.getTempAllianceId(), tempAlliance.getId());
				}

				if (!JavaUtils.bool(tempAlliance.getLeaderId())){
					AllianceLeader allianceLeader = new AllianceLeader();
					allianceLeader.copyProperty(role);
					tempAlliance.setAllianceLeader(allianceLeader);
					logger.info("TVT战场 设置临时联盟盟主:{}/{}/{} 临时联盟:{}", role.getId(), teamId, role.getTempAllianceId(), tempAlliance.getId());
				}

			}else {
				ErrorLogUtil.errorLog("玩家进入TVT战斗服没有临时联盟", "roleId",role.getId(), "tvtTeamId",roleExtra.getTvtTeamId());
			}
		}else {
			ErrorLogUtil.errorLog("玩家进入TVT战斗服没有队伍", "roleId",role.getId(), "tvtTeamId",roleExtra.getTvtTeamId());
		}
	}

	@Override
	public void afterLogin(Role role) {
		if (Application.isBattleServer()) {
			AllianceMember allianceMember = allianceMemberManager.getMember(role.getId());
			allianceMemberManager.broadcastGVGMemberUpdate(allianceMember);
		}
	}

	@Override
	public void migrateForGVGInTargetServerForFirstLogin(Role role) {
		// TODO Auto-generated method stub

	}

	@Override
	public void migrateForGVGInSrcServerFailed(MigrateForGVGContext migrateForGVGContext) {
		// TODO Auto-generated method stub

	}

	@Override
	public void migrateForGVGInSrcServer(Long roleId, MigrateForGVGContext migrateForGVGContext) {
		if (ServerConfigManager.getInstance().getWorldMapConfig().isBattleServer()) {
			// battle->game
			// 移除alliancemember
			AllianceMember allianceMember = allianceMemberDao.findById(roleId);
			if (allianceMember != null) {
				allianceMemberDao.removeMemory(roleId);
				allianceMemberManager.broadcastGVGMemberDel(allianceMember);
				logger.info("GVG战斗 玩家迁回原服，移除内存中allianceMember,roleId:{}",allianceMember.getPersistKey());
			}
		}
	}

	@Override
	public void migrateForGVGBackFirstLogin(Role role) {
		// TODO Auto-generated method stub

	}

}
