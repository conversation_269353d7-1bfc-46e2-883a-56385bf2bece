package com.lc.billion.icefire.gvgbattle.biz.model.army;

import com.lc.billion.icefire.core.common.TimeUtil;

/**
 * <AUTHOR>
 * @date 2021/1/26
 */
public class GvgGatherContext {
	private long carry; // 负重，可采集最大的积分
	private double speed; // 采集速度
	private long startTime;// 开始采集时间
	private long endTime;// 结束采集时间
	private boolean reCaculate;// 重算标志，可能由于被攻击，士兵减少，需要重算
	private double currentGatherAmount;// 当前采集量
	private long updateTime;// 上次结算时间
	private int originalTotalTime; // 初始时间总长，客户端显示进度条用
	private String metaId;

	public GvgGatherContext() {

	}

	public GvgGatherContext(long carry, double speed, String metaId) {
		this.carry = carry;
		this.speed = speed;
		this.startTime = TimeUtil.getNow();
		this.updateTime = this.startTime;
		int time = (int) (Math.ceil(Double.valueOf(carry / speed)) * TimeUtil.SECONDS_MILLIS);
		this.endTime = this.startTime + time;
		this.originalTotalTime = time;
		this.metaId = metaId;
	}

	public long getCarry() {
		return carry;
	}

	public void setCarry(long carry) {
		this.carry = carry;
	}

	public double getSpeed() {
		return speed;
	}

	public void setSpeed(double speed) {
		this.speed = speed;
	}

	public long getStartTime() {
		return startTime;
	}

	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public boolean isReCaculate() {
		return reCaculate;
	}

	public void setReCaculate(boolean reCaculate) {
		this.reCaculate = reCaculate;
	}

	public double getCurrentGatherAmount() {
		return currentGatherAmount;
	}

	public void setCurrentGatherAmount(double currentGatherAmount) {
		this.currentGatherAmount = currentGatherAmount;
	}

	public long getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(long updateTime) {
		this.updateTime = updateTime;
	}

	public int getOriginalTotalTime() {
		return originalTotalTime;
	}

	public void setOriginalTotalTime(int originalTotalTime) {
		this.originalTotalTime = originalTotalTime;
	}

	public String getMetaId() {
		return metaId;
	}

	public void setMetaId(String metaId) {
		this.metaId = metaId;
	}

	/**
	 * 结算
	 */
	public double calculate(long now) {
		double delta = (now - this.updateTime) * 1d / TimeUtil.SECONDS_MILLIS * this.speed;
		if (this.currentGatherAmount + delta > this.carry) {
			// 采集过量
			delta = this.carry - this.currentGatherAmount;
			this.currentGatherAmount = this.carry;
		} else {
			// +
			this.currentGatherAmount += delta;
		}
		this.updateTime = now;
		return delta;
	}

	/**
	 * 重算
	 *
	 * @param carry
	 * @param speed
	 */
	public boolean reCalculate(long carry, double speed) {
		this.carry = carry;
		this.speed = speed;
		if (this.carry <= this.currentGatherAmount) {
			return false;
		}
		double time = (this.carry - this.currentGatherAmount) / speed;
		this.endTime = this.updateTime + (long) Math.ceil(Double.valueOf(time)) * TimeUtil.SECONDS_MILLIS;
		return true;
	}

	public double getReaminGatherTime() {
		return (this.carry - this.currentGatherAmount) / speed;
	}
}
