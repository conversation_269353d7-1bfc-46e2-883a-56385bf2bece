package com.lc.billion.icefire.game.biz.service.impl;

import java.util.Iterator;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.internalmsg.login.ClosePlayerReason;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.player.PlayerLimitContext;
import com.lc.billion.icefire.game.biz.schedule.ScheduleOperation;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;
import com.lc.billion.icefire.game.biz.service.GameLimitService;
import com.lc.billion.icefire.game.biz.service.ScheduleService;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerSessionServiceImpl;

/**
 * 
 *
 * <AUTHOR>
 */
@Service
public class GameLimitServiceImpl implements GameLimitService {

	private static final Logger log = LoggerFactory.getLogger(GameLimitService.class);

	@Autowired
	private PlayerSessionServiceImpl playerSessionSrv;

	@Autowired
	private ScheduleService scheduleSrv;

	public void startService() {
		scheduleSrv.scheduleAtFixedRate(new ScheduleOperation() {

			@Override
			public void execute() {
				checkAll();
			}
		}, CHECK_INTERVAL, CHECK_INTERVAL);
	}

	@Override
	public void checkAll() {
		checkPlayerSessions();
	}

	@Override
	public void checkPlayerSessions() {
		long now = TimeUtil.getNow();

		Iterator<Player> playerSessionItr = playerSessionSrv.playersIterator();
		while (playerSessionItr.hasNext()) {
			Player p = playerSessionItr.next();
			checkPlayerSession(p, now);
		}
	}

	private void checkPlayerSession(Player p, long now) {
		PlayerLimitContext limitCtx = p.getLimitContext();

		// 检查应该关闭的连接
		// if (limitCtx.getCloseTime() != -1 && now >= limitCtx.getCloseTime() +
		// CLOSE_TIMEOUT) {
		// log.info("#####GameLimitServiceImpl.checkPlayerSession###p.close()
		// limitCtx.getCloseTime()={}", Utils.formatTime(limitCtx.getCloseTime(),
		// "yyyy-MM-dd HH:mm:ss"));
		// p.close();
		// log.error("close channel. 连接超时关闭. Player=" + p);
		// return;
		// }

		boolean keepAliveTimeout = false;
		if (now >= limitCtx.getLastKeepAliveTime() + KEEP_ALIVE_TIMEOUT) {
			keepAliveTimeout = true;
		}

		// 检查在线状态，预防卡号
		AbstractOnlineState onlineState = p.getOnlineState();
		int timeout = onlineState.getTimeout();// connected,logined，logouted-AbstractOnlineState.TIMEOUT_1(120s);enter，RECONNECTING-AbstractOnlineState.TIMEOUT_2(300s)；gameing-(-1s)
		if (timeout != -1) {
			// 如果不在Gaming状态
			if (!keepAliveTimeout) {
				timeout *= 2;
			}
			if (now >= limitCtx.getLastChangeOnlineStateTime() + timeout) {
				ErrorLogUtil.errorLog("GameLimit find player onlineState timeout.", "Player" ,p , "type" ,onlineState.getType());

				p.tryClose(null, ClosePlayerReason.TimeOutKickForNotGaming);
			}
		} else {
			// 如果是Gaming状态
			if (keepAliveTimeout) {
				p.tryClose(null, ClosePlayerReason.TimeOutKickForGaming);
			}
		}
	}

	@Override
	public boolean checkReconnect(Player player, long now) {
		// ReconnectCacheMessageSender sender = player.getReconnectCacheMsgSender();
		// if (sender != null && sender.size() > MAX_RECONNECT_CACHE_MSG_SIZE) {
		//// log.info("#####GameLimitServiceImpl.checkReconnect###player.close()");
		// player.close();
		//
		// log.info(
		// "GameLimit find player reconnect cache message is too many. Player={},
		// size={}",
		// player, sender.size());
		// return false;
		// }

		PlayerLimitContext limitCtx = player.getLimitContext();
		if (now >= limitCtx.getLastChangeOnlineStateTime() + RECONNECT_TIMEOUT) {

			player.tryClose(null, ClosePlayerReason.TimeOutKickForReconnect);

			log.info("close channel. GameLimit find player reconnect is timeout. Player={}", player);
			return false;
		}

		return true;
	}

}
