package com.lc.billion.icefire.game.biz.model.battlePass;

import com.lc.billion.icefire.game.annotation.MongoIndex;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * battle pass
 */
public class RoleBattlePass extends AbstractEntity implements IRolesEntity {
	private static final long serialVersionUID = -3236828646369128655L;

    @MongoId
    private Long id;
    @MongoIndex
    private long roleId;
    // BattlePassGroup 表的 groupId
    private String groupId;
    // 总共分数
	private long score;
    // 解锁了哪些列
    private final Map<Integer, BattlePassColumnType> columns = new HashMap<>();
    // 领取 行，列
    private final Map<Integer, Set<Integer>> rewardStates = new HashMap<>();
    // 循环宝箱领取数量
    private int loopBoxCount;
    // bp生成依赖的活动Id
    private String exInfo;
    // 开始时间
    private long startTime;
    // 结束时间
    private long endTime;
    // 上次刷新时间
    private long lastRefreshTime;

    @Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return roleId;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public Map<Integer, BattlePassColumnType> getColumns() {
        return columns;
    }

    public Map<Integer, Set<Integer>> getRewardStates() {
        return rewardStates;
    }

    public int getLoopBoxCount() {
        return loopBoxCount;
    }

    public void setLoopBoxCount(int loopBoxCount) {
        this.loopBoxCount = loopBoxCount;
    }

    public String getExInfo() {
        return exInfo;
    }

    public void setExInfo(String exInfo) {
        this.exInfo = exInfo;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    public void setLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }
}
