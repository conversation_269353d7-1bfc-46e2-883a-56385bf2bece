package com.lc.billion.icefire.gvgbattle.biz.service.rpc.impl;

import java.util.List;

import com.lc.billion.icefire.game.biz.manager.gvg.RZEGameDataVoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.rpc.service.gvg.IGVGControlRemoteGVGBattleService;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGControlRemoteGVGBattleServiceImpl implements IGVGControlRemoteGVGBattleService {

	// private static final Logger logger =
	// LoggerFactory.getLogger(GVGControlRemoteGVGBattleServiceImpl.class);

	@Autowired
	private GVGBattleDataVoManager gvgDataVoManager;
	@Autowired
	private RZEGameDataVoManager rzeGameDataVoManager;

	@Override
	public String broadcastGvgBattleServerDispatchRecord(GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo) {
		gvgDataVoManager.updateGvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecordVo);
		gvgDataVoManager.initBattleFieldTimeLine();
		return null;
	}

	@Override
	public String broadcastGVGActivity(ActivityVo activityVo) {
		gvgDataVoManager.updateActivityVo(activityVo);
		return null;
	}

	@Override
	public void broadcastGVGAllianceSignUpInfo(List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos) {
		gvgDataVoManager.updateGvgAllianceSignUpInfoVo(gvgAllianceSignUpInfoVos);
	}

	@Override
	public void broadcastRZEActivity(ActivityVo activityVo) {
		rzeGameDataVoManager.updateRzeActivityVo(activityVo);
	}

}
