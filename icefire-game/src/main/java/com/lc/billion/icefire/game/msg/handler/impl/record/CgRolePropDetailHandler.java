package com.lc.billion.icefire.game.msg.handler.impl.record;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropExtentionService;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropSource;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgRolePropDetail;
import com.lc.billion.icefire.protocol.GcRolePropDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * 查看玩家某个prop的详细组成
 * 
 * <AUTHOR>
 * @date 2020/11/13
 */
@Controller
public class CgRolePropDetailHandler extends CgAbstractMessageHandler<CgRolePropDetail> {

	@Autowired
	private PropExtentionService propExtentionService;

	@Override
	protected void handle(Role role, CgRolePropDetail message) {
		Map<PropSource, Double> propDetail = propExtentionService.getPropDetail(role.getPersistKey(), message.getPropId());
		if (propDetail == null) {
			return;
		}

		GcRolePropDetail gcInfo = new GcRolePropDetail();
		gcInfo.setPropId(message.getPropId());
		for (var entry : propDetail.entrySet()) {
			gcInfo.putToPropSourceDetail(entry.getKey().getSource().getValue(), entry.getValue());
		}
		role.send(gcInfo);
	}
}
