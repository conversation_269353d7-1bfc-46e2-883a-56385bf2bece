package com.lc.billion.icefire.game.biz.service.impl.expedition;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.BtlMapConfig;
import com.lc.billion.icefire.game.biz.config.DropGroupConfig;
import com.lc.billion.icefire.game.biz.config.InnerPVEConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExpeditionNumberGateDao;
import com.lc.billion.icefire.game.biz.manager.RoleHeroManager;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionBtlStatus;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionErrorCode;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionLevelType;
import com.lc.billion.icefire.game.biz.model.expedition.RoleExpeditionNumberGate;
import com.lc.billion.icefire.game.biz.model.hero.HeroBattleInfo;
import com.lc.billion.icefire.game.biz.model.item.ItemUtils;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.people.PeopleServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.unlock.UnlockServiceImpl;
import com.lc.billion.icefire.game.biz.service.rpc.impl.battle.BattleServiceImpl;
import com.lc.billion.icefire.game.biz.util.RandSeedUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.structure.PsExpeditionBtlVerify;
import com.lc.billion.icefire.protocol.structure.PsHeroEquip;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 倍增门关卡服务
 */
@Slf4j
@Service
public class ExpeditionNumberGateService {

    private static final int LINEUP_PLACEHOLDER = 0;
    private static final int MIN_SEED_INDEX = 1;

    @Autowired
    private DropServiceImpl dropService;
    @Autowired
    private ItemServiceImpl itemService;
    @Autowired
    private HeroServiceImpl heroService;
    @Autowired
    private RoleExpeditionNumberGateDao expeditionNumberGateDao;
    @Autowired
    private RoleHeroManager roleHeroManager;
    @Autowired
    private BattleServiceImpl battleService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private UnlockServiceImpl unlockService;
    @Autowired
    private MissionServiceImpl missionService;
    @Autowired
    private PeopleServiceImpl peopleService;
    @Autowired
    private ExpeditionValidator expeditionValidator;

    /**
     * 玩家进入世界时的初始化处理
     * 
     * @param role 玩家角色
     */
    public void onEnterWorld(Role role) {
        var expedition = getOrCreateExpedition(role);
        var nextLevel = getNextLevel(expedition);
        role.send(createExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup()));
        sendInnerFinishedIds(role);
        // 重新登录时重置状态 - 使用规范化方法
        expedition.forceReset("Player re-login");
    }

    /**
     * 获取或创建倍增门数据
     * 
     * @param role 玩家角色
     * @return 倍增门数据
     */
    private RoleExpeditionNumberGate getOrCreateExpedition(Role role) {
        return Optional.ofNullable(expeditionNumberGateDao.findById(role.getRoleId()))
                .orElseGet(() -> {
                    var expedition = expeditionNumberGateDao.create(role);
                    expedition.setSeed(RandSeedUtil.rand(role.getRoleId()).seed);
                    expeditionNumberGateDao.save(expedition);
                    return expedition;
                });
    }

    /**
     * 创建倍增门信息响应
     * 
     * @param level 关卡等级
     * @param seed 随机种子
     * @param heroIds 英雄ID列表
     * @return 倍增门信息响应
     */
    private GcExpeditionNumberGateInfo createExpeditionInfo(int level, long seed, List<Integer> heroIds) {
        return new GcExpeditionNumberGateInfo()
                .setLevel(level)
                .setSeed(seed)
                .setLineup(heroIds);
    }

    /**
     * 检查玩家是否通过指定关卡
     * 
     * @param role 玩家角色
     * @param level 关卡等级
     * @return 是否通过
     */
    public boolean isPassed(Role role, int level) {
        return Optional.ofNullable(expeditionNumberGateDao.findById(role.getRoleId()))
                .map(expedition -> expedition.getLevel() >= level)
                .orElse(false);
    }

    /**
     * 内城关卡等级更新，GM专用
     *
     * @param role
     * @param targetLevel
     */
    public void updateInnerLevel(Role role, int targetLevel) {
        var innerPVEConfig = configService.getConfig(InnerPVEConfig.class);
        var config = innerPVEConfig.getMeta(targetLevel);
        if (config == null) {
            role.send(createInnerRewardResponse(PsErrorCode.EXPEDITION_INNER_ERROR, Lists.newArrayList(), List.of(targetLevel)));
            return;
        }
        var expedition = getOrCreateExpedition(role);
        var levelIds = new ArrayList<Integer>();
        for (int i = 1; i <= targetLevel; i++) {
            if (expedition.getInnerReceivedIds().contains(i)) {
                continue;
            }
            levelIds.add(i);
        }

        processAndSendInnerRewards(role, expedition, levelIds);

    }

    /**
     * 倍增门关卡等级更新，GM专用
     * @param role
     * @param level
     */
    public void updateLevel(Role role, int level) {
        var expedition = getOrCreateExpedition(role);

        if (expedition.getLevel() >= level) {
            ErrorLogUtil.errorLog("ExpeditionNumberGateService update targetLevel error",
                    "new", level, "old", expedition.getLevel());
            return;
        }

        expedition.setLevel(level);
        expeditionNumberGateDao.save(expedition);

        var nextLevel = getNextLevel(expedition);
        role.send(createExpeditionInfo(nextLevel, expedition.getSeed(), List.of()));

        onLevelUpdate(role, level);
    }


    /**
     * 关卡变化后的处理逻辑
     * 
     * @param role 玩家角色
     * @param level 新关卡等级
     */
    private void onLevelUpdate(Role role, int level) {
        var levelMeta = Optional.ofNullable(configService.getConfig(BtlMapConfig.class).getMetaById(level))
                .orElseThrow(() -> {
                    ErrorLogUtil.errorLog("chapter targetLevel is invalid", "roleId", role.getRoleId(), "level", level);
                    return new IllegalArgumentException("Invalid level: " + level);
                });

        // 发送奖励
        var drops = processLevelReward(role, Integer.parseInt(levelMeta.getRewardId()));
        sendLevelReward(role, level, drops);
        
        // 触发相关业务逻辑
        triggerLevelUpdateEvents(role, level);
    }

    /**
     * 处理关卡奖励
     * 
     * @param role 玩家角色
     * @param rewardId 奖励ID
     * @return 奖励物品列表
     */
    private List<SimpleItem> processLevelReward(Role role, int rewardId) {
        return Optional.ofNullable(configService.getConfig(DropGroupConfig.class).get(String.valueOf(rewardId)))
                .map(dropMeta -> {
                    var drops = DropServiceImpl.dropsMerge(dropService.drop(dropMeta));
                    itemService.give(role, drops, LogReasons.ItemLogReason.NUMBER_GATE_BATTER_WIN_REWARD);
                    return drops;
                })
                .orElse(Lists.newArrayList());
    }

    /**
     * 发送关卡奖励消息
     * 
     * @param role 玩家角色
     * @param level 关卡等级
     * @param drops 奖励物品
     */
    private void sendLevelReward(Role role, int level, List<SimpleItem> drops) {
        var rewardMsg = new GcExpeditionReward()
                .setLevel(level)
                .setItems(ItemUtils.toPsSimpleItem(drops));
        role.send(rewardMsg);
    }

    /**
     * 触发关卡更新相关事件
     * 
     * @param role 玩家角色
     * @param level 关卡等级
     */
    private void triggerLevelUpdateEvents(Role role, int level) {
        // 功能解锁更新
        unlockService.onExpeditionNumberGateLevelChanged(role, level);

        // 任务更新
        missionService.onMissionFinish(role, MissionType.CHAPTER_LEVEL_UPDATE, level);

    }

    /**
     * 创建战斗结果响应
     * 
     * @param errorCode 错误码
     * @param isWin 是否胜利
     * @param nextLevel 下一关卡
     * @param drops 奖励物品
     * @return 战斗结果响应
     */
    private GcExpeditionNumberGateBtlResult createBattleResult(ExpeditionErrorCode errorCode, boolean isWin,
                                                   int nextLevel, List<SimpleItem> drops) {
        return new GcExpeditionNumberGateBtlResult()
                .setErrorCode(errorCode.getId())
                .setIsWin(isWin)
                .setNextLevel(nextLevel)
                .setItems(ItemUtils.toPsSimpleItem(drops));
    }


    /**
     * 验证随机种子
     * 
     * @param expedition 倍增门数据
     * @param seed 客户端种子
     * @param seedIndex 种子索引
     * @return 是否有效
     */
    private boolean isValidSeed(RoleExpeditionNumberGate expedition, long seed, int seedIndex) {
        if (seedIndex < MIN_SEED_INDEX) {
            return false;
        }

        for (int i = 0; i < seedIndex; i++) {
            flushRandSeed(expedition);
        }
        expeditionNumberGateDao.save(expedition);
        return expedition.getSeed() == seed;
    }


    /**
     * 刷新随机种子
     * 
     * @param expedition 倍增门数据
     */
    private void flushRandSeed(RoleExpeditionNumberGate expedition) {
        var rand = RandSeedUtil.rand(expedition.getSeed());
        expedition.setSeed(rand.seed);
    }

    /**
     * 开始战斗验证
     * 
     * @param role 玩家角色
     * @param targetLevel 目标关卡
     * @param data 战斗验证数据
     * @return 是否成功开始
     */
    public boolean btlStart(Role role, int targetLevel, PsExpeditionBtlVerify data) {
        var expedition = getExpeditionForBattle(role, targetLevel);
        if (expedition.isEmpty()) {
            return false;
        }

        var expeditionData = expedition.get();
        
        // 验证战斗状态
        if (!validateBattleStatus(role, expeditionData, targetLevel)) {
            return false;
        }

        // 验证关卡等级
        var nextLevel = getNextLevel(expeditionData);
        if (!validateTargetLevel(role, targetLevel, nextLevel)) {
            expeditionData.forceReset("Target level validation failed");
            role.send(createBattleResult(ExpeditionErrorCode.PARAMETER_ERROR, false, nextLevel, Lists.newArrayList()));
            return false;
        }

        // 处理剧情关卡
        if (isStoryLevel(targetLevel)) {
            return processStoryLevel(role, expeditionData, targetLevel);
        }

        // 处理战斗关卡
        return processBattleLevel(role, expeditionData, targetLevel, nextLevel, data);
    }

    /**
     * 获取用于战斗的倍增门数据
     */
    private Optional<RoleExpeditionNumberGate> getExpeditionForBattle(Role role, int targetLevel) {
        var expedition = expeditionNumberGateDao.findById(role.getRoleId());
        if (expedition == null) {
            role.send(createBattleResult(ExpeditionErrorCode.EXPEDITION_NOT_FOUND, false, targetLevel, Lists.newArrayList()));
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 未找到关卡", "roleId", role.getRoleId(), "level", targetLevel);
            return Optional.empty();
        }
        return Optional.of(expedition);
    }

    /**
     * 验证战斗状态
     */
    private boolean validateBattleStatus(Role role, RoleExpeditionNumberGate expedition, int targetLevel) {
        int currentStatus = expedition.getCurrentStatus();

        // 如果是END状态，尝试重置为INIT
        if (currentStatus == ExpeditionBtlStatus.END &&
                expedition.tryTransition(ExpeditionBtlStatus.END, ExpeditionBtlStatus.INIT)) {
            currentStatus = ExpeditionBtlStatus.INIT;
        }

        if (currentStatus != ExpeditionBtlStatus.INIT) {
            // 记录异常状态
            ErrorLogUtil.errorLog("战斗状态异常",
                    "roleId", role.getRoleId(),
                    "currentStatus", currentStatus,
                    "targetLevel", targetLevel);
            role.send(createBattleResult(ExpeditionErrorCode.LAST_BATTLE_NOT_FINISHED, false, targetLevel, Lists.newArrayList()));
            return false;
        }

        return true;
    }

    /**
     * 获取下一关卡等级
     */
    private int getNextLevel(RoleExpeditionNumberGate expedition) {
        return configService.getConfig(BtlMapConfig.class).getNextLevel(expedition.getLevel());
    }

    /**
     * 验证目标关卡
     */
    private boolean validateTargetLevel(Role role, int targetLevel, int nextLevel) {
        if (nextLevel != targetLevel) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 关卡不一致",
                    "roleId", role.getRoleId(), "clientLevel", targetLevel, "serverLevel", nextLevel);
            return false;
        }
        return true;
    }

    /**
     * 检查是否为剧情关卡
     */
    private boolean isStoryLevel(int targetLevel) {
        var levelType = configService.getConfig(BtlMapConfig.class).getLevelType(targetLevel);
        return levelType == ExpeditionLevelType.STORY;
    }

    /**
     * 处理剧情关卡
     */
    private boolean processStoryLevel(Role role, RoleExpeditionNumberGate expedition, int targetLevel) {
        flushRandSeed(expedition);
        // 直接处理剧情关卡完成逻辑
        processStoryLevelComplete(role, expedition, targetLevel);
        expeditionNumberGateDao.save(expedition);
        return true;
    }

    /**
     * 处理剧情关卡完成
     */
    private void processStoryLevelComplete(Role role, RoleExpeditionNumberGate expedition, int targetLevel) {
        var levelConfig = configService.getConfig(BtlMapConfig.class);
        var nextLevel = levelConfig.getNextLevel(targetLevel);

        // 处理战斗结果（剧情关卡始终胜利）
        var drops = processBattleResult(role, expedition, targetLevel, true, levelConfig);

        role.send(createBattleResult(ExpeditionErrorCode.NORMAL, true, nextLevel, drops));

        // 触发相关事件
        triggerLevelUpdateEvents(role, targetLevel);

        // 剧情关卡完成后状态保持INIT，可以直接进行下一场战斗
        log.debug("Story level {} completed for role {}, status remains INIT", targetLevel, role.getRoleId());
    }

    /**
     * 处理战斗关卡
     */
    private boolean processBattleLevel(Role role, RoleExpeditionNumberGate expedition, int targetLevel,
                                       int nextLevel, PsExpeditionBtlVerify data) {
        if (data == null) {
            role.send(createBattleResult(ExpeditionErrorCode.PARAMETER_ERROR, false, targetLevel, Lists.newArrayList()));
            return false;
        }

        // 验证阵容
        if (!expeditionValidator.isValidLineup(role, data.getLineup())) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 阵容不合规", "client", data.getLineup());
            role.send(createBattleResult(ExpeditionErrorCode.LINEUP_ERROR, false, nextLevel, Lists.newArrayList()));
            expedition.forceReset("Lineup validation failed");
            return false;
        }

        // 验证随机种子
        long oldSeed = expedition.getSeed();
        if (!isValidSeed(expedition, data.getData().getRandseed(), data.getSeedIndex())) {
            handleSeedValidationFailure(role, expedition, data, nextLevel, oldSeed);
            return false;
        }

        // 开始战斗
        return startBattle(role, expedition, targetLevel, data);
    }

    /**
     * 处理种子验证失败
     */
    private void handleSeedValidationFailure(Role role, RoleExpeditionNumberGate expedition,
                                           PsExpeditionBtlVerify data, int nextLevel, long oldSeed) {
        ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 随机种子不一致",
                "roleId", role.getRoleId(),
                "client", data.getData().getRandseed(),
                "server", expedition.getSeed(),
                "old", oldSeed,
                "index", data.getSeedIndex());
        
        role.send(createBattleResult(ExpeditionErrorCode.RAND_SEED_NOT_SAME, false, nextLevel, Lists.newArrayList()));
        role.send(createExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup()));
        expedition.forceReset("Seed validation failed");
    }

    /**
     * 开始战斗流程
     */
    private boolean startBattle(Role role, RoleExpeditionNumberGate expedition, int targetLevel, PsExpeditionBtlVerify data) {
        int recordId = expedition.getNewRecordId();
        var battleData = prepareBattleData(role, data.getLineup());
        
        expedition.setLineup(data.getLineup());

        if (!sendBattleRequest(role, expedition, recordId, targetLevel, battleData, data)) {
            role.send(createBattleResult(ExpeditionErrorCode.TIME_OUT, false, targetLevel, Lists.newArrayList()));
            expedition.forceReset("Battle request failed");
            ErrorLogUtil.errorLog("CgExpeditionGateBtlVerifyHandler: 战斗校验异常", "roleId", role.getRoleId());
        }
        
        expeditionNumberGateDao.save(expedition);
        return true;
    }

    /**
     * 准备战斗数据
     */
    private record BattleData(Map<String, HeroBattleInfo> heroMap, Map<String, PsHeroEquip> equipMap) {}

    private BattleData prepareBattleData(Role role, List<Integer> lineup) {
        Map<String, HeroBattleInfo> heroMap = new HashMap<>();
        Map<String, PsHeroEquip> equipMap = new HashMap<>();
        
        lineup.stream()
                .filter(heroId -> heroId != LINEUP_PLACEHOLDER)
                .forEach(heroId -> {
                    var hero = heroService.getHero(role, heroId.toString());
                    if (hero != null) {
                        double lordSkillAddition = roleHeroManager.lordSkillAddition(role, hero);
                        heroMap.put(String.valueOf(heroId), HeroOutput.toBattleInfo(hero, lordSkillAddition));
                        
                        var equips = heroService.getHeroEquips(hero);
                        equips.forEach(equip -> {
                            var psEquip = new PsHeroEquip(
                                    equip.getId().toString(),
                                    equip.getMetaId(),
                                    equip.getHeroId().toString(),
                                    equip.getExps(),
                                    equip.getLevel());
                            psEquip.setCfgId(equip.getMetaId());
                            psEquip.setForge(equip.getForge());
                            equipMap.put(equip.getId().toString(), psEquip);
                        });
                    }
                });
        
        return new BattleData(heroMap, equipMap);
    }

    /**
     * 发送战斗请求
     */
    private boolean sendBattleRequest(Role role, RoleExpeditionNumberGate expedition, int recordId, int level,
                                      BattleData battleData, PsExpeditionBtlVerify data) {
        try {
            if (expedition.tryTransition(ExpeditionBtlStatus.INIT, ExpeditionBtlStatus.WAITING)) {
                JSONObject obj = new JSONObject();
                obj.put("roleId", role.getRoleId());
                obj.put("recordId", recordId);
                obj.put("level", level);
                obj.put("heroes", battleData.heroMap());
                obj.put("verifyData", data);
                obj.put("equipMap", battleData.equipMap());
                
                battleService.expeditionVerify(obj.toJSONString(), resp -> {
                    if (resp != null) {
                        log.info("CgExpeditionNumberGateBtlVerifyHandler: expeditionVerify success: {}", resp);
                        if (!resp.isWin()) {
                            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: expeditionVerify error", "description", obj.toJSONString());
                        }
                        onBattleEnd(role, resp.getRecordId(), resp.getLevel(), resp.isWin(), false, resp.isAbTest());
                    } else {
                        handleBattleTimeout(role, expedition, level);
                    }
                });
                return true;
            }
            return false;
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("CgExpeditionNumberGateBtlVerifyHandler: expeditionVerify error", e);
            }
            return false;
        }
    }

    /**
     * 处理战斗超时
     */
    private void handleBattleTimeout(Role role, RoleExpeditionNumberGate expedition, int level) {
        role.send(createBattleResult(ExpeditionErrorCode.TIME_OUT, false, level, Lists.newArrayList()));
        expedition.forceReset("Battle timeout");
        ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 战斗校验异常", "roleId", role.getRoleId());
    }

    /**
     * 异步战斗结束处理
     * 
     * @param role 玩家角色
     * @param recordId 记录ID
     * @param targetLevel 目标关卡
     * @param isWin 是否胜利
     * @param isSkip 是否跳过
     * @param isAbTest 是否AB测试
     */
    public void onBattleEnd(Role role, int recordId, int targetLevel, boolean isWin, boolean isSkip, boolean isAbTest) {
        var expedition = expeditionNumberGateDao.findById(role.getRoleId());
        if (expedition == null) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: onEnd 未找到数据", "roleId", role.getRoleId(), "recordId", recordId);
            return;
        }

        // 验证战斗记录
        if (!validateBattleRecord(expedition, recordId, targetLevel)) {
            return;
        }

        // 验证当前状态，必须是WAITING
        if (expedition.getCurrentStatus() != ExpeditionBtlStatus.WAITING) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: 状态异常，期望WAITING",
                    "roleId", role.getRoleId(),
                    "currentStatus", expedition.getCurrentStatus(),
                    "recordId", recordId);
            expedition.forceReset("Invalid status when battle end");
            return;
        }

        var levelConfig = configService.getConfig(BtlMapConfig.class);
        var nextLevel = levelConfig.getNextLevel(targetLevel);

        var drops = processBattleResult(role, expedition, targetLevel, isWin, levelConfig);
        role.send(createBattleResult(ExpeditionErrorCode.NORMAL, isWin, nextLevel, drops));

        // 保存数据变更
        expeditionNumberGateDao.save(expedition);

        // 触发相关事件（只在胜利时）
        if (isWin) {
            //TODO bilog
            triggerLevelUpdateEvents(role, targetLevel);
        }

        // 最后转换状态到END，确保玩家收到结果后才能发起新的战斗
        if (!expedition.tryTransition(ExpeditionBtlStatus.WAITING, ExpeditionBtlStatus.END)) {
            log.warn("Failed to transition to END status for role {}, current status: {}",
                    role.getRoleId(), expedition.getCurrentStatus());
            expedition.status.set(ExpeditionBtlStatus.END);
        }
    }

    /**
     * 验证战斗记录
     */
    private boolean validateBattleRecord(RoleExpeditionNumberGate expedition, int recordId, int targetLevel) {
        if (expedition.getRecordId() != recordId) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: onEnd recordId 不一致",
                    "roleId", expedition.getRoleId(), "recordId", recordId);
            return false;
        }

        var nextLevel = getNextLevel(expedition);
        if (nextLevel != targetLevel) {
            ErrorLogUtil.errorLog("CgExpeditionNumberGateBtlVerifyHandler: onEnd targetLevel 不一致",
                    "roleId", expedition.getRoleId(), "targetLevel", targetLevel, "nextLevel", nextLevel);
            return false;
        }
        return true;
    }

    /**
     * 处理战斗结果
     */
    private List<SimpleItem> processBattleResult(Role role, RoleExpeditionNumberGate expedition, int targetLevel,
                                                 boolean isWin, BtlMapConfig levelConfig) {
        List<SimpleItem> drops = Lists.newArrayList();
        
        if (isWin) {
            drops = processVictoryReward(role, targetLevel, levelConfig);
            expedition.setLevel(targetLevel);
            triggerVictoryEvents(role, targetLevel);
        }
        
        return drops;
    }

    /**
     * 处理胜利奖励
     */
    private List<SimpleItem> processVictoryReward(Role role, int targetLevel, BtlMapConfig levelConfig) {
        return Optional.ofNullable(levelConfig.getMetaById(targetLevel))
                .map(levelMeta -> processLevelReward(role, Integer.parseInt(levelMeta.getRewardId())))
                .orElse(Lists.newArrayList());
    }

    /**
     * TODO 触发胜利相关事件
     */
    private void triggerVictoryEvents(Role role, int targetLevel) {
        // 功能解锁
        unlockService.onExpeditionNumberGateLevelChanged(role, targetLevel);
    }


    /**
     * 创建内部奖励响应
     * 
     * @param code 错误码
     * @param items 奖励物品
     * @param levelIds 关卡ID列表
     * @return 内部奖励响应
     */
    public GcExpeditionInnerReward createInnerRewardResponse(PsErrorCode code, List<SimpleItem> items, List<Integer> levelIds) {
        return new GcExpeditionInnerReward()
                .setCode(code)
                .setItems(ItemUtils.toPsSimpleItem(items))
                .setLevelIds(levelIds);
    }

    /**
     * 获取内部奖励
     * 
     * @param role 玩家角色
     * @param levelIds 关卡ID列表
     */
    public void getInnerReward(Role role, List<Integer> levelIds) {
        var expedition = expeditionNumberGateDao.findById(role.getRoleId());
        if (expedition == null) {
            role.send(createInnerRewardResponse(PsErrorCode.EXPEDITION_INNER_ERROR, Lists.newArrayList(), levelIds));
            return;
        }

        var validationResult = validateInnerRewards(role, expedition, levelIds);
        if (validationResult.isPresent()) {
            role.send(createInnerRewardResponse(validationResult.get(), Lists.newArrayList(), levelIds));
            return;
        }

        processAndSendInnerRewards(role, expedition, levelIds);
    }

    /**
     * 验证内部奖励
     */
    private Optional<PsErrorCode> validateInnerRewards(Role role, RoleExpeditionNumberGate expedition, List<Integer> levelIds) {
        var innerPVEConfig = configService.getConfig(InnerPVEConfig.class);
        
        for (var levelId : levelIds) {
            var config = innerPVEConfig.getMeta(levelId);
            if (config == null) {
                return Optional.of(PsErrorCode.EXPEDITION_INNER_ERROR);
            }
            if (expedition.getLevel() < config.getExpeditionLevelId()) {
                return Optional.of(PsErrorCode.EXPEDITION_INNER_REWARD_UNCLIMABLE);
            }
            if (role.getLevel() < config.getUnlockLevelLimit()) {
                return Optional.of(PsErrorCode.EXPEDITION_INNER_REWARD_UNCLIMABLE);
            }
            if (expedition.getInnerReceivedIds().contains(levelId)) {
                return Optional.of(PsErrorCode.EXPEDITION_INNER_REWARD_RECEIVED);
            }

        }
        return Optional.empty();
    }

    /**
     * 处理内部奖励
     */
    private List<SimpleItem> processInnerRewards(List<Integer> levelIds) {
        var innerPVEConfig = configService.getConfig(InnerPVEConfig.class);
        List<SimpleItem> items = Lists.newArrayList();
        
        levelIds.forEach(levelId -> {
            var dropGroupId = innerPVEConfig.getDropGroupId(levelId);
            if (!dropGroupId.isEmpty()) {
                items.addAll(DropServiceImpl.dropsMerge(dropService.drop(dropGroupId)));
            }
        });
        return items;
    }

    /**
     * 处理内城奖励发放
     *
     * @param role       玩家角色
     * @param expedition 远征数据
     * @param levelIds   关卡ID列表
     */
    private void processAndSendInnerRewards(Role role, RoleExpeditionNumberGate expedition, List<Integer> levelIds) {
        // 处理奖励发放
        var items = processInnerRewards(levelIds);
        itemService.give(role, items, LogReasons.ItemLogReason.UNLOCK_INNER_REWARD);

        // 更新已接收状态
        expedition.getInnerReceivedIds().addAll(levelIds);
        expeditionNumberGateDao.save(expedition);

        // 发送响应和触发事件
        role.send(createInnerRewardResponse(PsErrorCode.SUCCESS, items, levelIds));
        sendInnerFinishedIds(role);
        triggerInnerRewardEvents(role, levelIds);
    }

    /**
     * 触发内城PVE奖励相关事件
     */
    private void triggerInnerRewardEvents(Role role, List<Integer> levelIds) {
        // 触发任务变更
        missionService.onMissionFinish(role, MissionType.REVENDICATION_INNER_REWARD, role);
        // 触发收复地块的 来人逻辑
        peopleService.onTriggerBorn(role, PeopleBornConditionType.REVENDICATION_INNER_REWARD, levelIds);
        // 功能解锁
        unlockService.onInnerLevelChanged(role, getInnerLevel(role));
    }

    /**
     * 发送内部已完成ID列表
     * 
     * @param role 玩家角色
     */
    public void sendInnerFinishedIds(Role role) {
        var expedition = expeditionNumberGateDao.findById(role.getRoleId());
        var msg = new GcExpeditionInnerInfo()
                .setFinishedLevelIds(expedition == null ? List.of() : expedition.getInnerReceivedIds());
        role.send(msg);
    }

    /**
     * 发送倍增门信息
     * 
     * @param role 玩家角色
     */
    public void sendExpeditionGateInfo(Role role) {
        Optional.ofNullable(expeditionNumberGateDao.findById(role.getRoleId()))
                .ifPresent(expedition -> {
                    var nextLevel = getNextLevel(expedition);
                    role.send(createExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup()));
                });
    }



    /**
     * 获取玩家内部奖励等级
     * 
     * @param role 玩家角色
     * @return 内部奖励等级
     */
    public int getInnerLevel(Role role) {
        return Optional.ofNullable(expeditionNumberGateDao.findById(role.getRoleId()))
                .map(expedition -> expedition.getInnerReceivedIds().stream()
                        .max(Integer::compareTo)
                        .orElse(0))
                .orElse(0);
    }

    /**
     * 检查是否达到指定内城等级
     *
     * @param role  玩家角色
     * @param level 关卡等级
     * @return 是否达到
     */
    public boolean isReachInnerLevel(Role role, int level) {
        if (level <= 0) {
            return true;
        }
        return getInnerLevel(role) >= level;
    }
}
