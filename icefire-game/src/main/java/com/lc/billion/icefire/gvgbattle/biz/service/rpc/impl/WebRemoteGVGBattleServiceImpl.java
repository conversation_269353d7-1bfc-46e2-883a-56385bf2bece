package com.lc.billion.icefire.gvgbattle.biz.service.rpc.impl;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleService;
import com.longtech.ls.rpc.IWebRemoteGVGBattleService;

/**
 * <AUTHOR>
 *
 */
@Service
public class WebRemoteGVGBattleServiceImpl implements IWebRemoteGVGBattleService {
	private static final Logger logger = LoggerFactory.getLogger(WebRemoteGVGBattleServiceImpl.class);

	@Autowired
	private GVGBattleService gvgBattleService;

	@Override
	public JSONObject battleServerEnd(int battleServerId) {
		try {
			JSONObject ret = new JSONObject();
			gvgBattleService.battleServerEnd();
			ret.put("ret", 0);
			return ret;
		}catch (Exception e){
			if(!(e instanceof ExpectedException)){
				ErrorLogUtil.exceptionLog("WebRemoteGVGBattleServiceImpl battleServerEnd 异常", e);
			}
			JSONObject ret = new JSONObject();
			ret.put("ret", 2);
			return ret;
		}
	}
}
