package com.lc.billion.icefire.game.biz.model.official;

import com.lc.billion.icefire.protocol.structure.PsOfficialSkill;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName OfficialSkill
 * @Description 技能信息
 * <AUTHOR>
 * @Date 2024/8/9 18:17
 * @Version 1.0
 */
@Getter
@Setter
public class OfficialSkill {

    /**
     * 技能Id
     */
    private String metaId;
    /**
     * 下次可使用时间
     */
    private long availableTime;

    /**
     * 剩余使用次数
     */
    private int remainTimes;

    public PsOfficialSkill toPsOfficialSkill() {
        PsOfficialSkill psOfficialSkill = new PsOfficialSkill();
        psOfficialSkill.setMetaId(metaId);
        psOfficialSkill.setRemainTimes(remainTimes);
        psOfficialSkill.setAvailableTime(availableTime);
        return psOfficialSkill;
    }
}
