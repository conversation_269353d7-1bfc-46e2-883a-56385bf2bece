package com.lc.billion.icefire.game.msg.handler.impl.activity;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.activity.PlayerActivityExport;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgActivityReceiveReward;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgActivityReceiveRewardHandler extends CgAbstractMessageHandler<CgActivityReceiveReward> {

	@Autowired
	private PlayerActivityExport activityExport;

	@Override
	public void handle(Role role, CgActivityReceiveReward message) {
		activityExport.receiveActivityReward(role, message.getActivityTypeMetaId(), message.getGoalSn());
	}

}
