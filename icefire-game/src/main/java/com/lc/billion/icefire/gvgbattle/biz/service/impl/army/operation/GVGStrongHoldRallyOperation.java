package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/20
 */
@Service
public class GVGStrongHoldRallyOperation extends AbstractGvgFightOperation {
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.RALLY_STRONGHOLD;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		logger.info("[GVG]armyArrive, army: {}", army.getPersistKey());
		try {
			if (gvgStrongHoldService.attackerArriveStrongHold(army)) {
				logger.info("[GVG]armyArrive, rally begin fight: {}", army.getPersistKey());
				super.armyArrive(army);
			}
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("[GVG]armyArrive error", e);
			armyManager.returnArmy(army);
		}
	}

	@Override
	public void finishWork(ArmyInfo army) {
		super.finishWork(army);

		// 遣返进攻方部队
		if(!army.getFightContext().isWin()) { // 没打赢直接回城
			logger.info("[GVG]官渡 集结进攻官渡建筑失败 队列返回 玩家: {} 部队: {}",army.getRoleId(), army.getPersistKey());
			armyManager.returnArmy(army);
		}

		logger.info("[GVG]finishWork, army: {}", army.getPersistKey());
		armyManager.marchRetBILog(army);
	}

	@Override
	protected void endBattle(ArmyInfo army) {
		logger.info("[GVG]endBattle, army: {}", army.getPersistKey());
		gvgStrongHoldService.onStrongHoldBattleEnd(army);
	}
}
