package com.lc.billion.icefire.game.biz.manager;

import com.lc.billion.icefire.game.biz.config.AllianceConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.alliances.AllianceTechDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleAllianceRecordDao;
import com.lc.billion.icefire.game.biz.model.alliance.RoleAllianceRecord;
import com.lc.billion.icefire.game.biz.model.alliance.tech.AllianceTech;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.tick.TickService;
import com.lc.billion.icefire.game.biz.service.impl.tick.TickType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by 周会勤 on 2019/06/25. Copyright © 2019 周会勤. All rights reserved.
 */
@Component
public class AllianceTechManager implements IRoleAllianceManager {

	@Autowired
	private AllianceTechDao allianceTechDao;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private TickService tickService;

    @Autowired
    private RoleExtraManager roleExtraManager;

    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private RoleAllianceRecordDao roleAllianceRecordDao;

	public AllianceTech getAllianceTech(Long allianceId, int groupId) {
		AllianceTech allianceTech = allianceTechDao.findAllianceTechsByAllianceIdAndGroup(allianceId, groupId);
		return allianceTech;
	}

	public Map<Integer, AllianceTech> getTechMap(Long allianceId) {
		Map<Integer, AllianceTech> allianceTechMap = allianceTechDao.findAllianceTechMap(allianceId);
		return allianceTechMap;
	}

	@Override
	public void onAllianceJoin(Long roleId, Long allianceId) {
		Role role = roleManager.getRole(roleId);
		if (role != null) {
			tickService.initTickEntity(role, TickType.ALLIANCETECHREDPOINT);
		}

        // 需求：玩家第一次加入联盟时捐献次数是满的  @策划 曹帅
        RoleAllianceRecord roleAllianceRecord = roleAllianceRecordDao.findById(roleId);
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(roleId);
        if (roleExtra.isFirstJoinAlliance()) {
            int max = srvDpd.getConfigService().getConfig(AllianceConfig.class).getTechnologyDonateLimit();
            roleAllianceRecord.setCanDonateNum(max);
        }
        // 需求 end
	}

	@Override
	public void onAllianceLeave(Long roleId, Long allianceId) {

	}

	@Override
	public void onLegionJoin(Long allianceId, Long legionId) {
		// TODO Auto-generated method stub

	}

	@Override
	public void onLegionLeave(Long allianceId, Long legionId) {
		// TODO Auto-generated method stub

	}

	public int getTechSumLevel(Long allianceId) {
		int sumLevel = 0;
		Map<Integer, AllianceTech> allianceTechMap = allianceTechDao.findAllianceTechMap(allianceId);
		for (AllianceTech tech : allianceTechMap.values()) {
			sumLevel += tech.getLevel();
		}
		return sumLevel;
	}

}
