package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * <AUTHOR>
 * @date 2020/12/24
 */
@Service
public class GVGStrongHoldGarrisonOperation implements ArmyOperation {
	private static final Logger logger = LoggerFactory.getLogger(GVGStrongHoldGarrisonOperation.class);
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GARRISON_STRONGHOLD;
	}


	@Override
	public void armyArrive(ArmyInfo army) {
		logger.info("[GVG]armyArrive, army: {}", army.getPersistKey());
		try {
			gvgStrongHoldService.garrisonStrongHold(army);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("GVG 驻防建筑,行军到达异常", e);
			armyManager.returnArmy(army);
		}
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		logger.info("[GVG]returnArrived, army: {}", army.getPersistKey());
		armyManager.takeBackArmy(army);
	}

	@Override
	public void finishWork(ArmyInfo army) {

	}
}
