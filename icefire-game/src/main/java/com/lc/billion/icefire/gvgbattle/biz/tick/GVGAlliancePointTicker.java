package com.lc.billion.icefire.gvgbattle.biz.tick;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGAlliancePointTicker extends AbstractTicker<AllianceBattlePoint> {

	@Autowired
	private AllianceBattlePointDao allianceBattlePointDao;
	@Autowired
	private GVGBattleService gvgBattleService;
	@Autowired
	private GVGBattleDataVoManager gvgBattleDataVoManager;
	@Autowired
	private ServiceDependency srvDpd;
	@Autowired
	private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;

	public GVGAlliancePointTicker() {
		super(1 * TimeUtil.SECONDS_MILLIS);
	}

	@Override
	protected void tick(AllianceBattlePoint allianceBattlePoint, long now) {

		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		if (gvgBattleFieldTimeLine.isEndFlag()) {
			return;
		}
		GvgSettingConfig gvgSettingConfig = srvDpd.getConfigService().getConfig(GvgSettingConfig.class);
		int max = gvgSettingConfig.getGvgWinPoint();
		if (allianceBattlePoint.getPointSpeed() != 0 && allianceBattlePoint.getEndTime() < now) {
			logger.info("[GVG] tick needRecount alliance: {} endTime: {}", allianceBattlePoint.getPersistKey(), allianceBattlePoint.getEndTime());
			gvgBattleService.recountAlliancePoint(allianceBattlePoint.getPersistKey(),true);
		}

		if(allianceBattlePoint.getValue() - allianceBattlePoint.getLastBiValue() >= gvgSettingConfig.getBIAlliancePointInterval()){
			int teamId = gvgBattleDataVoManager.getTeamIdIdByAllianceId(allianceBattlePoint.getPersistKey());
			srvDpd.getBiLogUtil().tvtTeamScoreChange(teamId, allianceBattlePoint.getValue(), allianceBattlePoint.getPointSpeed());
			allianceBattlePoint.setLastBiValue(allianceBattlePoint.getValue());
			allianceBattlePointDao.save(allianceBattlePoint);
		}

		if (allianceBattlePoint.getValue() >= max) {
			logger.info("[GVG] tick winToStop winAlliance: {} allianceScore: {}, winScore: {}", allianceBattlePoint.getPersistKey(), allianceBattlePoint.getValue(), max);
			gvgBattleService.battleStop();
		}
	}

	@Override
	protected Collection<AllianceBattlePoint> findAll() {
		return allianceBattlePointDao.findAll();
	}

}
