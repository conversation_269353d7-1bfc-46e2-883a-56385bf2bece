package com.lc.billion.icefire.gvgbattle.biz.async.gvg;

import com.lc.billion.icefire.game.biz.async.AsyncOperation;

/**
 * <AUTHOR>
 *
 */
public abstract class GVGBattleAsyncOperation implements AsyncOperation {

	/**
	 * 是否同步执行
	 */
	private boolean synchronize;

	public GVGBattleAsyncOperation(boolean synchronize) {
		this.synchronize = synchronize;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		if (!synchronize) {
			execute();
			return false;
		} else {
			return true;
		}
	}

	@Override
	public void finish() {
		if (synchronize) {
			execute();
		}
	}

	public abstract void execute();
}
