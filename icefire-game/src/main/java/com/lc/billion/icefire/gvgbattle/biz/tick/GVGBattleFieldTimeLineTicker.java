package com.lc.billion.icefire.gvgbattle.biz.tick;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.player.PlayerServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.protocol.GcGVGBuildingOpen;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleService;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.GcGvgBattleServerTimeInfo;
import com.longtech.ls.zookeeper.ConfigCenter;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGBattleFieldTimeLineTicker extends AbstractTicker<GVGBattleFieldTimeLine> {

	private static final Logger logger = LoggerFactory.getLogger(GVGBattleFieldTimeLineTicker.class);

	@Autowired
	private GVGBattleService gvgBattleService;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private WorldServiceImpl worldService;
	@Autowired
	private GVGStrongHoldService GVGStrongHoldService;
	@Autowired
	private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;
	@Autowired
	private ConfigCenter configCenter;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private StrongHoldNodeDao strongHoldNodeDao;
	@Autowired
	private PlayerServiceImpl playerService;

	public GVGBattleFieldTimeLineTicker() {
		super(1 * TimeUtil.SECONDS_MILLIS);
	}

	@Override
	protected void tick(GVGBattleFieldTimeLine gvgBattleFieldTimeLine, long now) {
		long startTime = gvgBattleFieldTimeLine.getStartTime();
		if (!gvgBattleFieldTimeLine.isStartFlag() && startTime < now) {
			gvgBattleFieldTimeLine.setStartFlag(true);
			// 战场开始，可以入场，但不能干别的事
			logger.info("GVG战场开启，准备");
			sendGcGvgBattleServerTimeInfo(gvgBattleFieldTimeLine);
			configCenter.updateGameServerEnable(Application.getServerId(), true);


			try {
				GVGBattleDataVoManager gvgBattleDataVoManager = Application.getBean(GVGBattleDataVoManager.class);
				GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo = gvgBattleDataVoManager.findGvgBattleServerDispatchRecordVo();
				gvgBattleService.loadAllianceData(gvgBattleServerDispatchRecordVo.getAllianceId1(), gvgBattleServerDispatchRecordVo.getAlliance1ServerId());
				gvgBattleService.loadAllianceData(gvgBattleServerDispatchRecordVo.getAllianceId2(), gvgBattleServerDispatchRecordVo.getAlliance2ServerId());
			} catch (Exception e) {
				ErrorLogUtil.exceptionLog("记载联盟数据异常", e);
			}

			try {
				GVGStrongHoldService.loadOfficeBuff();
			} catch (ExpectedException ignored){

			} catch (Exception e) {
				ErrorLogUtil.exceptionLog("GVG加载buff", e);
			}
		}
		long readyEndTime = gvgBattleFieldTimeLine.getReadyEndTime();
		if (!gvgBattleFieldTimeLine.isReadyEndFlag() && readyEndTime < now) {
			gvgBattleFieldTimeLine.setReadyEndFlag(true);
			// 准备时间过了，可以开打了
			logger.info("GVG战场正式开始");
			sendGcGvgBattleServerTimeInfo(gvgBattleFieldTimeLine);
			GVGStrongHoldService.gvgReadyFinish();
		}
		long endTime = gvgBattleFieldTimeLine.getEndTime();
		if (!gvgBattleFieldTimeLine.isEndFlag() && endTime < now) {
			logger.info("GVG战场时间到了结束start");
			gvgBattleFieldTimeLine.setEndFlag(true);
			// 结束了
			gvgBattleService.recountAlliancePoint();
			gvgBattleService.battleStop();
			sendGcGvgBattleServerTimeInfo(gvgBattleFieldTimeLine);
			logger.info("GVG战场时间到了结束end");
		}

		GvgBuildingConfig config = configService.getConfig(GvgBuildingConfig.class);
		List<String> buildingList = new ArrayList<>();

		// 刷新资源
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		int[] refreshResourceRefreshTimeArray = gvgSettingConfig.getRefreshResourceRefreshTime();
		if (refreshResourceRefreshTimeArray != null) {
			List<Integer> resourceRefreshFlag = gvgBattleFieldTimeLine.getResourceRefreshFlag();
			for (int index = 1; index <= refreshResourceRefreshTimeArray.length; index++) {
				if (resourceRefreshFlag.contains(index)) {
					continue;
				}
				if (now > startTime + refreshResourceRefreshTimeArray[index-1] * TimeUtil.SECONDS_MILLIS) {
					resourceRefreshFlag.add(index);
					logger.info("GVG战场刷新资源。index : " + index);
					GVGStrongHoldService.startRefreshGvgRes();

					GvgBuildingConfig.GvgBuildingMeta meta = config.getBuildingMetaByType(BuildingType.ZiYuan.getId());
					if (meta != null) {
						buildingList.add(meta.getId());
					}
				}
			}
		}


		// 广播建筑
		List<String> noticedOpenFlag = gvgBattleFieldTimeLine.getNoticedOpenFlag();
		if (noticedOpenFlag == null) {
			noticedOpenFlag = new ArrayList<>();
		}

		for (StrongHoldNode strongHoldNode : strongHoldNodeDao.findAll()) {
			if (now < strongHoldNode.getUnlockTime()) {
				continue;
			}
			GvgBuildingConfig.GvgBuildingMeta gvgBuildingMeta = config.get(strongHoldNode.getMetaId());
			if (gvgBuildingMeta == null || !JavaUtils.bool(gvgBuildingMeta.getBuildingOpenShow())) {
				continue;
			}
			if (noticedOpenFlag.contains(gvgBuildingMeta.getId())) {
				continue;
			}
			logger.info("broad cast : " + gvgBuildingMeta.getId());
			buildingList.add(gvgBuildingMeta.getId());
			noticedOpenFlag.add(gvgBuildingMeta.getId());
		}

		if (JavaUtils.bool(buildingList)) {
			for (String metaId : buildingList) {
				pushGVGBuildingOpen(metaId);
			}
		}
		gvgBattleFieldTimeLine.setNoticedOpenFlag(noticedOpenFlag);
		gvgBattleFieldTimeLineDao.save(gvgBattleFieldTimeLine);

	}

	private void pushGVGBuildingOpen(String metaId) {
		GcGVGBuildingOpen gcGVGBuildingOpen = new GcGVGBuildingOpen();
		gcGVGBuildingOpen.setMetaId(metaId);
		playerService.broadcast(0, Application.getServerId(), gcGVGBuildingOpen);
	}

	private void sendGcGvgBattleServerTimeInfo(GVGBattleFieldTimeLine gvgBattleFieldTimeLine) {
		asyncOperationService.execute(new AsyncOperation() {
			@Override
			public boolean run() {
				GcGvgBattleServerTimeInfo gcGvgBattleServerTimeInfo = new GcGvgBattleServerTimeInfo();
				gvgBattleFieldTimeLine.putGvgBattleServerTimeInfo(gcGvgBattleServerTimeInfo);
				worldService.run(role -> role.send(gcGvgBattleServerTimeInfo));
				return false;
			}

			@Override
			public boolean init() {
				return true;
			}

			@Override
			public void finish() {

			}
		});
	}

	@Override
	protected Collection<GVGBattleFieldTimeLine> findAll() {
		return gvgBattleFieldTimeLineDao.findAll();
	}

}
