package com.lc.billion.icefire.csabattle.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.simfun.sgf.common.tuple.TwoTuple;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lc.billion.icefire.core.config.model.AbstractMeta.META_SEPARATOR_2;

@Config(name = "CrossSeverAttackSetting")
public class CrossSeverAttackSetting {

	private List<Integer> battlePhaseMarchTypeForbidAttackerCrossSevers;
	private List<Integer> battlePhaseMarchTypeForbidAttackerOwnSevers;
	private List<Integer> battlePhaseMarchTypeForbidDefencerOwnSevers;
	private int battlePhaseBattlefieldEnterTime;
	private int battlePhaseBattlefieldEnterFreeTime;
	private TwoTuple<String, Integer> _battlePhaseBattlefieldEnterCost;

	/**
	 * 战场buff<br>
	 * 6 1 battlePhaseBattlefieldBuff01 71001|0.5 战斗阶段-战场服buff效果1，向城市发起的行军速度增加<br>
	 * 7 1 battlePhaseBattlefieldBuff02 71002|0.9
	 * 战斗阶段-战场服buff效果2，在城市发生的战斗伤兵额外伤转活系数<br>
	 */
	private final Map<Prop, Double> battlePhaseBattlefieldBuff = new HashMap<>();

	/**
	 * 个人积分-杀敌<br>
	 * 1|2|3|4|5|6|7|8|9|10 配置1-10级兵得分，数组长度=士兵等级，需要支持扩展11级及以上
	 */
	private int[] _individualPointKillSoldier;
	/**
	 * 个人积分-驻军<br>
	 * 1|100 加分间隔（单位秒）|加分值
	 */
	private int[] _individualPointGarrison;

	// 跨服夺城-援军最大队伍数
	private int csaReinforcementMaxTeam;

	// 跨服夺城-援军每次出战最大队伍数
	private int csaReinforcementBattleTeam;

	// 跨服夺城-进入战场最低等级
	private int battlePhaseBattlefieldEnterLevel;

	// 1级城得分
	private int communityPointLevel01;

	// 2级城得分
	private int communityPointLevel02;

	// 3级城得分
	private int communityPointLevel03;

	// 4级城得分
	private int communityPointLevel04;

	// 5级城得分
	private int communityPointLevel05;

	// 1级城占领值上限
	private int occupationPointLimitLevel01;

	// 2级城占领值上限
	private int occupationPointLimitLevel02;

	// 3级城占领值上限
	private int occupationPointLimitLevel03;

	// 4级城占领值上限
	private int occupationPointLimitLevel04;

	// 5级城占领值上限
	private int occupationPointLimitLevel05;

	// 39 1 csaRankListLimit 100 跨服夺城排行榜展示最大人数
	private int csaRankListLimit;

	// 预热阶段持续时长，单位秒
	private long csaPreviewPhaseTime;

	// 选时间阶段持续时长，单位秒
	private long csaSelectPhaseTime;

	// 战斗阶段-上半场准备时间，单位秒
	private long battlePhaseFirstHalfPreparingTime;

	// 战斗阶段-上半场战斗时间，单位秒
	private long battlePhaseFirstHalfBattleTime;

	// 上半场结算时长，单位秒
	private long csaFirstHalfResultTime;

	// 战斗阶段-中场休息时间，单位秒
	private  long battlePhaseRestTime;

	// 战斗阶段-下半场准备时间，单位秒
	private long battlePhaseSecondHalfPreparingTime;

	// 战斗阶段-下半场战斗时间，单位秒
	private long battlePhaseSecondHalfBattleTime;

	// 下半场结算时长，单位秒
	private long csaSecondHalfResultTime;

	// 占领值增长间隔，单位秒
	private int occupationPointIncreaseInterval;

	// 占领值增长值
	private int occupationPointIncreaseValue;

	// 同时有超过此数量的行军线时（双方都算）触发热点城市展示
	private int hotSpotMarchNumTrigger;

	// 跨服夺城首次让玩家见到的所需的开服时间
	private int csaFirstAppearTime;

	// 跨服夺城加入匹配的最小天数
	private int csaMatchLimit;

	// 计算服务器总战力选取的联盟数量
	private int csaServerPowerSelectClanNum;

	// 一轮比赛包含的场次数量
	private int csaSeriesMatchNum;

	// 跨服夺城buff持续时间，单位：秒
	private long csaBuffDuration;

	// csa活动中K服限制选择时间开关 0：关（不限制） 1：开（限制）
	private int csaKvkForceMatchSwitch;

	// 跨服夺城活动能不能在赛季结算期开，1-能，0-不能
	private int csaSeasonEndSwitch;

	// 优先对战 csaPracticeMatchCondition 场内没遇到过的服务器
	private int csaPracticeMatchCondition;

	// 跨服夺城联赛首场判断是否能开启时，距离赛季结算时间需要大于等于此时间（后续场次不判断）
	private int csaLeagueTimelimit;

	public void init(JsonNode json) {

		battlePhaseMarchTypeForbidAttackerCrossSevers = MetaUtils.parseIntegerList(json.path("battlePhaseMarchTypeForbidAttackerCrossSever").asText(), META_SEPARATOR_2);
		battlePhaseMarchTypeForbidAttackerOwnSevers = MetaUtils.parseIntegerList(json.path("battlePhaseMarchTypeForbidAttackerOwnSever").asText(), META_SEPARATOR_2);
		battlePhaseMarchTypeForbidDefencerOwnSevers = MetaUtils.parseIntegerList(json.path("battlePhaseMarchTypeForbidDefencerOwnSever").asText(), META_SEPARATOR_2);

		// 战场buff
		String[] battlePhaseBattlefieldBuff01 = MetaUtils.parse(json.path("battlePhaseBattlefieldBuff01").asText(), META_SEPARATOR_2);
		if (battlePhaseBattlefieldBuff01 != null) {
			Prop prop = Prop.findById(Integer.parseInt(battlePhaseBattlefieldBuff01[0]));
			double value = Double.parseDouble(battlePhaseBattlefieldBuff01[1]);
			battlePhaseBattlefieldBuff.put(prop, value);
		}
		String[] battlePhaseBattlefieldBuff02 = MetaUtils.parse(json.path("battlePhaseBattlefieldBuff02").asText(), META_SEPARATOR_2);
		if (battlePhaseBattlefieldBuff02 != null) {
			Prop prop = Prop.findById(Integer.parseInt(battlePhaseBattlefieldBuff02[0]));
			double value = Double.parseDouble(battlePhaseBattlefieldBuff02[1]);
			battlePhaseBattlefieldBuff.put(prop, value);
		}

		String[] battlePhaseBattlefieldBuff03 = MetaUtils.parse(json.path("battlePhaseBattlefieldBuff03").asText(), META_SEPARATOR_2);
		if (battlePhaseBattlefieldBuff03 != null) {
			Prop prop = Prop.findById(Integer.parseInt(battlePhaseBattlefieldBuff03[0]));
			double value = Double.parseDouble(battlePhaseBattlefieldBuff03[1]);
			battlePhaseBattlefieldBuff.put(prop, value);
		}

		String[] battlePhaseBattlefieldBuff04 = MetaUtils.parse(json.path("battlePhaseBattlefieldBuff04").asText(), META_SEPARATOR_2);
		if (battlePhaseBattlefieldBuff04 != null) {
			Prop prop = Prop.findById(Integer.parseInt(battlePhaseBattlefieldBuff04[0]));
			double value = Double.parseDouble(battlePhaseBattlefieldBuff04[1]);
			battlePhaseBattlefieldBuff.put(prop, value);
		}

		String[] battlePhaseBattlefieldBuff05 = MetaUtils.parse(json.path("battlePhaseBattlefieldBuff05").asText(), META_SEPARATOR_2);
		if (battlePhaseBattlefieldBuff05 != null) {
			Prop prop = Prop.findById(Integer.parseInt(battlePhaseBattlefieldBuff05[0]));
			double value = Double.parseDouble(battlePhaseBattlefieldBuff05[1]);
			battlePhaseBattlefieldBuff.put(prop, value);
		}

		// 个人积分
		_individualPointKillSoldier = MetaUtils.parseInts(json.path("individualPointKillSoldier").asText(), META_SEPARATOR_2);
		_individualPointGarrison = MetaUtils.parseInts(json.path("individualPointGarrison").asText(), META_SEPARATOR_2);
		_battlePhaseBattlefieldEnterCost = MetaUtils.convertContentToTwoTuple(json.path("battlePhaseBattlefieldEnterCost").asText(), META_SEPARATOR_2, String.class, Integer.class);
	}

	public List<Integer> getBattlePhaseMarchTypeForbidAttackerCrossSevers() {
		return battlePhaseMarchTypeForbidAttackerCrossSevers;
	}

	public List<Integer> getBattlePhaseMarchTypeForbidAttackerOwnSevers() {
		return battlePhaseMarchTypeForbidAttackerOwnSevers;
	}

	public List<Integer> getBattlePhaseMarchTypeForbidDefencerOwnSevers() {
		return battlePhaseMarchTypeForbidDefencerOwnSevers;
	}

	public Map<Prop, Double> getBattlePhaseBattlefieldBuff() {
		return battlePhaseBattlefieldBuff;
	}

	public int[] getIndividualPointKillSoldier() {
		return _individualPointKillSoldier;
	}

	public int getIndividualPointKillSoldierPoint(int soldierLevel) {
		int i = soldierLevel - 1;
		if (i < 0) {
			return 0;
		}
		if (i > _individualPointKillSoldier.length - 1) {
			i = _individualPointKillSoldier.length - 1;
		}
		return _individualPointKillSoldier[i];
	}

	public int[] getIndividualPointGarrison() {
		return _individualPointGarrison;
	}

	public int getCsaReinforcementBattleTeam() {
		return csaReinforcementBattleTeam;
	}

	public void setCsaReinforcementBattleTeam(int csaReinforcementBattleTeam) {
		this.csaReinforcementBattleTeam = csaReinforcementBattleTeam;
	}

	public void setCsaReinforcementMaxTeam(int csaReinforcementMaxTeam) {
		this.csaReinforcementMaxTeam = csaReinforcementMaxTeam;
	}

	public int getCsaReinforcementMaxTeam() {
		return csaReinforcementMaxTeam;
	}

	public int getBattlePhaseBattlefieldEnterTime() {
		return battlePhaseBattlefieldEnterTime;
	}

	public int getBattlePhaseBattlefieldEnterFreeTime() {
		return battlePhaseBattlefieldEnterFreeTime;
	}

	public TwoTuple<String, Integer> getBattlePhaseBattlefieldEnterCost() {
		return _battlePhaseBattlefieldEnterCost;
	}

	public int getBattlePhaseBattlefieldEnterLevel() {
		return battlePhaseBattlefieldEnterLevel;
	}

	public int getCommunityPointLevel01() {
		return communityPointLevel01;
	}

	public int getCommunityPointLevel02() {
		return communityPointLevel02;
	}

	public int getCommunityPointLevel03() {
		return communityPointLevel03;
	}

	public int getCommunityPointLevel04() {
		return communityPointLevel04;
	}

	public int getCommunityPointLevel05() {
		return communityPointLevel05;
	}

	public int getOccupationPointLimitLevel01() {
		return occupationPointLimitLevel01;
	}

	public int getOccupationPointLimitLevel02() {
		return occupationPointLimitLevel02;
	}

	public int getOccupationPointLimitLevel03() {
		return occupationPointLimitLevel03;
	}

	public int getOccupationPointLimitLevel04() {
		return occupationPointLimitLevel04;
	}

	public int getOccupationPointLimitLevel05() {
		return occupationPointLimitLevel05;
	}

	public void setCommunityPointLevel01(int communityPointLevel01) {
		this.communityPointLevel01 = communityPointLevel01;
	}

	public void setCommunityPointLevel02(int communityPointLevel02) {
		this.communityPointLevel02 = communityPointLevel02;
	}

	public void setCommunityPointLevel03(int communityPointLevel03) {
		this.communityPointLevel03 = communityPointLevel03;
	}

	public void setCommunityPointLevel04(int communityPointLevel04) {
		this.communityPointLevel04 = communityPointLevel04;
	}

	public void setCommunityPointLevel05(int communityPointLevel05) {
		this.communityPointLevel05 = communityPointLevel05;
	}

	public void setOccupationPointLimitLevel01(int occupationPointLimitLevel01) {
		this.occupationPointLimitLevel01 = occupationPointLimitLevel01;
	}

	public void setOccupationPointLimitLevel02(int occupationPointLimitLevel02) {
		this.occupationPointLimitLevel02 = occupationPointLimitLevel02;
	}

	public void setOccupationPointLimitLevel03(int occupationPointLimitLevel03) {
		this.occupationPointLimitLevel03 = occupationPointLimitLevel03;
	}

	public void setOccupationPointLimitLevel04(int occupationPointLimitLevel04) {
		this.occupationPointLimitLevel04 = occupationPointLimitLevel04;
	}

	public void setOccupationPointLimitLevel05(int occupationPointLimitLevel05) {
		this.occupationPointLimitLevel05 = occupationPointLimitLevel05;
	}

	public int getOccupationPointLimit(int level) {
		switch (level) {
		case 1:
			return occupationPointLimitLevel01;
		case 2:
			return occupationPointLimitLevel02;
		case 3:
			return occupationPointLimitLevel03;
		case 4:
			return occupationPointLimitLevel04;
		case 5:
			return occupationPointLimitLevel05;
		}

		return 0;
	}

	public int getWorldCastlePoint(int level) {
		switch (level) {
		case 1:
			return communityPointLevel01;
		case 2:
			return communityPointLevel02;
		case 3:
			return communityPointLevel03;
		case 4:
			return communityPointLevel04;
		case 5:
			return communityPointLevel05;
		}

		return 0;
	}

	public int getCsaRankListLimit() {
		return csaRankListLimit;
	}

	public long getBattlePhaseFirstHalfPreparingTime() {
		return battlePhaseFirstHalfPreparingTime;
	}

	public void setBattlePhaseFirstHalfPreparingTime(long battlePhaseFirstHalfPreparingTime) {
		this.battlePhaseFirstHalfPreparingTime = battlePhaseFirstHalfPreparingTime;
	}

	public long getBattlePhaseFirstHalfBattleTime() {
		return battlePhaseFirstHalfBattleTime;
	}

	public void setBattlePhaseFirstHalfBattleTime(long battlePhaseFirstHalfBattleTime) {
		this.battlePhaseFirstHalfBattleTime = battlePhaseFirstHalfBattleTime;
	}

	public long getBattlePhaseRestTime() {
		return battlePhaseRestTime;
	}

	public void setBattlePhaseRestTime(long battlePhaseRestTime) {
		this.battlePhaseRestTime = battlePhaseRestTime;
	}

	public long getBattlePhaseSecondHalfPreparingTime() {
		return battlePhaseSecondHalfPreparingTime;
	}

	public void setBattlePhaseSecondHalfPreparingTime(long battlePhaseSecondHalfPreparingTime) {
		this.battlePhaseSecondHalfPreparingTime = battlePhaseSecondHalfPreparingTime;
	}

	public long getBattlePhaseSecondHalfBattleTime() {
		return battlePhaseSecondHalfBattleTime;
	}

	public void setBattlePhaseSecondHalfBattleTime(long battlePhaseSecondHalfBattleTime) {
		this.battlePhaseSecondHalfBattleTime = battlePhaseSecondHalfBattleTime;
	}

	public int getOccupationPointIncreaseInterval() {
		return occupationPointIncreaseInterval;
	}

	public int getOccupationPointIncreaseValue() {
		return occupationPointIncreaseValue;
	}

	public void setOccupationPointIncreaseInterval(int occupationPointIncreaseInterval) {
		this.occupationPointIncreaseInterval = occupationPointIncreaseInterval;
	}

	public void setOccupationPointIncreaseValue(int occupationPointIncreaseValue) {
		this.occupationPointIncreaseValue = occupationPointIncreaseValue;
	}

	public int getHotSpotMarchNumTrigger() {
		return hotSpotMarchNumTrigger;
	}

	public void setHotSpotMarchNumTrigger(int hotSpotMarchNumTrigger) {
		this.hotSpotMarchNumTrigger = hotSpotMarchNumTrigger;
	}

	public long getCsaPreviewPhaseTime() {
		return csaPreviewPhaseTime;
	}

	public void setCsaPreviewPhaseTime(long csaPreviewPhaseTime) {
		this.csaPreviewPhaseTime = csaPreviewPhaseTime;
	}

	public long getCsaSelectPhaseTime() {
		return csaSelectPhaseTime;
	}

	public void setCsaSelectPhaseTime(long csaSelectPhaseTime) {
		this.csaSelectPhaseTime = csaSelectPhaseTime;
	}

	public long getCsaFirstHalfResultTime() {
		return csaFirstHalfResultTime;
	}

	public void setCsaFirstHalfResultTime(long csaFirstHalfResultTime) {
		this.csaFirstHalfResultTime = csaFirstHalfResultTime;
	}

	public long getCsaSecondHalfResultTime() {
		return csaSecondHalfResultTime;
	}

	public void setCsaSecondHalfResultTime(long csaSecondHalfResultTime) {
		this.csaSecondHalfResultTime = csaSecondHalfResultTime;
	}

	public int getCsaFirstAppearTime() {
		return csaFirstAppearTime;
	}

	public void setCsaFirstAppearTime(int csaFirstAppearTime) {
		this.csaFirstAppearTime = csaFirstAppearTime;
	}

	public int getCsaMatchLimit() {
		return csaMatchLimit;
	}

	public void setCsaMatchLimit(int csaMatchLimit) {
		this.csaMatchLimit = csaMatchLimit;
	}

	public int getCsaServerPowerSelectClanNum() {
		return csaServerPowerSelectClanNum;
	}

	public void setCsaServerPowerSelectClanNum(int csaServerPowerSelectClanNum) {
		this.csaServerPowerSelectClanNum = csaServerPowerSelectClanNum;
	}

	public int getCsaSeriesMatchNum() {
		return csaSeriesMatchNum;
	}

	public void setCsaSeriesMatchNum(int csaSeriesMatchNum) {
		this.csaSeriesMatchNum = csaSeriesMatchNum;
	}

	public long getCsaBuffDuration() {
		return csaBuffDuration;
	}

	public void setCsaBuffDuration(long csaBuffDuration) {
		this.csaBuffDuration = csaBuffDuration;
	}

	public int getCsaSeasonEndSwitch() {
		return csaSeasonEndSwitch;
	}

	public void setCsaSeasonEndSwitch(int csaSeasonEndSwitch) {
		this.csaSeasonEndSwitch = csaSeasonEndSwitch;
	}

	public int getCsaKvkForceMatchSwitch() {
		return csaKvkForceMatchSwitch;
	}

	public void setCsaKvkForceMatchSwitch(int csaKvkForceMatchSwitch) {
		this.csaKvkForceMatchSwitch = csaKvkForceMatchSwitch;
	}

	public void setBattlePhaseMarchTypeForbidAttackerCrossSevers(List<Integer> battlePhaseMarchTypeForbidAttackerCrossSevers) {
		this.battlePhaseMarchTypeForbidAttackerCrossSevers = battlePhaseMarchTypeForbidAttackerCrossSevers;
	}

	public void setBattlePhaseMarchTypeForbidAttackerOwnSevers(List<Integer> battlePhaseMarchTypeForbidAttackerOwnSevers) {
		this.battlePhaseMarchTypeForbidAttackerOwnSevers = battlePhaseMarchTypeForbidAttackerOwnSevers;
	}

	public void setBattlePhaseMarchTypeForbidDefencerOwnSevers(List<Integer> battlePhaseMarchTypeForbidDefencerOwnSevers) {
		this.battlePhaseMarchTypeForbidDefencerOwnSevers = battlePhaseMarchTypeForbidDefencerOwnSevers;
	}

	public void setBattlePhaseBattlefieldEnterTime(int battlePhaseBattlefieldEnterTime) {
		this.battlePhaseBattlefieldEnterTime = battlePhaseBattlefieldEnterTime;
	}

	public void setBattlePhaseBattlefieldEnterFreeTime(int battlePhaseBattlefieldEnterFreeTime) {
		this.battlePhaseBattlefieldEnterFreeTime = battlePhaseBattlefieldEnterFreeTime;
	}

	public int[] get_individualPointKillSoldier() {
		return _individualPointKillSoldier;
	}

	public void set_individualPointKillSoldier(int[] _individualPointKillSoldier) {
		this._individualPointKillSoldier = _individualPointKillSoldier;
	}

	public int[] get_individualPointGarrison() {
		return _individualPointGarrison;
	}

	public void set_individualPointGarrison(int[] _individualPointGarrison) {
		this._individualPointGarrison = _individualPointGarrison;
	}

	public void setBattlePhaseBattlefieldEnterLevel(int battlePhaseBattlefieldEnterLevel) {
		this.battlePhaseBattlefieldEnterLevel = battlePhaseBattlefieldEnterLevel;
	}

	public void setCsaRankListLimit(int csaRankListLimit) {
		this.csaRankListLimit = csaRankListLimit;
	}

	public int getCsaPracticeMatchCondition() {
		return csaPracticeMatchCondition;
	}

	public void setCsaPracticeMatchCondition(int csaPracticeMatchCondition) {
		this.csaPracticeMatchCondition = csaPracticeMatchCondition;
	}

	public int getCsaLeagueTimelimit() {
		return csaLeagueTimelimit;
	}

	public void setCsaLeagueTimelimit(int csaLeagueTimelimit) {
		this.csaLeagueTimelimit = csaLeagueTimelimit;
	}
}
