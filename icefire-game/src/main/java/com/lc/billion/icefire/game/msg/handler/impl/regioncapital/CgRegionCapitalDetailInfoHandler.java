package com.lc.billion.icefire.game.msg.handler.impl.regioncapital;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgRegionCapitalDetailInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgRegionCapitalDetailInfoHandler extends CgAbstractMessageHandler<CgRegionCapitalDetailInfo> {
    @Autowired
    private RegionCapitalService regionCapitalService;

    @Override
    public void handle(Role role, CgRegionCapitalDetailInfo message) {
        regionCapitalService.sendRegionCapital(role, message.getId());
    }
}
