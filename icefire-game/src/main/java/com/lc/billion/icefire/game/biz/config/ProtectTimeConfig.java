package com.lc.billion.icefire.game.biz.config;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.config.model.PackageInfo;
import com.lc.billion.icefire.game.biz.config.ProtectTimeConfig.ProtectTimeMeta;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 迁服保护时间-ServerProtection.xlsx
 * 
 * <AUTHOR>
 *
 */
@Config(name = "protectTime", metaClass = ProtectTimeMeta.class)
public class ProtectTimeConfig {

	@MetaMap
	private Map<String, ProtectTimeMeta> data;

	public ProtectTimeMeta getMetaById(String id) {
		return data.get(id);
	}

	public ProtectTimeMeta getMetaById(int serverId) {
		return getMetaById(String.valueOf(serverId));
	}

	public static class ProtectTimeMeta extends AbstractMeta {

		/**
		 * 保护天数
		 */
		private int protectTime;
		private PackageInfo targetServerIds;
		private PackageInfo targetFreeServerIds;

		@Override
		public void init(JsonNode json) {
			String asText = json.path("Target").asText();
			targetServerIds = new PackageInfo();
			if (StringUtils.isNotBlank(asText)) {
				targetServerIds.init(asText);
			}
			asText = json.path("TargetFree").asText();
			targetFreeServerIds = new PackageInfo();
			if (StringUtils.isNotBlank(asText)) {
				targetFreeServerIds.init(asText);
			}
		}

		public int getProtectTime() {
			return protectTime;
		}

		public Set<Integer> getTargetServerIds() {
			return targetServerIds.getAll();
		}

		public Set<Integer> getTargetFreeServerIds() {
			return targetFreeServerIds.getAll();
		}

		public Set<Integer> getAllTargetServerIds() {
			Set<Integer> ret = new HashSet<>();
			Set<Integer> targetServerIds = getTargetServerIds();
			if (targetServerIds != null) {
				ret.addAll(targetServerIds);
			}
			targetServerIds = getTargetFreeServerIds();
			if (targetServerIds != null) {
				ret.addAll(targetServerIds);
			}
			return ret;
		}
	}
}
