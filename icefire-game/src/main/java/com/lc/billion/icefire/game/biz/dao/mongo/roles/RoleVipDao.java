package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import com.lc.billion.icefire.game.biz.dao.RolesSingleEntityDao;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.vip.RoleVip;
import org.springframework.stereotype.Repository;

/**
 * 
 * <AUTHOR>
 *
 */
@Repository
public class RoleVipDao extends RolesSingleEntityDao<RoleVip> {

	protected RoleVipDao() {
		super(RoleVip.class);
	}

	public RoleVip create(Role role) {
		RoleVip roleVip = newEntityInstance();
		roleVip.setRoleId(role.getId());
		roleVip.setVipExp(0);
		roleVip.setVipLevel(1);
		return createEntity(role, roleVip);
	}
}
