package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.battle.FightContext;
import com.lc.billion.icefire.game.biz.battle.FightTroop;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BIMarchResult;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleScoreService;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGResGatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * 攻击其他人资源点
 * 
 * <AUTHOR>
 * @date 2021/1/27
 */
@Service
public class GVGResAttackOperation extends AbstractGvgFightOperation {

	@Autowired
	private GVGResGatherService GVGResGatherService;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private ArmyServiceImpl armyService;
	@Autowired
	private GVGBattleScoreService gvgBattleScoreService;


	@Override
	public ArmyType getArmyType() {
		return ArmyType.ATTACK_GVG_GATHER;
	}

	@Override
	public void finishWork(ArmyInfo attackerMainArmy) {
		super.finishWork(attackerMainArmy);
		if (attackerMainArmy.getFightContext().isWin()) {
			// 胜利方直接采集
			attackerMainArmy.setArmyType(ArmyType.GVG_GATHER);
			attackerMainArmy.setWorkType(ArmyWorkType.SETOUT);
			armyManager.saveArmy(attackerMainArmy);
		}
		armyManager.marchRetBILog(attackerMainArmy);
	}

	@Override
	protected void endBattle(ArmyInfo army) {
		FightContext fightContext = army.getFightContext();
		armyManager.worldDefendRet(army, fightContext.isWin() ? BIMarchResult.DEFEATED : BIMarchResult.VICTORY);
		if (fightContext.isWin()) {
			winBattle(army);
		} else {
			loseBattle(army);
		}
		gvgBattleScoreService.onGVGFightEnd(army);
	}

	private void winBattle(ArmyInfo army) {
		// 没有奖励
		SceneNode sceneNode = armyManager.getArmyTargetNode(army);
		if (sceneNode == null) {
			ErrorLogUtil.errorLog("不应该进到这里 战斗完结算时目标为空");
			armyManager.returnArmy(army);
			return;
		}
		//
		// 1.防守方回家defArmy
		//
		GvgResNode resNode = (GvgResNode) sceneNode;
		if (resNode.inGathering()) {
			ArmyInfo defArmy = armyDao.findById(resNode.getArmyId());
			if (defArmy != null) {
				GVGResGatherService.calculate(defArmy, true, false, false);
			}
		}
		// 2 胜利方采集在finishWork后处理
	}

	private void loseBattle(ArmyInfo army) {
		// 防守方重算采集数据
		var fightTroops = army.getFightContext().getDefender().getFightTroops();
		for (FightTroop fightTroop : fightTroops) {
			ArmyInfo findById = armyDao.findById(fightTroop.getArmyId());
			if (findById != null) {
				findById.getGvgGatherContext().setReCaculate(true);
				armyDao.save(findById);
			}
		}
		// 攻击方
		armyManager.marchRetBILog(army);
		armyManager.returnArmy(army);
		SceneNode targetNode = armyManager.getArmyTargetNode(army);
		GvgResNode resNode = null;
		if (targetNode != null && targetNode.getNodeType() == SceneNodeType.GVG_RES) {
			resNode = (GvgResNode) targetNode;
		}
		if (resNode != null) {
			army.getFightContext().setGvgResMetaId(resNode.getMetaId());
		}
	}

	@Override
	protected boolean check(ArmyInfo army, SceneNode target) {
		Long targetNodeId = army.getTargetNodeId();
		if (target == null || !target.getPersistKey().equals(targetNodeId) || target.getNodeType() != SceneNodeType.GVG_RES) {
			return false;
		}

		GvgResNode resNode = (GvgResNode) target;
		// 没有军队正在采集 || 占领着和我同盟
		if (!resNode.inGathering() || allianceService.isSameAlliance(army.getRoleId(), resNode.getRoleId())) {
			return false;// 不会发生战斗
		}
		return true;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		armyManager.removeEnemy(army);
		SceneNode targetNode = armyManager.getArmyTargetNode(army);
		if (!check(army, targetNode)) {
			armyManager.returnArmy(army);
			return;
		}
		GvgResNode resNode = (GvgResNode) targetNode;
		if (!checkFight(army, resNode)) {// 不发生战斗的情况
			if (!resNode.inGathering() && resNode.hasRemain()) {
				// 无人采集 && 资源点没过期
				directGather(army);
			} else {
				armyManager.returnArmy(army);
			}
			return;
		}
		super.armyArrive(army);
	}

	private void directGather(ArmyInfo army) {
		army.setArmyType(ArmyType.GVG_GATHER);
		ArmyOperation armyOperation = armyService.getArmyOperation(army);
		armyOperation.armyArrive(army);
	}

	private boolean checkFight(ArmyInfo army, GvgResNode resNode) {
		// 目标点无人占领 || 是我本人 || 占领着和我同盟 || 没有军队正在采集 || 无人驻扎
		if (!resNode.hasOwner() || army.getRoleId().equals(resNode.getRoleId()) || allianceService.isSameAlliance(army.getRoleId(), resNode.getRoleId()) || !resNode.inGathering()
				|| armyManager.findById(resNode.getArmyId()) == null) {
			return false;// 不会发生战斗
		}
		return true;
	}


}
