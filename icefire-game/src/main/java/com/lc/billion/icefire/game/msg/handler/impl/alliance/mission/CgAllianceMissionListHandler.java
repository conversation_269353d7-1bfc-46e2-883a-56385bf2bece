package com.lc.billion.icefire.game.msg.handler.impl.alliance.mission;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.mission.AllianceLeaderMissionServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.alliance.mission.AllianceMissionServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgAllianceLeaderMissionList;
import com.lc.billion.icefire.protocol.CgAllianceMissionList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 */
@Controller
public class CgAllianceMissionListHandler extends CgAbstractMessageHandler<CgAllianceMissionList> {

	@Autowired
	private AllianceMissionServiceImpl allianceMissionService;

	@Override
	protected void handle(Role role, CgAllianceMissionList message) {
		allianceMissionService.listMission(role);
	}

}
