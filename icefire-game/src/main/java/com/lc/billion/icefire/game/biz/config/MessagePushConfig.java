package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.config.MessagePushConfig.MessagePushMeta;
import com.lc.billion.icefire.protocol.constant.PsPushPlatformType;
import com.lc.billion.icefire.protocol.constant.PsPushType;


import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Config(name = "MessagePush", metaClass = MessagePushMeta.class)
public class MessagePushConfig {

    // pushPlatformType[1:微信,2:谷歌 3:ios 4:华为] -> pushType -> pushMeta
    public Map<Integer,Map<Integer,MessagePushMeta>> messagePushMetas = new ConcurrentHashMap<>();

    public void init(List<MessagePushMeta> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        for (MessagePushMeta meta : list) {
            var messagePushMetaByPlatform = messagePushMetas.computeIfAbsent(meta.getPlatform(), o -> new ConcurrentHashMap<>());
            messagePushMetaByPlatform.put(meta.getPushType(),meta);
        }
    }

    public MessagePushMeta getMessagePushMeta(PsPushPlatformType pushPlatformType, PsPushType pushType) {
        var meta = messagePushMetas.get(pushPlatformType.getValue());
//        return messagePushMetas.get(pushPlatformType.getValue()).get(pushType.getValue());
        return meta == null ? null : meta.get(pushType.getValue());
    }

    public static class MessagePushMeta extends AbstractMeta {
        private int platform;
        private int pushType;
        private int subscribeId;
        private String defaultState;

        @Override
        public void init(JsonNode json) {
            super.init(json);
        }

        public int getPlatform() {
            return platform;
        }

        public void setPlatform(int platform) {
            this.platform = platform;
        }

        public int getPushType() {
            return pushType;
        }

        public void setPushType(int pushType) {
            this.pushType = pushType;
        }

        public int getSubscribeId() {
            return subscribeId;
        }

        public void setSubscribeId(int subscribeId) {
            this.subscribeId = subscribeId;
        }

        public String getDefaultState() {
            return defaultState;
        }

        public void setDefaultState(String defaultState) {
            this.defaultState = defaultState;
        }
    }
}
