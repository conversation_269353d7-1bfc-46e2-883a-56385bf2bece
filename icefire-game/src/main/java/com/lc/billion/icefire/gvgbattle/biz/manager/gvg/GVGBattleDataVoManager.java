package com.lc.billion.icefire.gvgbattle.biz.manager.gvg;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.graph.Position;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.model.army.Path;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.manager.IManager;
import com.lc.billion.icefire.game.biz.model.activity.ActivityStatus;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig.GvgActivitySafeArea;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.AllianceBattlePointDao;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.AllianceBattlePoint;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBirthArea;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgObMatchInfoVo;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 *
 */
@Component
public class GVGBattleDataVoManager implements IManager {

	private static final Logger logger = LoggerFactory.getLogger(GVGBattleDataVoManager.class);

	@Autowired
	private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;
	@Autowired
	private AllianceBattlePointDao allianceBattlePointDao;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private ConfigServiceImpl configService;

	private GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo;
	private ActivityVo gvgActivityVo;
	private Map<Long, GVGBirthArea> gvgBirthAreas = new HashMap<>();
	private Map<Long, GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos;
	private Map<Integer,List<GvgObMatchInfoVo>> obData;

	private void initGVGBirthAreas() {
		if (gvgBattleServerDispatchRecordVo != null) {
			logger.info("初始化战斗服出生点");
			// 初始化出生点
			// 读表，2个坐标，两个区域
			GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
			GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
			var daYingMetaList = gvgBuildingConfig.getBuildingMetasByType(BuildingType.DaYing.getId());
			GvgActivitySafeArea gvgActivitySafeArea = gvgSettingConfig.getGvgActivitySafeArea();
			if (!gvgActivitySafeArea.isEffective()) {
				throw new AlertException("战斗服出生点策划数据出错");
			}
			int xLength = gvgActivitySafeArea.getxLength();
			int yLength = gvgActivitySafeArea.getyLength();
			Point position = gvgActivitySafeArea.getPosition1();
			Long allianceId = gvgBattleServerDispatchRecordVo.getAllianceId1();
			if (JavaUtils.bool(allianceId)) {
				var daYingMeta = getNearestDaYing(daYingMetaList, position);
				GVGBirthArea gvgBirthArea = new GVGBirthArea(position, xLength, yLength, allianceId, 1, daYingMeta.getId());
				gvgBirthAreas.put(allianceId, gvgBirthArea);
			} else {
				ErrorLogUtil.errorLog("联盟不存在初始化失败");
			}

			position = gvgActivitySafeArea.getPosition2();
			allianceId = gvgBattleServerDispatchRecordVo.getAllianceId2();
			if (JavaUtils.bool(allianceId)) {
				var daYingMeta = getNearestDaYing(daYingMetaList, position);
				GVGBirthArea gvgBirthArea = new GVGBirthArea(position, xLength, yLength, allianceId, 2, daYingMeta.getId());
				gvgBirthAreas.put(allianceId, gvgBirthArea);
			} else {
				ErrorLogUtil.errorLog("联盟不存在初始化失败");
			}
		}
	}

	public GvgBuildingConfig.GvgBuildingMeta getNearestDaYing(List<GvgBuildingConfig.GvgBuildingMeta> daYingMetaList, Point pos){
		float minDis = Float.MAX_VALUE;
		GvgBuildingConfig.GvgBuildingMeta minMeta = null;
		for(var daYingMeta: daYingMetaList){
			var x = daYingMeta.getBuildingPosition().getFloatX();
			var y = daYingMeta.getBuildingPosition().getFloatY();
			var dis = Path.distance(pos.toPosition(), new Position(x, y));
			if(dis < minDis){
				minMeta = daYingMeta;
				minDis = dis;
			}
		}

		return minMeta;
	}

	public GVGBirthArea findGvgBirthArea(Long allianceId) {
		return gvgBirthAreas.get(allianceId);
	}
	public Long getPointBelong(Point position) {
		if (position == null) {
			return null;
		}
		for (Map.Entry<Long, GVGBirthArea> entry : gvgBirthAreas.entrySet()) {
			GVGBirthArea value = entry.getValue();
			if (value.getMinX() <= position.getX() && position.getX() <= value.getMaxX()) {
				if (value.getMinY() <= position.getY() && position.getY() <= value.getMaxY()) {
					return entry.getKey();
				}
			}
		}
		return null;
	}

	public GvgBattleServerDispatchRecordVo findGvgBattleServerDispatchRecordVo() {
		return gvgBattleServerDispatchRecordVo;
	}

	public void updateGvgBattleServerDispatchRecordVo(GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo) {
		this.gvgBattleServerDispatchRecordVo = gvgBattleServerDispatchRecordVo;
		if(Application.getServerType() == ServerType.GVG_BATTLE){
			initGVGBirthAreas();
		}

		logger.info("updateGvgBattleServerDispatchRecordVo 空否:{}", gvgBattleServerDispatchRecordVo != null ? 0 : 1);
	}

	public ActivityVo findActivityVo() {
		return gvgActivityVo;
	}

	public void updateActivityVo(ActivityVo activityVo) {
		this.gvgActivityVo = activityVo;
		if (activityVo != null && activityVo.getStatus() == ActivityStatus.STOP) {
			gvgActivityVo = null;
		}

		logger.info("战斗服:{} 更新Activity镜像", Application.getServerType());
	}

	public void updateGvgObMatch(Map<Integer,List<GvgObMatchInfoVo>> obData){
		this.obData = obData;
	}

	public List<GvgObMatchInfoVo> findGvgObMatchListByWarZoneId(int warZoneId){
		return obData.get(warZoneId);
	}

	public void initBattleFieldTimeLine() {
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		if (gvgBattleFieldTimeLine == null && gvgBattleServerDispatchRecordVo != null) {
			// 战场开启时间
			long battleStartTime = gvgBattleServerDispatchRecordVo.getBattleStartTime();
			// 准备阶段结束时间
			long readyEndTime = gvgSettingConfig.getGVGBattleReadEndTime(battleStartTime);
			// 战场结束时间
			long endTime = gvgSettingConfig.getGVGBattleEndTime(battleStartTime);
			gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.create(battleStartTime, readyEndTime, endTime);
			gvgBattleFieldTimeLineDao.save(gvgBattleFieldTimeLine);
			logger.info("初始化战斗服battleLine");
			initAllianceBattlePoint(gvgBattleFieldTimeLine);
		}else if(Application.getServerType() == ServerType.TVT_BATTLE && gvgBattleFieldTimeLine != null && gvgBattleServerDispatchRecordVo != null){
			gvgBattleServerDispatchRecordVo.setAllianceId1(gvgBattleFieldTimeLine.getAllianceId1());
			gvgBattleServerDispatchRecordVo.setAllianceId2(gvgBattleFieldTimeLine.getAllianceId2());

			logger.info("TVT战场服 初始化战场时间--更新DispatchVo 联盟1:{} 联盟2:{}",
					gvgBattleServerDispatchRecordVo.getAllianceId1(), gvgBattleServerDispatchRecordVo.getAllianceId2());

			// 关联报名信息和联盟
			for(GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo : gvgAllianceSignUpInfoVos.values()){
				Long allianceId = getAllianceIdByTeamId(gvgAllianceSignUpInfoVo.getTeamId());
				if(JavaUtils.bool(allianceId)){
					gvgAllianceSignUpInfoVo.setAllianceId(allianceId);
					logger.info("TVT战场服 初始化战场时间--更新GVGAllianceSignUpInfoVo 队伍:{} Alliance:{}为空", gvgAllianceSignUpInfoVo.getTeamId(), allianceId);
				}else {
					logger.info("TVT战场服 初始化战场时间--更新GVGAllianceSignUpInfoVo 队伍:{} 对应的Alliance为空", gvgAllianceSignUpInfoVo.getTeamId());
				}
			}
		}

		if(Application.getServerType() == ServerType.TVT_BATTLE){
			// 因为TVT战场这个时候才有DispatchVo的联盟
			initGVGBirthAreas();
		}
	}

	private void initAllianceBattlePoint(GVGBattleFieldTimeLine gvgBattleFieldTimeLine) {
		// TVT战斗服需要创建临时联盟
		if(Application.getServerType() == ServerType.TVT_BATTLE){
			initTempAlliance(gvgBattleFieldTimeLine);
		}

		if (gvgBattleServerDispatchRecordVo != null) {
			logger.info("初始化战斗服AllianceBattlePoint");
			Collection<AllianceBattlePoint> allianceBattlePoints = allianceBattlePointDao.findAll();

			List<AllianceBattlePoint> allianceBattlePoints2 = new ArrayList<>(allianceBattlePoints);

			Long allianceId = gvgBattleServerDispatchRecordVo.getAllianceId1();
			AllianceBattlePoint allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
			if (allianceBattlePoint == null) {
				logger.info("AllianceBattlePoint[{}]创建", allianceId);
				AllianceBattlePoint allianceBattlePoint1 = allianceBattlePointDao.createById(allianceId);
				allianceBattlePointDao.save(allianceBattlePoint1);
			} else {
				allianceBattlePoints2.remove(allianceBattlePoint);
			}

			allianceId = gvgBattleServerDispatchRecordVo.getAllianceId2();
			allianceBattlePoint = allianceBattlePointDao.findById(allianceId);
			if (allianceBattlePoint == null) {
				logger.info("AllianceBattlePoint[{}]创建", allianceId);
				AllianceBattlePoint allianceBattlePoint1 = allianceBattlePointDao.createById(allianceId);
				allianceBattlePointDao.save(allianceBattlePoint1);
			} else {
				allianceBattlePoints2.remove(allianceBattlePoint);
			}

			if (JavaUtils.bool(allianceBattlePoints2)) {
				allianceBattlePoints2.forEach(allianceBattlePoint2 -> {
					logger.info("AllianceBattlePoint[{}]移除", allianceBattlePoint2.getPersistKey());
					allianceBattlePointDao.delete(allianceBattlePoint2);
				});
			}
		}
	}

	/**
	 * 创建临时联盟
	 * */
	public void initTempAlliance(GVGBattleFieldTimeLine gvgBattleFieldTimeLine){
		if(!JavaUtils.bool(gvgBattleServerDispatchRecordVo.getAllianceId1())){
			Alliance tempAlliance1 = allianceService.createTVTTempAlliance(1);
			gvgBattleServerDispatchRecordVo.setAllianceId1(tempAlliance1.getId());

			// 关联联盟和队伍
			gvgBattleFieldTimeLine.setAllianceId1(tempAlliance1.getId());
			gvgBattleFieldTimeLineDao.save(gvgBattleFieldTimeLine);
			logger.info("TVT战场服 创建临时联盟 设置DispatchVO Alliance1:{} Team1:{}", tempAlliance1.getId(), 1);
		}

		if(!JavaUtils.bool(gvgBattleServerDispatchRecordVo.getAllianceId2())){
			Alliance tempAlliance2 = allianceService.createTVTTempAlliance(2);
			gvgBattleServerDispatchRecordVo.setAllianceId2(tempAlliance2.getId());

			// 关联联盟和队伍
			gvgBattleFieldTimeLine.setAllianceId2(tempAlliance2.getId());
			gvgBattleFieldTimeLineDao.save(gvgBattleFieldTimeLine);
			logger.info("TVT战场服 创建临时联盟 设置DispatchVO Alliance2:{} Team2:{}", tempAlliance2.getId(), 2);
		}

		// 关联报名信息和联盟
		for(GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo : gvgAllianceSignUpInfoVos.values()){
			Long allianceId = getAllianceIdByTeamId(gvgAllianceSignUpInfoVo.getTeamId());
			if(JavaUtils.bool(allianceId)){
				gvgAllianceSignUpInfoVo.setAllianceId(allianceId);
				logger.info("TVT战场服 创建临时联盟--更新GVGAllianceSignUpInfoVo 队伍:{} Alliance:{}为空", gvgAllianceSignUpInfoVo.getTeamId(), allianceId);
			}else {
				logger.info("TVT战场服 创建临时联盟--更新GVGAllianceSignUpInfoVo 队伍:{} 对应的Alliance为空", gvgAllianceSignUpInfoVo.getTeamId());
			}
		}
	}

	public GvgBattleServerDispatchRecordVo getDispatchRecordInfo() {
		return this.gvgBattleServerDispatchRecordVo;
	}

	public GVGAllianceSignUpInfoVo findGvgAllianceSignUpInfoVos(Long allianceId) {
		if (this.gvgAllianceSignUpInfoVos == null) {
			this.gvgAllianceSignUpInfoVos = new HashMap<>();
		}
		return gvgAllianceSignUpInfoVos.get(allianceId);
	}

	public void updateGvgAllianceSignUpInfoVo(List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos) {
		if (this.gvgAllianceSignUpInfoVos == null) {
			this.gvgAllianceSignUpInfoVos = new HashMap<>();
		}

		if(!JavaUtils.bool(gvgAllianceSignUpInfoVos)){
			logger.info("战场服 更新GVGAllianceSignUpInfoVo 为空");
			return;
		}

		// 关联报名信息和联盟
		for(GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo : gvgAllianceSignUpInfoVos){
			if(Application.getServerType() == ServerType.TVT_BATTLE){
				Long allianceId = getAllianceIdByTeamId(gvgAllianceSignUpInfoVo.getTeamId());
				if(JavaUtils.bool(allianceId)){
					gvgAllianceSignUpInfoVo.setAllianceId(allianceId);
					logger.info("TVT战场服 更新GVGAllianceSignUpInfoVo 队伍:{} Alliance:{}为空", gvgAllianceSignUpInfoVo.getTeamId(), allianceId);
				}else {
					logger.info("TVT战场服 更新GVGAllianceSignUpInfoVo 队伍:{} 对应的Alliance为空", gvgAllianceSignUpInfoVo.getTeamId());
					continue;
				}
			}

			this.gvgAllianceSignUpInfoVos.put(gvgAllianceSignUpInfoVo.getAllianceId(), gvgAllianceSignUpInfoVo);
		}
	}

	public Long getAllianceIdByTeamId(int teamId){
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		if(gvgBattleFieldTimeLine != null){
			if(teamId == 1){
				return gvgBattleFieldTimeLine.getAllianceId1();
			}else if(teamId == 2){
				return gvgBattleFieldTimeLine.getAllianceId2();
			}
		}

		return null;
	}

	public int getTeamIdIdByAllianceId(Long allianceId){
		GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
		if(gvgBattleFieldTimeLine != null &&
				JavaUtils.bool(gvgBattleFieldTimeLine.getAllianceId1()) && gvgBattleFieldTimeLine.getAllianceId1().equals(allianceId) ){
			return 1;
		}else if(gvgBattleFieldTimeLine != null &&
				JavaUtils.bool(gvgBattleFieldTimeLine.getAllianceId2()) && gvgBattleFieldTimeLine.getAllianceId2().equals(allianceId) ){
			return 2;
		}

		return 0;
	}
}
