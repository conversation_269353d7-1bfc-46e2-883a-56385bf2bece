package com.lc.billion.icefire.gvgbattle.biz.model.scene.node;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;

import java.util.Objects;

public abstract  class GVGSceneNode extends SceneNode {
    @Override
    public void addEnemy(ArmyInfo attackerArmy) {
        var gvgStrongHoldService = Application.getBean(GVGStrongHoldService.class);
        var armyManager = Application.getBean(ArmyManager.class);

        Role attackerRole = attackerArmy.getOwner();
        var garrisonArmies = gvgStrongHoldService.getNodeGarrisonArmies(this);
        for(var garrisonArmy: garrisonArmies){
            Role defenderRole = garrisonArmy.getOwner();
            if (!Objects.equals(attackerRole.getAllianceId(), defenderRole.getAllianceId())) {
                armyManager.addEnemy(attackerArmy, defenderRole);
            }
        }
    }

    @Override
    public void removeEnemy(ArmyInfo attackerArmy) {
        var gvgStrongHoldService = Application.getBean(GVGStrongHoldService.class);
        var armyManager = Application.getBean(ArmyManager.class);

        Role attackerRole = attackerArmy.getOwner();
        var garrisonArmies = gvgStrongHoldService.getNodeGarrisonArmies(this);
        for(var garrisonArmy: garrisonArmies){
            Role defenderRole = garrisonArmy.getOwner();
            if (!Objects.equals(attackerRole.getAllianceId(), defenderRole.getAllianceId())) {
                armyManager.removeEnemy(defenderRole, attackerArmy);
            }
        }
    }
}
