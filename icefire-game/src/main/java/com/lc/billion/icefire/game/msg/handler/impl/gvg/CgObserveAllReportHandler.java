package com.lc.billion.icefire.game.msg.handler.impl.gvg;

import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGOBService;
import com.lc.billion.icefire.protocol.CgObserveAllReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;

@Controller
public class CgObserveAllReportHandler extends CgAbstractMessageHandler<CgObserveAllReport> {

	@Autowired
	private GVGOBService gvgobService;

	@Override
	protected void handle(Role role, CgObserveAllReport message) {
		gvgobService.sendAllSimpleReport(role);
	}
}
