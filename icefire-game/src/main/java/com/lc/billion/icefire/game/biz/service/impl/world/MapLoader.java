package com.lc.billion.icefire.game.biz.service.impl.world;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.model.scene.MapData;
import com.lc.billion.icefire.game.biz.model.scene.MapGrid;
import com.lc.billion.icefire.game.biz.model.scene.MapRegion;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.longtech.ls.config.ServerTypeConfig;
import com.simfun.sgf.common.ConfigLoader;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * map为byte二进制文件<br>
 * 顺序读取y为外循环，x内循环<br>
 * 取到的byte&0x3f后的值为约定枚举值<br>
 */
@Service
public class MapLoader {
	private static final Logger log = LoggerFactory.getLogger(MapLoader.class);

	/** 地图文件存放目录 */
	@Getter
	private String mapFileDir;

	public static final int MOUNTAINS = 51;
	public static final int WATER_VEIN = 52;
	private Map<String, MapData> mapCache = new HashMap<>();

	@Value("${map.fileDir}")
	public void setMapFileDir(String mapFileDir) {
		this.mapFileDir = normalizeDirPath(mapFileDir);
	}

	private String normalizeDirPath(String configFileDir) {
		if (configFileDir.length() == 0) {
			return configFileDir;
		}
		char lastChar = configFileDir.charAt(configFileDir.length() - 1);
		if (lastChar == '\\' || lastChar == '/') {
			configFileDir = configFileDir.substring(0, configFileDir.length() - 1);
		}
		return configFileDir;
	}

	public MapData load(int serverId) {
		ServerTypeConfig serverTypeConfig = ServerConfigManager.getInstance().getServerTypeConfig();
		String mapFileName = serverTypeConfig.getServerMap(serverId, Application.getSeason());
		return load(mapFileName, serverId);
	}
	public MapData load(String mapFileName, int serverId) {
		return load(mapFileName, serverId, true);
	}
	public MapData load(String mapFileName, int serverId, boolean usingCache) {
		if (usingCache) {
			MapData cache = this.mapCache.get(mapFileName);
			if (cache != null) {
				MapData data = new MapData(cache, serverId);
				return data;
			}
		}
		String mapPath = this.mapFileDir + "/" + mapFileName;
		File file = ConfigLoader.getConfigFile(mapPath);
		if (file == null) {
			throw new BizException("Not found map file","mapPath",mapPath,"serverId",serverId);
		}
		RandomAccessFile raf = null;
		MapData mapData = new MapData();
		try {
			raf = new RandomAccessFile(file, "r");
			// load(mapData, file);
			load(mapData, raf);
		} catch (Exception e) {
			if (!(e instanceof ExpectedException)) {
				throw JavaUtils.sneakyThrow(e);
			}
		} finally {
			if (raf != null) {
				try {
					raf.close();
				} catch (IOException e) {
					ErrorLogUtil.exceptionLog("Load map file error.", e,"filepath",mapPath);
				}
			}
		}
		if (log.isInfoEnabled()) {
			log.info("Load map width={}, height={}", mapData.getWidth(), mapData.getHeight());
		}

		mapData.setServerId(serverId);
		if (usingCache) {
			mapCache.putIfAbsent(mapFileName, mapData);
		}
		return mapData;
	}

	private void load(MapData mapData, RandomAccessFile raf) throws IOException {
		raf.seek(0);
		long length = raf.length();
		byte[] bytes = new byte[(int) length];
		raf.read(bytes);
		ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
		byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
		int height = byteBuffer.getInt();
		int width = byteBuffer.getInt();
		mapData.setHeight(height);
		mapData.setWidth(width);

		// raf.seek(0); // 从头开始读地格数据
		MapGrid[][] grids = new MapGrid[height][width];
		Set<Point> blocks = new HashSet<>();
		// 前后端约定：由y做外循环，x做内循环
		for (int y = 0; y < height; y++) {
			for (int x = 0; x < width; x++) {
				int regionId = byteBuffer.getInt();
				MapGrid grid = createMapGrid(Point.getInstance(x, y), regionId);
				grids[x][y] = grid;
				if (grid.isBlock()) {
					blocks.add(grid.getPosition());
				}
			}
		}
		int regionSize = byteBuffer.getInt();
		for (int i = 0; i < regionSize; i++) {
			int regionId = byteBuffer.getInt();
			int level = byteBuffer.getInt();
			int neighbourSize = byteBuffer.getInt();
			MapRegion mapRegion = new MapRegion(regionId, level);
			for (int j = 0; j < neighbourSize; j++) {
				mapRegion.getNeighbour().add(byteBuffer.getInt());
			}
			mapData.addRegion(mapRegion);
		}
		mapData.setGrids(grids);
		mapData.computeMapRegion();
		log.info("障碍个数：" + blocks.size() + "区域个数:{}", mapData.getRegionMap().keySet());
	}

	private MapGrid createMapGrid(Point point, int type) {
		return new MapGrid(point, type);
	}

}
