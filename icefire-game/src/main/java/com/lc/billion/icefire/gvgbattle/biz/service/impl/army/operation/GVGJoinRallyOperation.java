package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.model.alliance.war.AllianceWar;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceWarDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.war.AllianceWarService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.protocol.constant.PsAllianceWarInfoUpdateReason;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/26
 */
@Service
public class GVGJoinRallyOperation implements ArmyOperation {
	private static final Logger logger = LoggerFactory.getLogger(GVGJoinRallyOperation.class);
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private ArmyServiceImpl armyService;
	@Autowired
	private SceneServiceImpl sceneService;
	@Autowired
	private AllianceWarService warService;
	@Autowired
	private GVGStrongHoldService gvgStrongHoldService;
	@Autowired
	private AllianceWarDao warDao;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_JOIN;
	}

	@Override
	public void finishWork(ArmyInfo army) {
		throw new AlertException();
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		logger.info("[GVG]returnArrived, army: {}, role: {}", army.getPersistKey(), army.getRoleId());
		// 收回军队
		armyManager.takeBackArmy(army);
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		logger.info("[GVG]armyArrive, army: {}, role: {}", army.getPersistKey(), army.getRoleId());
		var role = army.getOwner();
		if(role == null){
			ErrorLogUtil.errorLog("[GVG]armyArrive role null", "armyId",army.getPersistKey(), "roleId",army.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		var targetNode = army.getTargetNode();
		if(targetNode == null){
			ErrorLogUtil.errorLog("[GVG]armyArrive target null",  "armyId",army.getPersistKey(), "roleId",army.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		var rallyContext = army.getRallyContext();
		if(rallyContext == null){
			ErrorLogUtil.errorLog("[GVG]armyArrive rally context null",  "armyId",army.getPersistKey(), "roleId",army.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		var leaderArmyId = rallyContext.getLeaderArmyId();

		ArmyInfo rallyArmy = armyManager.findById(leaderArmyId);
		if (rallyArmy == null) {
			ErrorLogUtil.errorLog("[GVG]armyArrive rallyArmy null",  "armyId",army.getPersistKey(), "roleId",army.getRoleId(),
					"leaderArmyId",leaderArmyId);
			armyManager.returnArmy(army);
			return;
		}

		if (rallyArmy.getWorkType() != ArmyWorkType.RALLYING) {
			ErrorLogUtil.errorLog("[GVG]armyArrive rallyArmy workType error",  "armyId",army.getPersistKey(), "roleId",army.getRoleId(),
					"leaderArmyId",leaderArmyId, "rallyArmyWorkType",rallyArmy.getWorkType());
			armyManager.returnArmy(army);
			return;
		}

		AllianceWar war = warDao.findById(rallyArmy.getPersistKey());
		if (war == null) {
			ErrorLogUtil.errorLog("[GVG]armyArrive rallyArmy workType error","armyId",army.getPersistKey(), "roleId",army.getRoleId(),
					"leaderArmyId",leaderArmyId, "rallyArmyWorkType",rallyArmy.getWorkType());
			armyManager.returnArmy(army);
			return;
		}

		// 不在集结中
		boolean inRally = false;
		List<Long> joinArmyIdList = rallyArmy.getRallyContext().getJoinArmyIdList();
		for (Long joinArmyId : joinArmyIdList) {
			ArmyInfo joinArmy = armyManager.findById(joinArmyId);
			if (joinArmy.getRoleId().equals(role.getPersistKey())) {
				inRally = true;
				break;
			}
		}

		if(!inRally){
			ErrorLogUtil.errorLog("[GVG]gvgJoinCommonCheck not in rally",
					"roleId",role.getId(), "targetNode",targetNode.getPersistKey(),
					"rallyArmyId",rallyArmy.getPersistKey(), "rallyArmyType",rallyArmy.getArmyType(),
					"rallyWorkType",rallyArmy.getWorkType(), "allianceId",role.getAllianceId(),
					"targetNodeId",rallyArmy.getTargetNodeId(), "attackerAllianceId",war.getAttackerAllianceId(),
					"leaderRoleId",rallyArmy.getRoleId());
			armyManager.returnArmy(army);
			return;
		}

		if(!gvgStrongHoldService.gvgJoinCommonCheck(role, targetNode, rallyArmy, war)) {
			ErrorLogUtil.errorLog("[GVG]armyArrive gvgJoinCommonCheck fail",
					"armyId",army.getPersistKey(), "roleId",army.getRoleId(),
					"leaderArmyId",leaderArmyId, "rallyArmyWorkType",rallyArmy.getWorkType());
			armyManager.returnArmy(army);
			return;
		}

		// 到达集结地，出发以后，车头会改成SETOUT，但车身不会改成SETOUT
		army.setWorkType(ArmyWorkType.RALLYING);
		armyManager.saveArmy(army);

		// 集结出发检查
		warService.checkRallySetout(leaderArmyId);

		// 从大地图中移除
		sceneService.remove(army);

		// 同步自身进度
		armyService.updateArmyProgress(army);

		// 同步战争状态
		warService.broadcastUpdate(war, PsAllianceWarInfoUpdateReason.JOIN_ARRIVE);
	}
}
