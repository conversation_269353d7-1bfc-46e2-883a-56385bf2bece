package com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.RoleGVGBattle;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.RoleGVGExtraInfo;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class RoleGVGExtraInfoDao extends RootDao<RoleGVGExtraInfo> {

    public RoleGVGExtraInfoDao() {
        super(RoleGVGExtraInfo.class, false);
    }

    public RoleGVGExtraInfo create(Long roleId) {
        RoleGVGExtraInfo entity = newEntityInstance();
        entity.setRoleId(roleId);
        return createEntity(Application.getServerId(), entity);
    }

    @Override
    protected MongoCursor<RoleGVGExtraInfo> doFindAll(int db ) {
        return dbFindAllForWorldEntity(db);
    }

    @Override
    protected void putMemoryIndexes(RoleGVGExtraInfo entity) {

    }

    @Override
    protected void removeMemoryIndexes(RoleGVGExtraInfo entity) {

    }

    @Override
    public void save(RoleGVGExtraInfo entity) {
        if (Application.isGVGBattleServer()) {
            super.save(entity);
        }
    }
}
