package com.lc.billion.icefire.gvgbattle.biz.service.rpc;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGVGBattleRemoteGVGControlService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGBattleRPCToGVGControlProxyService extends AbstractRPCProxyService {

	private RpcProxyBean<IGVGBattleRemoteGVGControlService> gvgBattleRemoteGVGControlService;

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.GVG_BATTLE };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GVG_CONTROL };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<IGVGBattleRemoteGVGControlService> rpcProxyBuilder = RpcProxyBuilder.create(IGVGBattleRemoteGVGControlService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		IGVGBattleRemoteGVGControlService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
		gvgBattleRemoteGVGControlService = new RpcProxyBean<IGVGBattleRemoteGVGControlService>(service, rpcClient);
		logger.info("rpc GVG_BATTLE to GVG_CONTROL {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
				gameServerConfig.getRpcPort());
		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		if (gvgBattleRemoteGVGControlService != null) {
			RpcClient rpcClient = gvgBattleRemoteGVGControlService.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		if (gvgBattleRemoteGVGControlService != null) {
			RpcClient rpcClient = gvgBattleRemoteGVGControlService.getRpcClient();
			rpcClient.setStop(true);
		}
		gvgBattleRemoteGVGControlService = null;
	}

	// @Override
	// protected void clearRPCClient() {
	// gvgBattleRemoteGVGControlService = null;
	// }

	@Override
	protected boolean containsRPCClient(int serverId) {
		return gvgBattleRemoteGVGControlService != null;
	}

	@Override
	protected boolean checkInstance() {
		return false;
	}

	@Override
	protected boolean createWait() {
		return true;
	}

	public IGVGBattleRemoteGVGControlService getGVGBattleRemoteGVGControlService() {
		if (gvgBattleRemoteGVGControlService != null) {
			return gvgBattleRemoteGVGControlService.getProxy();
		}
		return null;
	}

}
