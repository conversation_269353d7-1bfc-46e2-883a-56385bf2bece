package com.lc.billion.icefire.game.config;

import com.lc.billion.icefire.core.config.model.MetaServerType;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.exception.AlertException;
import com.longtech.ls.config.ServerType;

public class ConfigHelper {
	private static ConfigServiceImpl instance;

	public static ConfigServiceImpl getServiceInstance() {
		if (instance != null) {
			return instance;
		}

		instance = Application.getBean(ConfigServiceImpl.class);
		if (instance == null) {
			throw new AlertException("Spring not initialized yet");
		}
		return instance;
	}

	// 通过serverId获得MetaServerType
	public static MetaServerType getMetaServerType(int serverId) {
		ServerType serverType = Application.getConfigCenter().getServerType(serverId);
		if (ServerType.GAME == serverType) {
			return MetaServerType.BASE;
		}

		if (ServerType.KVK_SEASON == serverType) {
			return MetaServerType.KVK;
		}

		return null;
	}
}
