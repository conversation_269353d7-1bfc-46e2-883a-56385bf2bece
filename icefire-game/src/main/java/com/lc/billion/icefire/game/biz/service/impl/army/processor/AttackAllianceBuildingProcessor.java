package com.lc.billion.icefire.game.biz.service.impl.army.processor;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.RegionCapitalConfig;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.AllianceBuildingNode;
import com.lc.billion.icefire.game.biz.model.scene.node.RegionCapitalNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.alliancebuilding.AllianceBuildingServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.regioncapital.RegionCapitalService;
import com.lc.billion.icefire.protocol.structure.PsArmyProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 单人攻击州府
 */
@Service
public class AttackAllianceBuildingProcessor extends ArmyProcessor {
	@Autowired
	private AllianceBuildingServiceImpl allianceBuildingService;

	@Autowired
	private RegionCapitalService regionCapitalService;

	@Autowired
	private NoticeServiceImpl noticeService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.ATTACK_ALLIANCE_BUILDING;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		// 目标类型检测
		if (targetNode.getNodeType() != SceneNodeType.ALLIANCE_BUILDING) {
			return false;
		}
		if (role.getAllianceId() == null) {
			return false;
		}

		AllianceBuildingNode allianceBuildingNode = (AllianceBuildingNode) targetNode;

		// 是否有联盟
		Alliance alliance = allianceDao.findById(role.getAllianceId());
		if (alliance == null) {
			ErrorLogUtil.errorLog("check role alliance isn't find", "roleId",role.getId(), "allianceId",role.getAllianceId());
			return false;
		}

		// 是同一个联盟
		long belongId = allianceBuildingNode.getAllianceId();
		if (alliance.getPersistKey().equals(belongId)) {
			ErrorLogUtil.errorLog("check role node occupy belong is same", "roleId",role.getId(),"node", allianceBuildingNode.getMetaId(), "belong",belongId);
			return false;
		}

		if (allianceBuildingNode.isResourceCenter()) {
			noticeService.notice(role, "AllianceBuildingTips51", false);
			return false;
		}

		// 是否是争夺状态
		if (allianceBuildingNode.isInAllianceDomain()) {
			RegionCapitalNode capitalNode = regionCapitalService.getRegionCapital(targetNode.getCurrentServerId(), targetNode.getRegionId());
			if (capitalNode == null) {
				return false;
			}
			if (!capitalNode.isAttack()) {
				noticeService.notice(role, "AllianceBuildingTips74", false);
				return false;
			}
		}

		return true;
	}

	@Override
	protected void start(ArmyInfo army) {
		// 添加到联盟战争
		warService.createWar(army);
		sdp.getArmyServiceImpl().addArmyToAoi(army, addToAoi());
	}

	@Override
	protected PsArmyProgress toArmyProgressInfo(ArmyInfo army, PsArmyProgress info) {
		return null;
	}
}
