package com.lc.billion.icefire.gvgbattle.biz.config.auto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.config.model.StringKeyIntValue;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Config(name = "gvgBuilding", metaClass = GvgBuildingConfig.GvgBuildingMeta.class)
public class GvgBuildingConfig {
    private static final Logger logger = LoggerFactory.getLogger(GvgBuildingConfig.class);

    @MetaMap
    private final Map<String, GvgBuildingMeta> metaMap = new HashMap<>();

    private final Map<Integer,List<GvgBuildingMeta>> type2Map = new HashMap<>();

    public void init(List<GvgBuildingMeta> list) {
        List<GvgBuildingMeta> needFilter = Lists.newArrayList();
        for(GvgBuildingMeta meta : list) {
            /**
             * 根据服务器类型 确定是否保留配置
             * 0为GVG地图；
             * 1为TVT中的普通GVG地图
             **/
            if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
                if(meta.getMapType() == 0){
                    needFilter.add(meta);
                }
            }else if(Application.getServerType() == ServerType.GVG_BATTLE || Application.getServerType() == ServerType.GVG_CONTROL){
                if(meta.getMapType() == 1){
                    needFilter.add(meta);
                }
            }
        }

        for(GvgBuildingMeta meta:needFilter){
            metaMap.remove(meta.getId());
            logger.info("GvgBuildingConfig 移除不适配的配置，id:{},mapType:{},currentServerType:{}",meta.getId(),meta.getMapType(),Application.getServerType());
        }

        for(GvgBuildingMeta meta : metaMap.values()) {
            List<GvgBuildingMeta> metas = type2Map.computeIfAbsent(meta.getBuildingtype(), k -> new ArrayList<>());
            metas.add(meta);
        }
    }

    public Map<String, GvgBuildingMeta> getMetaMap() {
        return metaMap;
    }

    public GvgBuildingMeta get(String id) {
        return metaMap.get(id);
    }

    public static class GvgBuildingMeta extends AbstractMeta {
        private String buildingName;
        private int buildingtype;
        private String buildingSize;
        @JsonIgnore
        private Point buildingPosition;
        @JsonIgnore
        private List<AttributeValue> buildingBuff;

        private List<String> buildingBuffIdList;

        private int buildingPoints;
        @Setter
        private int buildingPoints2;
        @Setter
        private int buildingPoints3;
        private int buildingDurability;
        private int buildingDurability2;
        private int buildingDurability3;
        private int buildingMove;
        private int buildingMove2;
        private int buildingMove3;
        @JsonIgnore
        private List<StringKeyIntValue> buildingNpcHigh;
        @JsonIgnore
        private List<StringKeyIntValue> buildingNpcLow;
        private int mapType;
        private int unlockTime;
        private int buildingRange;
        private String buildingOccupyBroadcast;
        private String buildingOpenShow;

        public void init(JsonNode json) {
            buildingPosition = Point.parseFromString(json.path("buildingPosition").asText());
//            buildingBuff = MetaUtils.parseObjectList(json.path("buildingBuff").asText(), AttributeValue.class, AbstractMeta.META_SEPARATOR_1, AbstractMeta.META_SEPARATOR_2);

            buildingBuffIdList = MetaUtils.parseStringList(json.path("buildingBuff").asText(), AbstractMeta.META_SEPARATOR_2);

            buildingNpcHigh = MetaUtils.parseObjectList(json.path("buildingNpcHigh").asText(), StringKeyIntValue.class, AbstractMeta.META_SEPARATOR_1, AbstractMeta.META_SEPARATOR_2);
            buildingNpcLow = MetaUtils.parseObjectList(json.path("buildingNpcLow").asText(), StringKeyIntValue.class, AbstractMeta.META_SEPARATOR_1, AbstractMeta.META_SEPARATOR_2);
        }

        public int getMapType() {
            return mapType;
        }

        public String getBuildName() {
            return buildingName;
        }

        public void setBuildingName(String buildingName) {
            this.buildingName = buildingName;
        }

        public int getBuildingtype() {
            return buildingtype;
        }

        public String getBuildingSize() {
            return buildingSize;
        }

        public Point getBuildingPosition() {
            return buildingPosition;
        }

        public List<AttributeValue> getBuildingBuff() {
            return buildingBuff;
        }

        public int getBuildingPoints() {
            return buildingPoints;
        }

        public int firstOccupyScore() {
            return buildingPoints2;
        }

        public int occupyScore() {
            return buildingPoints3;
        }

        public int getBuildingDurability() {
            return buildingDurability;
        }

        public int getBuildingDurability2() {
            return buildingDurability2;
        }

        public int getBuildingDurability3() {
            return buildingDurability3;
        }

        public int getBuildingMove() {
            return buildingMove;
        }

        public int getBuildingMove2() {
            return buildingMove2;
        }

        public int getBuildingMove3() {
            return buildingMove3;
        }

        public List<StringKeyIntValue> getBuildingNpcHigh() {
            return buildingNpcHigh;
        }

        public List<StringKeyIntValue> getBuildingNpcLow() {
            return buildingNpcLow;
        }

        public int getUnlockTime(){
            return unlockTime;
        }

        public List<String> getBuildingBuffIdList() {
            return buildingBuffIdList;
        }

        public void setBuildingBuffIdList(List<String> buildingBuffIdList) {
            this.buildingBuffIdList = buildingBuffIdList;
        }

        public void setUnlockTime(int unlockTime) {
            this.unlockTime = unlockTime;
        }

        public String getBuildingOccupyBroadcast() {
            return buildingOccupyBroadcast;
        }

        public void setBuildingOccupyBroadcast(String buildingOccupyBroadcast) {
            this.buildingOccupyBroadcast = buildingOccupyBroadcast;
        }

        public String getBuildingOpenShow() {
            return buildingOpenShow;
        }

        public void setBuildingOpenShow(String buildingOpenShow) {
            this.buildingOpenShow = buildingOpenShow;
        }

        public int getBuildingRange() {
            return buildingRange;
        }

        public void setBuildingRange(int buildingRange) {
            this.buildingRange = buildingRange;
        }
    }

    public List<Point> getGvgBuildsSizePoints(GvgBuildingMeta meta){
        List<Point> sizePoints = new ArrayList<>();

        String[] strings = MetaUtils.parse(meta.getBuildingSize(), AbstractMeta.META_SEPARATOR_2);
        if (JavaUtils.bool(strings)) {
            for (String string : strings) {
                String[] rangeXAndY = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_1);
                int x = meta.getBuildingPosition().getX() + Integer.parseInt(rangeXAndY[0]);
                int y = meta.getBuildingPosition().getY() + Integer.parseInt(rangeXAndY[1]);
                Point p = Point.getInstance(x, y);
                sizePoints.add(p);
            }
        }

        return sizePoints;
    }

    public List<Point> getGvgResSizePoints(GvgBuildingMeta meta, Point position){
        List<Point> sizePoints = new ArrayList<>();

        String[] strings = MetaUtils.parse(meta.getBuildingSize(), AbstractMeta.META_SEPARATOR_2);
        if (JavaUtils.bool(strings)) {
            for (String string : strings) {
                String[] rangeXAndY = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_1);
                int x = position.getX() + Integer.parseInt(rangeXAndY[0]);
                int y = position.getY() + Integer.parseInt(rangeXAndY[1]);
                Point p = Point.getInstance(x, y);
                sizePoints.add(p);
            }
        }

        return sizePoints;
    }

    public GvgBuildingMeta getBuildingMetaByType(int type){
        List<GvgBuildingMeta> metas = type2Map.get(type);
        if(metas != null){
            return metas.get(0);
        }

        return null;
    }

    public List<GvgBuildingMeta> getBuildingMetasByType(int type){
        return type2Map.get(type);
    }
}
