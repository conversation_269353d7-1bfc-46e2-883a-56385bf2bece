package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightContext;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BIMarchResult;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGBattleScoreService;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * GVG攻击玩家处理
 *
 * <AUTHOR>
 * @date 2021/2/4
 */
@Service
public class GVGCityAttackOperation extends AbstractGvgFightOperation {
    @Autowired
    private AllianceServiceImpl allianceService;
    @Autowired
    private GVGStrongHoldService GVGStrongHoldService;
    @Autowired
    private GVGBattleScoreService gvgBattleScoreService;

    @Override
    public ArmyType getArmyType() {
        return ArmyType.GVG_ATTACK_PLAYER;
    }

    @Override
    protected boolean check(ArmyInfo army, SceneNode sceneNode) {
        if (sceneNode == null || sceneNode.getNodeType() != SceneNodeType.CITY) {
            armyManager.sendPVPTargetChangeMail(army, EmailConstants.ARMY_TARGET_DISAPPEAR);
            return false;
        }
        // 是同一个联盟
        if (allianceService.isSameAlliance(army.getRoleId(), sceneNode.getPersistKey())) {
            return false;
        }
        if (srvDpd.getProtectionService().hasProtection((RoleCity) sceneNode)) {
            armyManager.sendPVPTargetChangeMail(army, EmailConstants.ARMY_TARGET_PROTECTED_RALLY);
            return false;
        }
        return true;
    }

    @Override
    protected void endBattle(ArmyInfo armyInfo) {
        if (Objects.requireNonNull(armyInfo.getWorkType()) == ArmyWorkType.BATTLE) {
            FightContext fightContext = armyInfo.getFightContext();
            armyManager.worldDefendRet(armyInfo, fightContext.isWin() ? BIMarchResult.DEFEATED : BIMarchResult.VICTORY);
            if (fightContext.isWin()) {
                winBattle(armyInfo);
            }
            gvgBattleScoreService.onGVGFightEnd(armyInfo);
            // TODO 军营里的兵过一遍
        } else {
            throw new BizException("ArmyWorkType mismatch: " + armyInfo.getWorkType());
        }
    }

    private void winBattle(ArmyInfo army) {
        // 结算奖励
        FightArmy defenderArmy = army.getFightContext().getDefender();
        RoleCity targetCity = null;
        if (defenderArmy.getTargetId() != null) {
            targetCity = roleCityManager.getRoleCity(defenderArmy.getTargetId());
        }
        if (targetCity == null) {
            ErrorLogUtil.errorLog("战斗异常|不应该进到这里,战斗结束了,但是目标玩家没有主城了");
            return;
        }
        // GVG 不掠夺资源
        // 败者食尘
        // pvpDespoil(army);
        srvDpd.getRoleCityService().addCastleBroken(targetCity, army);
    }

    @Override
    public void finishWork(ArmyInfo attackerMainArmy) {
        super.finishWork(attackerMainArmy);
        armyManager.marchRetBILog(attackerMainArmy);
        armyManager.returnArmy(attackerMainArmy);
    }
}
