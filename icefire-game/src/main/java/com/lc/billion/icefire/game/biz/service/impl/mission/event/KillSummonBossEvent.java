package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;

public class KillSummonBossEvent extends AbstractMissionEvent {

    @Override
    public MissionType getType() {
        return MissionType.KILL_SUMMON_BOSS;
    }

    @Override
    public boolean check(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        return meta.getPara1().equals(params[0]);
    }

    @Override
    public boolean effect(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        long amount = item.getProgress() + 1;
        setAmount(item, meta, amount,true);
        return true;
    }
}
