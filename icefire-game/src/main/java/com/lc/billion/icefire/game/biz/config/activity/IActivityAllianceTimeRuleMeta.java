package com.lc.billion.icefire.game.biz.config.activity;

import com.lc.billion.icefire.game.biz.model.alliance.affair.AllianceAffairType;
import com.lc.billion.icefire.game.biz.model.alliance.rank.Auth;
import com.lc.billion.icefire.game.biz.model.email.SystemEmailType;

/**
 * 联盟活动时间调度规则
 */
public interface IActivityAllianceTimeRuleMeta {

    /**
     * 需要权限
     * @return
     */
    Auth getAuth();

    /**
     * 可否预设
     * @return
     */
    boolean isSchedule();

    /**
     * 是否有日历
     * @return
     */
    AllianceAffairType getAffairType();

    /**
     * 实际持续时间
     * @return
     */
    long getLastMilliseconds();

    SystemEmailType getMailType();

    String getStartMailNotice();

    String getFirstScheduleNotice();

    String getChangeScheduleNotice();

    String getCancelScheduleNotice();
}
