package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import lombok.Getter;
import lombok.Setter;
import org.jongo.marshall.jackson.oid.MongoId;

/**
 * <AUTHOR>
 *
 */
public class RoleGVGBattle extends AbstractEntity implements IRolesEntity {
	@MongoId
	private Long roleId;

	private Long allianceId;

	// 战场积分
	private int battlePoint = 0;
	// 杀敌数
	private int killEnemyNum = 0;
	// 杀敌积分
	private int killEnemyScore;

	// 迁城次数
	private int moveCount = 0;

	// 购买体力次数
	private int buyStaminaCount = 0;

	// 购买迁城次数
	private int buyMoveCount = 0;

	// 是否首次进入GVG
	private boolean isFirstEnter = false;

	// 玩家速度
	private int speed = 0;

	// 速度上次计算时间
	private long lastCalTime = 0;

	// 头像
	private String head;

	// 名字
	private String name;

	// 性别
	private int sex;

	// 头像框
	private String headFrame;

	// 联盟名字
	private String allianceName;

	// 联盟简称
	private String allianceAliasName;

	// 上次使用恢复伤兵技能的时间
	private long lastCureWoundedTime = 0;

	// 免费迁城的时间
	private long freeMoveCityTime = 0;

	// 迁城消耗的道具数量
	private int moveCityItemCostCount = 0;

	// 所属服务器
	@Setter @Getter
	private int serverId = 0;

	/************ TVT start*****************/
	private int result;			// 战斗结果
	private int score;				// 积分:也是真实实力积分
	private int changeScore;		// 本场变化积分
	private int rank;				// 排名
	private int changeRank;		// 排名变化


	private int killNpcScore;		// 杀怪积分
	private int gatherScore;		// 采集积分

	private int wuchaoScore;		// 乌巢积分
	private int occupyScore;		// 占领加分
	private int firstOccupyScore;	// 首占加分

	private int mvp;				// 0:否 1:mvp 2:svp
	private int tvtHideScore; 		// tvt隐藏分
	private int teamId;			// 队伍Id

	private int oldRank;        //进入战场时的排名

	private int gvgStamine;
	/************ TVT end*****************/

	public int getBattlePoint() {
		return battlePoint;
	}

	public void setBattlePoint(int battlePoint) {
		this.battlePoint = battlePoint;
	}

	public void addBattlePoint(int add) {
		this.battlePoint += add;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getRoleId() {
		return roleId;
	}

	public int getBuyMoveCount() {
		return buyMoveCount;
	}

	public int getMoveCount() {
		return moveCount;
	}

	public int getBuyStaminaCount() {
		return buyStaminaCount;
	}

	public void setBuyStaminaCount(int buyStaminaCount) {
		this.buyStaminaCount = buyStaminaCount;
	}

	public void setBuyMoveCount(int buyMoveCount) {
		this.buyMoveCount = buyMoveCount;
	}

	public void setMoveCount(int moveCount) {
		this.moveCount = moveCount;
	}

	public Long getAllianceId() {
		return allianceId;
	}

	public void setAllianceId(Long allianceId) {
		this.allianceId = allianceId;
	}

	public boolean isFirstEnter() {
		return isFirstEnter;
	}
	public void setFirstEnter(boolean firstEnter) {
		this.isFirstEnter = firstEnter;
	}

	public int getSpeed() {
		return speed;
	}
	public void setSpeed(int speed) {
		this.speed = speed;
	}

	public void setLastCalTime(long lastCalTime) {
		this.lastCalTime = lastCalTime;
	}
	public long getLastCalTime() {
		return lastCalTime;
	}

	public void setSex(int sex) {
		this.sex = sex;
	}
	public int getSex() {
		return sex;
	}

	public void setHeadFrame(String headFrame) {
		this.headFrame = headFrame;
	}
	public String getHeadFrame() {
		return headFrame;
	}

	public void setAllianceAliasName(String allianceAliasName) {
		this.allianceAliasName = allianceAliasName;
	}
	public String getAllianceAliasName() {
		return allianceAliasName;
	}

	public String getHead() {
		return head;
	}
	public void setHead(String head) {
		this.head = head;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getAllianceName() {
		return allianceName;
	}
	public void setAllianceName(String allianceName) {
		this.allianceName = allianceName;
	}

	public long getLastCureWoundedTime() {
		return lastCureWoundedTime;
	}
	public void setLastCureWoundedTime(long lastCureWoundedTime) {
		this.lastCureWoundedTime = lastCureWoundedTime;
	}

	public void setKillNpcScore(int killNpcScore) {
		this.killNpcScore = killNpcScore;
	}
	public int getKillNpcScore() {
		return killNpcScore;
	}

	public int getKillEnemyScore() {
		return killEnemyScore;
	}
	public void setKillEnemyScore(int killEnemyScore) {
		this.killEnemyScore = killEnemyScore;
	}

	public void setGatherScore(int gatherScore) {
		this.gatherScore = gatherScore;
	}
	public int getGatherScore() {
		return gatherScore;
	}

	public int getResult() {
		return result;
	}
	public void setResult(int result) {
		this.result = result;
	}

	public void setScore(int score) {
		this.score = score;
	}
	public int getScore() {
		return score;
	}

	public int getChangeScore() {
		return changeScore;
	}
	public void setChangeScore(int changeScore) {
		this.changeScore = changeScore;
	}

	public int getRank() {
		return rank;
	}
	public void setRank(int rank) {
		this.rank = rank;
	}

	public int getChangeRank() {
		return changeRank;
	}
	public void setChangeRank(int changeRank) {
		this.changeRank = changeRank;
	}

	public int getTvtHideScore() {
		return tvtHideScore;
	}
	public void setTvtHideScore(int tvtHideScore) {
		this.tvtHideScore = tvtHideScore;
	}

	public int getMvp() {
		return mvp;
	}
	public void setMvp(int mvp) {
		this.mvp = mvp;
	}

	public void setTeamId(int teamId) {
		this.teamId = teamId;
	}
	public int getTeamId() {
		return teamId;
	}

	public int getOldRank() {
		return oldRank;
	}

	public void setOldRank(int oldRank) {
		this.oldRank = oldRank;
	}

	public int getGvgStamine() {
		return gvgStamine;
	}

	public void setGvgStamine(int gvgStamine) {
		this.gvgStamine = gvgStamine;
	}

	@Override
	public void setPersistKey(Long id) {
		roleId = id;
	}

	@Override
	public Long getPersistKey() {
		return roleId;
	}

	@Override
	public Long getGroupingId() {
		return roleId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

    public long getFreeMoveCityTime() {
        return freeMoveCityTime;
    }

    public void setFreeMoveCityTime(long freeMoveCityTime) {
        this.freeMoveCityTime = freeMoveCityTime;
    }

    public int getMoveCityItemCostCount() {
        return moveCityItemCostCount;
    }

    public void setMoveCityItemCostCount(int moveCityItemCostCount) {
        this.moveCityItemCostCount = moveCityItemCostCount;
    }

    // 获取杀敌总数
	public int getKillEnemyNum() {
		return killEnemyNum;
	}

	public void addKillEnemyNum(int addition){
		killEnemyNum += addition;
	}

	//  获取联盟贡献积分
	public int getContributeAllianceScore() {
		return battlePoint - killEnemyScore;	// 目前联盟贡献积分就是个人总积分减去击杀所得积分
	}

	public int getWuchaoScore() {
		return wuchaoScore;
	}

	public void setWuchaoScore(int wuchaoScore) {
		this.wuchaoScore = wuchaoScore;
	}

	public int getOccupyScore() {
		return occupyScore;
	}

	public void setOccupyScore(int occupyScore) {
		this.occupyScore = occupyScore;
	}

	public int getFirstOccupyScore() {
		return firstOccupyScore;
	}

	public void setFirstOccupyScore(int firstOccupyScore) {
		this.firstOccupyScore = firstOccupyScore;
	}
}
