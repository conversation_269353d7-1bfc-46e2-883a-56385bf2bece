package com.lc.billion.icefire.game.biz.service.impl.army.processor;

import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.NewResNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 新资源点PVP
 * 
 * <AUTHOR>
 *
 */

@Service
public class NewResPvpProcessor extends AbstractResPvpProcessor {
	@Autowired
	private LegionService legionService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.NEW_RES_PVP;
	}

	@Override
	protected boolean check(Role role, SceneNode targetNode, ArmySetoutParam param) {
		if (targetNode.getNodeType() != SceneNodeType.NEW_RES) {
			return false;
		}
		NewResNode resNode = (NewResNode) targetNode;
		// 目标点无人占领 || 是我本人 || 占领着和我同盟 || 没有军队正在采集
		if (!resNode.hasOwner() || role.getPersistKey().equals(resNode.getRoleId()) || allianceService.isSameAlliance(role.getPersistKey(), resNode.getRoleId())
				|| (!resNode.inGathering() && !resNode.isStation())) {
			return false;// 不会发生战斗
		}
		// 同军团 无论k、o服， 都不可以攻击
		if (legionService.isSameLegion(role, roleManager.getRole(resNode.getRoleId()))) {
			return false;// 不会发生战斗
		}
		return true;
	}
}
