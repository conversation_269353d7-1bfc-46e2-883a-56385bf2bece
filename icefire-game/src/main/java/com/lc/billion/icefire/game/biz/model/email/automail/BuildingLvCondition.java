package com.lc.billion.icefire.game.biz.model.email.automail;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.manager.RoleCityManager;
import com.lc.billion.icefire.game.biz.model.email.AutoMailTriggerType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;

/**
 * 建筑
 * <AUTHOR>
 * @since 2021-8-23
 */
public class BuildingLvCondition implements IAutoMailCondition{
    private int groupId;
    private int lvMin;
    private int lvMax;

    public int getGroupId() {
        return groupId;
    }

    public int getLvMin() {
        return lvMin;
    }

    public int getLvMax() {
        return lvMax;
    }

    @Override
    public AutoMailTriggerType getType() {
        return AutoMailTriggerType.BUILDING_LV;
    }

    @Override
    public void initCondition(String[] initData) {
        //initData : 1|groupId|lvMin|lvMax or 1|groupId|lvMin
        groupId = Integer.parseInt(initData[1]);
        lvMin = Integer.parseInt(initData[2]);
        lvMax = initData.length == 4 ? Integer.parseInt(initData[3]) : Integer.MAX_VALUE;
    }

    @Override
    public boolean metaCheck(Role role) {
       return onCheck(role);
    }

    @Override
    public boolean onCheck(Role role) {
        RoleCityManager cityManager = Application.getBean(RoleCityManager.class);
        int buildingLv = cityManager.getBuildingMaxLevelByGroupId(role.getPersistKey(), groupId);
        return lvMin <= buildingLv && buildingLv <= lvMax;
    }

    @Override
    public boolean triggerCheck(Role role, Object... params) {
        int triggerId = (int) params[0];
        if(groupId != triggerId)
            return false;
        int buildingLv = (int)params[1];
        return lvMin <= buildingLv && buildingLv <= lvMax;
    }
}
