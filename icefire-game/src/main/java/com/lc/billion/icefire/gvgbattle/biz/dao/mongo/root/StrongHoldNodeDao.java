package com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root;

import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Repository
public class StrongHoldNodeDao extends RootDao<StrongHoldNode> {
	public StrongHoldNodeDao() {
		super(StrongHoldNode.class, true);
	}

	@Override
	protected MongoCursor<StrongHoldNode> doFindAll(int db) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(StrongHoldNode entity) {

	}

	@Override
	protected void removeMemoryIndexes(StrongHoldNode entity) {

	}

	public StrongHoldNode create(String metaId, Point position, long unlockTime) {
		int db = Application.getServerId();
		StrongHoldNode strongHoldNode = newEntityInstance();
		strongHoldNode.setMetaId(metaId);
		strongHoldNode.setPosition(position);
		strongHoldNode.setUnlockTime(unlockTime);
		return createEntity(db, strongHoldNode);
	}

	public void deleteAll() {
		delete(findAll());
	}

	public List<StrongHoldNode> findBuildingByType(BuildingType buildingType) {
		List<StrongHoldNode> result = new ArrayList<>();
		if (entities == null) {
			return result;
		}

		for (StrongHoldNode value : entities.values()) {
			if (value.getBuildingType() == buildingType) {
				result.add(value);
			}
		}
		return result;
	}

}
