package com.lc.billion.icefire.gvgbattle.biz.tick;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.GcPiliCheAttack;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class GVGPiliCheAttackTicker extends AbstractTicker<StrongHoldNode> {

    @Autowired
    private StrongHoldNodeDao strongHoldNodeDao;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private GVGStrongHoldService gvgStrongHoldService;
    @Autowired
    private SceneServiceImpl sceneService;

    public GVGPiliCheAttackTicker() {
        super(TimeUtil.SECONDS_MILLIS);
    }


    @Override
    protected void tick(StrongHoldNode pilieCheNode, long now) {
        List<StrongHoldNode> guanduList = strongHoldNodeDao.findBuildingByType(BuildingType.GuanDu);
        if (!JavaUtils.bool(guanduList)) {
            return ;
        }

        long piliCheNextAttackTime = gvgStrongHoldService.getPiliCheNextAttackTime(pilieCheNode);
        if (piliCheNextAttackTime == 0) {
            return;
        }
        if (now < piliCheNextAttackTime) {
            return;
        }

        pilieCheNode.setLastActionTime(now);
        strongHoldNodeDao.save(pilieCheNode);

        StrongHoldNode guanduNode = guanduList.get(0);

        sendPiliCheAttack(pilieCheNode.getPersistKey(), guanduNode.getPersistKey());
        gvgStrongHoldService.piliCheAttackGuandu();

        sceneService.update(pilieCheNode, null);
    }

    private void sendPiliCheAttack(long attackId, long targetId) {
        worldService.run(bean -> {
            extracted(attackId, targetId, bean);
        });
    }

    private void extracted(long attackId, long targetId, Player bean) {
        if (bean.getOnlineState().getType() == AbstractOnlineState.Type.GAMING) {
            Role role = worldService.getWorld().getRole(bean.getRoleId());
            if (role == null) {
                return;
            }
            GcPiliCheAttack message = new GcPiliCheAttack();
            message.setAttackUid(attackId);
            message.setTargetUid(targetId);
            role.send(message);
        }
    }

    @Override
    protected Collection<StrongHoldNode> findAll() {
        if (!Application.isBattleServer()) {
            return null;
        }
        return strongHoldNodeDao.findBuildingByType(BuildingType.PiLiChe);
    }
}
