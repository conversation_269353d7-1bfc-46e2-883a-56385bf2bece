package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import java.io.Serializable;

/**
 * 战报简要信息
 * */
public class SimpleReportData implements Serializable {
	// sdk 邮件服生成的邮件id
	private String mailId;
	// 攻击方玩家名字：单人或者集结车头
	private String name;
	// 攻击方玩家联盟简称：单人或者集结车头
	private String allianceAliasName;
	// 进攻方击杀防守方战力
	private long killPower;
	// 战斗时间
	private long battleTime;
	// 战斗结果
	private int result = -1;
	// 战斗类型
	private int type = -1;
	// 邮件类型 1:建筑 2:总览 3:二者都是
	private int mailType = -1;
	// 胜利方
	private int winAllianceId = -1;
	// 邮件归属的角色id
	private Long roleId;

	public String getMailId() {
		return mailId;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public String getAllianceAliasName() {
		return allianceAliasName;
	}

	public void setAllianceAliasName(String allianceAliasName) {
		this.allianceAliasName = allianceAliasName;
	}

	public long getBattleTime() {
		return battleTime;
	}

	public long getKillPower() {
		return killPower;
	}

	public void setBattleTime(long battleTime) {
		this.battleTime = battleTime;
	}

	public void setKillPower(long killPower) {
		this.killPower = killPower;
	}

	public void setMailId(String mailId) {
		this.mailId = mailId;
	}

	public void setResult(int result) {
		this.result = result;
	}

	public int getResult() {
		return result;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getType() {
		return type;
	}

	public int getMailType() {
		return mailType;
	}

	public void setMailType(int mailType) {
		this.mailType = mailType;
	}

	public int getWinAllianceId() {
		return winAllianceId;
	}

	public void setWinAllianceId(int winAllianceId) {
		this.winAllianceId = winAllianceId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Long getRoleId() {
		return roleId;
	}
}

