package com.lc.billion.icefire.gvgbattle.biz.tick;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.StrongHoldNode;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.StrongHoldNodeDao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * <AUTHOR>
 * @des：非实时性的联盟点数增加，绑定在建筑上的
 *
 */
@Service
public class GVGStrongHoldNodeTicker extends AbstractTicker<StrongHoldNode> {
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private StrongHoldNodeDao strongHoldNodeDao;
	@Autowired
	private GVGStrongHoldService GVGStrongHoldService;

	public GVGStrongHoldNodeTicker() {
		super(TimeUtil.SECONDS_MILLIS);
	}

	@Override
	protected void tick(StrongHoldNode node, long now) {
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		// 活动开始才走倒计时
		if (!GVGStrongHoldService.isMoveGvgActivityStage(now)) {
			return;
		}

		// Buff生效
		if (node.getOccupyState() == StrongHoldNode.SHOccupyState.Occupying
				&& node.getOccupyTime() > 0 && now >= node.getOccupyTime()
				&& node.getAllianceId() > 0) {
			GVGStrongHoldService.onOccupied(node);
		}
	}

	@Override
	protected Collection<StrongHoldNode> findAll() {
		return strongHoldNodeDao.findAll();
	}

}
