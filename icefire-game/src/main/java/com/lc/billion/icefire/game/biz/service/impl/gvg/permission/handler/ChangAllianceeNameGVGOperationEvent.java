package com.lc.billion.icefire.game.biz.service.impl.gvg.permission.handler;

import com.lc.billion.icefire.game.biz.model.activity.ActivityStatus;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.service.impl.gvg.permission.GVGOperationEvent;
import com.lc.billion.icefire.game.biz.service.impl.gvg.permission.GVGOperationType;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg.GVGActivityStatus;
import com.lc.billion.icefire.protocol.constant.PsGVGActivityStatus;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Service
public class ChangAllianceeNameGVGOperationEvent extends GVGOperationEvent {

	public ChangAllianceeNameGVGOperationEvent() {
		super(GVGOperationType.CHANGEALLIANCENAME);
	}

	@Override
	public boolean checkImpl(int serverId, Long allianceId, Long operaterId, Long targeterId) {
		if (allianceId == null) {
			return false;
		}
		// 联盟是否报名并且有玩家入场了
		Map<GvgMatchType, GVGAllianceSignUpInfoVo> map = gvgDataVoManager.findGVGAllianceSignUpInfoVos(allianceId);

		if (map != null && map.size() > 0) {
			ActivityVo gvgActivityVo = gvgDataVoManager.findGvgActivityVo();
			if (gvgActivityVo == null || gvgActivityVo.getStatus() != ActivityStatus.START) {
				return true;
			}
			GVGActivityContext activityContext = gvgActivityVo.getActivityContext();
			if (activityContext == null) {
				return true;
			}

			GVGActivityStatus gvgActivityStatus = activityContext.getGvgActivityStatus();
			// 已经开始入场了
			for (GVGAllianceSignUpInfoVo gvgAllianceSignUpInfoVo : map.values()) {

				if (activityContext.getRound() != gvgAllianceSignUpInfoVo.getRound()) {
					continue;
				}

				if (gvgActivityStatus.getGvgActivityStatus() == PsGVGActivityStatus.SIGNUP || gvgActivityStatus.getGvgActivityStatus() == PsGVGActivityStatus.ADMITTANCE_ADVANCE  || gvgActivityStatus.getGvgActivityStatus() == PsGVGActivityStatus.ADMITTANCE) {
					return false;
				}

			}
		}
		return true;
	}

}
