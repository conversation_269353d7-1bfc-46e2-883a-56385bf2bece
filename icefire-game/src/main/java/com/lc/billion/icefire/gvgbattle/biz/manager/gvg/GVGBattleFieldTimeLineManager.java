package com.lc.billion.icefire.gvgbattle.biz.manager.gvg;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.TvtActivityStatus;
import com.lc.billion.icefire.tvtcontrol.biz.model.activity.context.TvtActivityContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.manager.AbstractRoleManager;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.context.GVGActivityContext;
import com.lc.billion.icefire.gvgcontrol.biz.model.activity.gvg.GVGActivityStatus;
import com.lc.billion.icefire.protocol.GcGvgBattleServerTimeInfo;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.longtech.ls.config.ServerType;

/**
 * <AUTHOR>
 *
 */
@Component
public class GVGBattleFieldTimeLineManager extends AbstractRoleManager {

	private static final Logger logger = LoggerFactory.getLogger(GVGBattleFieldTimeLineManager.class);

	@Autowired
	private GVGBattleDataVoManager gvgDataVoManager;
	@Autowired
	private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;

	@Override
	public void onCreateRole(Role role, Player player) {
	}

	@Override
	protected void onCreateRoleFailed(Role role) {

	}

	@Override
	public void beforeLogin(Role role) {

	}

	@Override
	public void afterLogin(Role role) {
		if (ServerConfigManager.getInstance().getWorldMapConfig().isBattleServer()) {
			GcGvgBattleServerTimeInfo gcGvgBattleServerTimeInfo = new GcGvgBattleServerTimeInfo();
			ActivityVo activityVo = gvgDataVoManager.findActivityVo();
			if (activityVo != null) {
				if(Application.getServerType() == ServerType.GVG_BATTLE){
					GVGActivityContext gvgActivityContext = activityVo.getActivityContext();
					if (gvgActivityContext != null) {
						GVGActivityStatus gvgActivityStatus = gvgActivityContext.getGvgActivityStatus();
						if (gvgActivityStatus == GVGActivityStatus.ADMITTANCE || gvgActivityStatus == GVGActivityStatus.ADMITTANCE_ADVANCE) {
							GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
							if (gvgBattleFieldTimeLine == null) {
								throw new AlertException("战场服缺少gvgBattleFieldTimeLine");
							} else {
								gvgBattleFieldTimeLine.putGvgBattleServerTimeInfo(gcGvgBattleServerTimeInfo);
								role.send(gcGvgBattleServerTimeInfo);
							}
						} else if (gvgActivityStatus == GVGActivityStatus.READY) {
							ErrorLogUtil.errorLog("战斗服开了,玩家登入GVGActivityStatus不对", "gvgActivityStatus",gvgActivityStatus);
							throw new AlertException("战斗服开了,玩家登入GVGActivityStatus不对");
						}
					} else {
						ErrorLogUtil.errorLog("战斗服开了,玩家登入却没有活动GVGActivityContext信息");
					}
				}else if(Application.getServerType() == ServerType.TVT_BATTLE){
					TvtActivityContext tvtActivityContext = activityVo.getActivityContext();
					if (tvtActivityContext != null) {
						TvtActivityStatus tvtActivityStatus = tvtActivityContext.getTvtActivityStatus();
						if (tvtActivityStatus == TvtActivityStatus.ADMITTANCE) {
							GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
							if (gvgBattleFieldTimeLine == null) {
								throw new AlertException("战场服缺少gvgBattleFieldTimeLine");
							} else {
								gvgBattleFieldTimeLine.putGvgBattleServerTimeInfo(gcGvgBattleServerTimeInfo);
								role.send(gcGvgBattleServerTimeInfo);
							}
						} else if (tvtActivityStatus == TvtActivityStatus.READY) {
							ErrorLogUtil.errorLog("战斗服开了,玩家登入GVGActivityStatus不对", "tvtActivityStatus",tvtActivityStatus);
							throw new AlertException("战斗服开了,玩家登入GVGActivityStatus不对");
						}
					} else {
						ErrorLogUtil.errorLog("战斗服开了,玩家登入却没有活动GVGActivityContext信息");
					}
				}

			} else {
				ErrorLogUtil.errorLog("战斗服开了,玩家登入却没有活动信息");
			}
		}
	}

}
