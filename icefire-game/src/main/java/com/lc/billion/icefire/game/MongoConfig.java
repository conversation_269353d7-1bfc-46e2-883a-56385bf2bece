package com.lc.billion.icefire.game;

/**
 * <AUTHOR>
 * @create 2021/6/1 11:51 上午
 */
public class MongoConfig {

	//最少保持的连接数
	private int selfMinConnectionsPerHost;
	//最大的连接数
	private int selfConnectionsPerHost;
	//空闲连接过期时间~现在默认都不过期
	private int selfMaxConnectionIdleTime;
	//连接最长存活的时间
	private int selfMaxConnectionLifeTime;
	//最大等待连接的时间
	private int selfMaxWaitTime;
	//连接超时时间
	private int selfConnectTimeout;
	//每个连接的队列长度
	private int selfThreadsAllowedToBlockForConnectionMultiplier;

	private int otherMinConnectionsPerHost;
	private int otherConnectionsPerHost;
	private int otherMaxConnectionIdleTime;
	private int otherMaxConnectionLifeTime;
	private int otherMaxWaitTime;
	private int otherConnectTimeout;
	private int otherThreadsAllowedToBlockForConnectionMultiplier;

	public MongoConfig() {}

	public int getSelfMinConnectionsPerHost() {
		return selfMinConnectionsPerHost;
	}

	public void setSelfMinConnectionsPerHost(int selfMinConnectionsPerHost) {
		this.selfMinConnectionsPerHost = selfMinConnectionsPerHost;
	}

	public int getSelfConnectionsPerHost() {
		return selfConnectionsPerHost;
	}

	public void setSelfConnectionsPerHost(int selfConnectionsPerHost) {
		this.selfConnectionsPerHost = selfConnectionsPerHost;
	}

	public int getSelfMaxConnectionIdleTime() {
		return selfMaxConnectionIdleTime;
	}

	public void setSelfMaxConnectionIdleTime(int selfMaxConnectionIdleTime) {
		this.selfMaxConnectionIdleTime = selfMaxConnectionIdleTime;
	}

	public int getSelfMaxConnectionLifeTime() {
		return selfMaxConnectionLifeTime;
	}

	public void setSelfMaxConnectionLifeTime(int selfMaxConnectionLifeTime) {
		this.selfMaxConnectionLifeTime = selfMaxConnectionLifeTime;
	}

	public int getSelfMaxWaitTime() {
		return selfMaxWaitTime;
	}

	public void setSelfMaxWaitTime(int selfMaxWaitTime) {
		this.selfMaxWaitTime = selfMaxWaitTime;
	}

	public int getSelfConnectTimeout() {
		return selfConnectTimeout;
	}

	public void setSelfConnectTimeout(int selfConnectTimeout) {
		this.selfConnectTimeout = selfConnectTimeout;
	}

	public int getSelfThreadsAllowedToBlockForConnectionMultiplier() {
		return selfThreadsAllowedToBlockForConnectionMultiplier;
	}

	public void setSelfThreadsAllowedToBlockForConnectionMultiplier(int selfThreadsAllowedToBlockForConnectionMultiplier) {
		this.selfThreadsAllowedToBlockForConnectionMultiplier = selfThreadsAllowedToBlockForConnectionMultiplier;
	}

	public int getOtherMinConnectionsPerHost() {
		return otherMinConnectionsPerHost;
	}

	public void setOtherMinConnectionsPerHost(int otherMinConnectionsPerHost) {
		this.otherMinConnectionsPerHost = otherMinConnectionsPerHost;
	}

	public int getOtherConnectionsPerHost() {
		return otherConnectionsPerHost;
	}

	public void setOtherConnectionsPerHost(int otherConnectionsPerHost) {
		this.otherConnectionsPerHost = otherConnectionsPerHost;
	}

	public int getOtherMaxConnectionIdleTime() {
		return otherMaxConnectionIdleTime;
	}

	public void setOtherMaxConnectionIdleTime(int otherMaxConnectionIdleTime) {
		this.otherMaxConnectionIdleTime = otherMaxConnectionIdleTime;
	}

	public int getOtherMaxConnectionLifeTime() {
		return otherMaxConnectionLifeTime;
	}

	public void setOtherMaxConnectionLifeTime(int otherMaxConnectionLifeTime) {
		this.otherMaxConnectionLifeTime = otherMaxConnectionLifeTime;
	}

	public int getOtherMaxWaitTime() {
		return otherMaxWaitTime;
	}

	public void setOtherMaxWaitTime(int otherMaxWaitTime) {
		this.otherMaxWaitTime = otherMaxWaitTime;
	}

	public int getOtherConnectTimeout() {
		return otherConnectTimeout;
	}

	public void setOtherConnectTimeout(int otherConnectTimeout) {
		this.otherConnectTimeout = otherConnectTimeout;
	}

	public int getOtherThreadsAllowedToBlockForConnectionMultiplier() {
		return otherThreadsAllowedToBlockForConnectionMultiplier;
	}

	public void setOtherThreadsAllowedToBlockForConnectionMultiplier(int otherThreadsAllowedToBlockForConnectionMultiplier) {
		this.otherThreadsAllowedToBlockForConnectionMultiplier = otherThreadsAllowedToBlockForConnectionMultiplier;
	}

	@Override public String toString() {
		return "MongoConfig{" + "selfMinConnectionsPerHost=" + selfMinConnectionsPerHost + ", selfConnectionsPerHost=" + selfConnectionsPerHost + ", selfMaxConnectionIdleTime="
				+ selfMaxConnectionIdleTime + ", selfMaxConnectionLifeTime=" + selfMaxConnectionLifeTime + ", selfMaxWaitTime=" + selfMaxWaitTime + ", selfConnectTimeout="
				+ selfConnectTimeout + ", selfThreadsAllowedToBlockForConnectionMultiplier=" + selfThreadsAllowedToBlockForConnectionMultiplier + ", otherMinConnectionsPerHost="
				+ otherMinConnectionsPerHost + ", otherConnectionsPerHost=" + otherConnectionsPerHost + ", otherMaxConnectionIdleTime=" + otherMaxConnectionIdleTime
				+ ", otherMaxConnectionLifeTime=" + otherMaxConnectionLifeTime + ", otherMaxWaitTime=" + otherMaxWaitTime + ", otherConnectTimeout=" + otherConnectTimeout
				+ ", otherThreadsAllowedToBlockForConnectionMultiplier=" + otherThreadsAllowedToBlockForConnectionMultiplier + '}';
	}
}
