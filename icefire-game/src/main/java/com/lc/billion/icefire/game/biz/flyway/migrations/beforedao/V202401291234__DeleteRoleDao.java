package com.lc.billion.icefire.game.biz.flyway.migrations.beforedao;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleVipDao;
import com.lc.billion.icefire.game.biz.flyway.AbstractMongoMigrationForBeforeDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class V202401291234__DeleteRoleDao extends AbstractMongoMigrationForBeforeDao {
    private final Logger logger = LoggerFactory.getLogger(V202401291234__DeleteRoleDao.class);

    @Override
    protected void rollback() throws Exception {

    }

    @Override
    protected void updateForBeforeDao() throws Exception {
        logger.info("清理玩家道具数据>>>>>>>>>>>>开始>");
        Application.getBean(RoleDao.class).clearAll();
        Application.getBean(RoleVipDao.class).clearAll();
        logger.info("清理玩家道具数据>>>>>>>>>>>>结束>");
    }
}
