package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoTimeType;
import com.lc.billion.icefire.game.biz.model.role.Role;

/**
 * <AUTHOR>
 * @since 2016年7月15日下午6:45:14
 *
 */
@Service
public class ConditionTimeCheckFunction implements LiBaoTimeCheck {

	@Override
	public LiBaoTimeType getType() {
		return LiBaoTimeType.CONDITIONAL;
	}

	@Override
	public boolean check(Role role, LibaoMeta meta) {
		return true;
	}

}
