package com.lc.billion.icefire.game.msg.handler.impl.milestone;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.milestone.MilestoneService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgGetMilestoneAlliance;

@Controller
public class CgGetMilestoneAllianceHandler extends CgAbstractMessageHandler<CgGetMilestoneAlliance> {

	 @Autowired
	 private MilestoneService service;

	@Override
	public void handle(Role role, CgGetMilestoneAlliance message) {
		service.getMilestoneAlliance(role,message.getMetaId());
	}

}
