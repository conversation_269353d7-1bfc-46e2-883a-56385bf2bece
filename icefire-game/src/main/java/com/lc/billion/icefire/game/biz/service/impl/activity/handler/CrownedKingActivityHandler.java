package com.lc.billion.icefire.game.biz.service.impl.activity.handler;

import com.lc.billion.icefire.game.biz.config.ActivityListConfig.ActivityListMeta;
import com.lc.billion.icefire.game.biz.config.CrazyMissionConfig;
import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.config.ActivityCrazyMissionRewardConfig;
import com.lc.billion.icefire.game.biz.manager.MissionManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.mission.newplayer.NewPlayerMission;
import com.lc.billion.icefire.game.biz.model.mission.newplayer.NewPlayerMissionItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.protocol.constant.PsActivityStatus;
import com.lc.billion.icefire.protocol.constant.PsActivityType;
import com.lc.billion.icefire.protocol.constant.PsRewardClaimStatus;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CrownedKingActivityHandler extends AbstractActivityHandler<Activity> {
    @Autowired
    private MissionManager missionManager;

    @Override
    public ActivityType getType() {
        return ActivityType.CROWNED_KING;
    }

    @Override
    public PsActivityInfo getRoleActivityInfo(Role role, ActivityListMeta meta) {
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null) {
            return null;
        }

        PsActivityStatus status = PsActivityStatus.STOP;
        if (newPlayerMission.isOpen() && !newPlayerMission.isOverlap()) {
            status = PsActivityStatus.START;
        } else {
            status = PsActivityStatus.STOP;
        }

        Activity activity = activityDao.findActivityByActivityType(getType());
        PsActivityInfo info = new PsActivityInfo();
        info.setId(String.valueOf(activity.getPersistKey()));
        info.setStartTime(newPlayerMission.getMissionOpenTime());
        info.setEndTime(newPlayerMission.getEndTime());
        info.setMetaId(meta.getId());
        info.setType(PsActivityType.CROWNED_KING);
        info.setStatus(status);
        info.setHasReward(calculateActivityRewardShowForLogin(role, activity) > 0);
        return info;
    }

    @Override
    public int calculateActivityRewardShowForLogin(Role role, Activity activity) {
        int count = 0;
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission.getAll() != null) {
            int day = newPlayerMission.getUnlockDay();
            for (NewPlayerMissionItem m : newPlayerMission.getAll()) {
                if (!m.isAward() && m.getStatus() == 1) {
                    MissionConfig.MissionMeta missionMeta = configService.getConfig(CrazyMissionConfig.class).getById(m.getMissionId());
                    if (missionMeta != null && Integer.parseInt(missionMeta.getUnlock()) <= day) {
                        count++;
                        break;
                    }
                }
            }
            var config = configService.getConfig(ActivityCrazyMissionRewardConfig.class);
            for (var mate : config.getMetaMap().values()) {
                if (newPlayerMission.getPoint() < mate.getActivity()) {
                    continue;
                }

                int index = Integer.parseInt(mate.getId()) - 1;
                if (newPlayerMission.getCommonRewards().get(index) == PsRewardClaimStatus.CAN_BE_CLAIMED) {
                    count++;
                }

                if (newPlayerMission.getVipRewards().get(index) == PsRewardClaimStatus.CAN_BE_CLAIMED) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public boolean createValidated(ActivityListMeta activityMeta,long activityEndTime) {
        return true;
    }
}
