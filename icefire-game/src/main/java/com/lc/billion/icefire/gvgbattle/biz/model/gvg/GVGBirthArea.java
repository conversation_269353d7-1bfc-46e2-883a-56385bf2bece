package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.lc.billion.icefire.core.support.Point;

/**
 * <AUTHOR>
 *
 */
public class GVGBirthArea {

	/**
	 * 中心坐标
	 */
	private Point centerPosition;
	private int xLength;
	private int yLength;
	/**
	 * 分到该区域的联盟
	 */
	private long allianceId;
	/**
	 * 红 1 蓝 2
	 */
	private int color;
	private String daYingMetaId;

	public GVGBirthArea(Point centerPosition, int xLength, int yLength, long allianceId, int color, String daYingMetaId) {
		super();
		this.centerPosition = centerPosition;
		this.xLength = xLength;
		this.yLength = yLength;
		this.allianceId = allianceId;
		this.color = color;
		this.daYingMetaId = daYingMetaId;
	}

	public int getMinX() {
		return centerPosition.getX() - xLength;
	}

	public int getMaxX() {
		return centerPosition.getX() + xLength;
	}

	public int getMinY() {
		return centerPosition.getY() - yLength;
	}

	public int getMaxY() {
		return centerPosition.getY() + yLength;
	}

	public long getAllianceId() {
		return allianceId;
	}

	public int getColor() {
		return color;
	}

	public String getDaYingMetaId(){return daYingMetaId;}
}
