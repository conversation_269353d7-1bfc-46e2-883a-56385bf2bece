package com.lc.billion.icefire.gvgbattle.biz.service.rpc;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IMigrateForGVGService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.KvkSeasonServerGroupConfig;

/**
 * <AUTHOR>
 *
 */
@Service
public class GVGBattleRPCToGameProxyService extends AbstractRPCProxyService {

	private Map<Integer, RpcProxyBean<IMigrateForGVGService>> migrateRemoteGVGBattleServices = new HashMap<>();

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.GVG_BATTLE, ServerType.TVT_BATTLE };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GAME, ServerType.KVK_SEASON };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<IMigrateForGVGService> rpcProxyBuilder = RpcProxyBuilder.create(IMigrateForGVGService.class).connect(getSerializer(), gameServerConfig.getRpcIp(),
				gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		IMigrateForGVGService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
		RpcProxyBean<IMigrateForGVGService> rpcProxyBean = new RpcProxyBean<IMigrateForGVGService>(service, rpcClient);
		int gameServerId = gameServerConfig.getGameServerId();
		ServerType serverType = gameServerConfig.serverType();
		migrateRemoteGVGBattleServices.put(gameServerId, rpcProxyBean);
		logger.info("rpc GVG_BATTLE to GAME {}->{},{}:{}", Application.getServerId(), gameServerId, gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		if (serverType == ServerType.KVK_SEASON) {
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(gameServerId);
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",gameServerId);
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				for (Integer serverId : oServerIds) {
					migrateRemoteGVGBattleServices.put(serverId, rpcProxyBean);
					logger.info("rpc GVG_BATTLE to OGAME {}->{},{}:{}", Application.getServerId(), serverId, gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
				}
			}
		} else if (serverType == ServerType.GAME) {
			// 已经在前面初始化过了
		} else {
			throw new AlertException("GVG_BATTLE to的RPC建立在这里是不被允许的","serverType",serverType);
		}

		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		RpcProxyBean<IMigrateForGVGService> rpcProxyBean = migrateRemoteGVGBattleServices.get(gameServerConfig.getGameServerId());
		if (rpcProxyBean != null) {
			RpcClient rpcClient = rpcProxyBean.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected boolean containsRPCClient(int serverId) {
		return migrateRemoteGVGBattleServices.containsKey(serverId);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		ServerType serverType = configCenter.getServerType(serverId);
		if (serverType == ServerType.GAME) {
			RpcProxyBean<IMigrateForGVGService> rpcProxyBean = migrateRemoteGVGBattleServices.remove(serverId);
			if (rpcProxyBean != null) {
				RpcClient rpcClient = rpcProxyBean.getRpcClient();
				rpcClient.setStop(true);
				logger.info("rpc GVG_BATTLE to GAME 移除{} 不空", serverId);
			} else {
				logger.info("rpc GVG_BATTLE to GAME 移除{} 空", serverId);
			}
		} else if (serverType == ServerType.KVK_SEASON) {
			RpcProxyBean<IMigrateForGVGService> remove = migrateRemoteGVGBattleServices.remove(serverId);
			if (remove != null) {
				RpcClient rpcClient = remove.getRpcClient();
				rpcClient.setStop(true);
				logger.info("rpc GVG_BATTLE to KVK_SEASON 移除{} 不空", serverId);
			} else {
				logger.info("rpc GVG_BATTLE to KVK_SEASON 移除{} 空", serverId);
			}
			KvkSeasonServerGroupConfig kvkSeasonServerGroupConfig = configCenter.getLsConfig().getKvkSeasons().getServerGroupByKServerId(serverId);
			if (kvkSeasonServerGroupConfig == null) {
				ErrorLogUtil.errorLog("赛季服的KvkSeasonServerGroupConfig找不到", "serverId",serverId);
			} else {
				Set<Integer> oServerIds = kvkSeasonServerGroupConfig.getOServerIds();
				for (Integer oServerId : oServerIds) {
					RpcProxyBean<IMigrateForGVGService> rpcProxyBean = migrateRemoteGVGBattleServices.remove(oServerId);
					if (rpcProxyBean != null) {
						RpcClient rpcClient = rpcProxyBean.getRpcClient();
						rpcClient.setStop(true);
						logger.info("rpc GVG_BATTLE to GAME 移除{} 不空", oServerId);
					} else {
						logger.info("rpc GVG_BATTLE to GAME 移除{} 空", oServerId);
					}
				}
			}
		} else {
			throw new AlertException("rpc GVG_BATTLE to的RPC移除在这里是不被允许的","serverType",serverType);
		}
	}

	@Override
	protected boolean checkInstance() {
		return true;
	}

	@Override
	protected boolean createWait() {
		return false;
	}

	public IMigrateForGVGService getMigrateRemoteService(int serverId) {
		RpcProxyBean<IMigrateForGVGService> rpcProxyBean = migrateRemoteGVGBattleServices.get(serverId);
		if (rpcProxyBean != null) {
			return rpcProxyBean.getProxy();
		}
		return null;
	}
}
