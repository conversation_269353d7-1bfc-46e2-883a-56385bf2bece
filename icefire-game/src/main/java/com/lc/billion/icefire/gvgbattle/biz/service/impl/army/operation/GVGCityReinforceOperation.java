package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.reinforce.AllianceReinforceService;
import com.lc.billion.icefire.game.biz.service.impl.alliance.war.AllianceWarService;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.push.PushHelper;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.protocol.constant.PsAllianceWarInfoUpdateReason;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/2/26
 */
@Service
public class GVGCityReinforceOperation implements ArmyOperation {

    @Autowired
    protected ArmyManager armyManager;
    @Autowired
    protected ArmyServiceImpl armyService;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    protected AllianceWarService warService;
    @Autowired
    protected AllianceReinforceService reinforceService;

    @Override
    public ArmyType getArmyType() {
        return ArmyType.GVG_REINFORCE;
    }

    @Override
    public void finishWork(ArmyInfo army) {

    }

    @Override
    public void returnArrived(ArmyInfo army) {
        armyManager.takeBackArmy(army);
    }

    @Override
    public void armyArrive(ArmyInfo army) {
        SceneNode target = sceneService.getSceneNode(army.getCurrentServerId(), army.getMapRoute().getEnd());
        if (target == null) { // 目标不存在，军队返回
            armyManager.returnArmy(army);
            return;
        }
        if (!(target instanceof RoleCity)) {
            armyManager.returnArmy(army);
            return;
        }

        long leftCount = reinforceService.getLeftReinforceCapacity(target);
        if (leftCount <= 0) {
            armyManager.returnArmy(army);
            return;
        }
        if (army.getSoldierCount() > leftCount) {
            armyManager.divideArmy(army, (int) (army.getSoldierCount() - leftCount), String.valueOf(target.getPersistKey()));
        }

        army.setWorkType(ArmyWorkType.REINFORCING);
        armyManager.saveArmy(army);
        sceneService.remove(army);
        // 更新信息给前端
        armyService.updateArmyProgress(army);
        armyManager.removeEnemy(army);
        reinforceService.pushAllianceReinforceChange(target.getPersistKey());

        updateWar(army);
    }


    private void updateWar(ArmyInfo army) {
        var targetNode = sceneService.getSceneNode(Application.getServerId(), army.getEndPoint());
        if(targetNode != null) {
            warService.broadcastUpdate(targetNode, PsAllianceWarInfoUpdateReason.REINFORCE_CHANGE);
        }
    }
}