package com.lc.billion.icefire.gvgbattle.biz.model.scene.node;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightProp;
import com.lc.billion.icefire.game.biz.battle.calculator.FightUnitPropCalculate;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconRoleInfo;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.ISceneWithWatcherNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.armyTarget.IGvgRallyTargetNode;
import com.lc.billion.icefire.game.biz.model.scene.node.armyTarget.IReconTargetNode;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.scene.MapNodeUpdater;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.SimpleReportData;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGStrongHoldService;
import com.lc.billion.icefire.protocol.GcMapNodeUpdate;
import com.lc.billion.icefire.protocol.GcStrongHoldPanelInfo;
import com.lc.billion.icefire.protocol.structure.PsAllianceFlagInfo;
import com.lc.billion.icefire.protocol.structure.PsMapNode;
import com.lc.billion.icefire.protocol.structure.PsMapStrongHoldInfo;
import com.lc.billion.icefire.protocol.structure.PsMapWuChaoInfo;
import lombok.Getter;
import lombok.Setter;
import org.apache.thrift.TBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


// GVG建筑，todo gvg 删除接口：IGvgRallyTargetNode
public class StrongHoldNode extends GVGSceneNode implements IGvgRallyTargetNode, IReconTargetNode, ISceneWithWatcherNode {

    private static final Logger logger = LoggerFactory.getLogger(StrongHoldNode.class);

    public enum SHOccupyState{
        NotOccupied,
        Occupying,
        Occupied;
    }
    private static final long serialVersionUID = -4141603426523375870L;

    private String metaId;

    /**
     * 占领的联盟id
     */
    private long allianceId = 0;

    // 驻防队长Id
    private long leaderId = 0;

    /**
     * 历史首次占领
     */
    private boolean occupied = false;

    /**
     * 当前占领状态
     */
    private boolean occupyState = false;

    /**
     * 完成占领建筑时间戳
     */
    private long occupyTime = 0L;

    /**
     * 部队到达建筑，开始执行占领的时间戳
     */
    private long occupyingBeginTime = 0L;

    /**
     * 旧的归属联盟Id
     */
    private long oldAllianceId = 0;

    /**
     * 参战的人员
     */
    private List<Long> occupierList = new ArrayList<>();

    /**
     * 类型
     */
    @JsonIgnore
    private BuildingType buildingType;

    /**
     * 点数增加次数
     */
    private int addCount = 0;
    /**
     * 霹雳车行为事件
     */
    private long lastActionTime = 0;

    private long unlockTime = 0;

    @Getter
    @Setter
    private boolean autoSetLeader = true;
    // 集结次数
    private Map<Long, Integer> rallyCounts = new HashMap<>();
    // 进攻次数
    private Map<Long, Integer> attackCounts = new HashMap<>();

    // 以下字段为OB新加--并不是在AOI中
    private List<SimpleReportData> reportDataList = new ArrayList<>();

    public String getMetaId() {
        return metaId;
    }

    @Override
    public String getMetaName() {
        var meta = Application.getBean(ConfigServiceImpl.class).getConfig(GvgBuildingConfig.class).get(getMetaId());
        if (meta == null) {
            return "";
        }
        return meta.getBuildName();
    }
    public void setMetaId(String metaId) {
        this.metaId = metaId;
    }
    public long getAllianceId() {
        return allianceId;
    }

    public void setAllianceId(Long allianceId) {
        this.allianceId = allianceId;
    }

    public boolean isOccupiedByAlliance(Long allianceId) {
        if(occupyState) {
            return Objects.equals(this.allianceId, allianceId);
        } else {
            return false;
        }
    }

    public boolean occupied() {
        return occupied;
    }
    public void setOccupied(boolean occupied) {
        this.occupied = occupied;
    }

    public void setOccupyState(boolean state){
        occupyState = state;
    }

    public SHOccupyState getOccupyState(){
        if(hasOwner()) {
            return occupyState ? SHOccupyState.Occupied: SHOccupyState.Occupying;
        } else {
            return SHOccupyState.NotOccupied;
        }
    }

    public BuildingType getBuildingType() {
        return buildingType;
    }
    public void setBuildingType(BuildingType buildingType) {
        this.buildingType = buildingType;
    }
    public int getAddCount() {
        return addCount;
    }
    public void setAddCount(int addCount) {
        this.addCount = addCount;
    }

    public void setUnlockTime(long unlockTime){this.unlockTime = unlockTime;}
    public long getUnlockTime(){return this.unlockTime;}

    public void setOccupyTime(long occupyTime) {
        this.occupyTime = occupyTime;
    }

    public long getOccupyTime() {
        return occupyTime;
    }
    public void setOccupyBeginTime(long occupyingBeginTime) {
        this.occupyingBeginTime = occupyingBeginTime;
    }

    public long getOccupyBeginTime() {
        return occupyingBeginTime;
    }

    public long getOldAllianceId() {
        return oldAllianceId;
    }

    public void setOldAllianceId(long oldAllianceId) {
        this.oldAllianceId = oldAllianceId;
    }

    public List<Long> getGarrisonRoleList() {
        var roleIdList = new ArrayList<Long>();
        var garrisonArmies = Application.getBean(GVGStrongHoldService.class).getNodeGarrisonArmies(this);
        for(var garrisonArmy : garrisonArmies){
            roleIdList.add(garrisonArmy.getRoleId());
        }

        return roleIdList;
    }

    public boolean hasGarrisonRoleList(Long roleId) {
        var garrisonArmies = Application.getBean(GVGStrongHoldService.class).getNodeGarrisonArmies(this);
        return garrisonArmies.stream().anyMatch(a-> Objects.equals(a.getRoleId(), roleId));
    }

    public void setOccupierList(List<Long> occupierList) {
        this.occupierList = occupierList;
    }

    @Override
    public SceneNodeType getNodeType() {
        return SceneNodeType.STRONGHOLD;
    }

    @Override
    public String toString() {
        return super.toString();
    }

    public PsMapStrongHoldInfo toPsMapStrongHoldInfo() {
        PsMapStrongHoldInfo info = new PsMapStrongHoldInfo();
        info.setId(this.getPersistKey());
        info.setMetaId(metaId);
        info.setAllianceId(allianceId);
        info.setOccupyTime(getOccupyTime());
        info.setTotalOccupyTime(getOccupyTime() - getOccupyBeginTime());

        if (hasOwner()) {
            AllianceDao allianceDao = Application.getBean(AllianceDao.class);
            Alliance alliance = allianceDao.findById(allianceId);
            if (alliance != null) {
                info.setAllianceName(alliance.getName() == null ? "" : alliance.getName());
                info.setAllianceAliasName(alliance.getAliasName() == null ? "" : alliance.getAliasName());

                PsAllianceFlagInfo flag = new PsAllianceFlagInfo();
                flag.setBannerColor(alliance.getBannerColor());
                flag.setBanner(alliance.getBanner());
                flag.setBadgeColor(alliance.getBadgeColor());
                flag.setBadge(alliance.getBadge());
                info.setAllianceFlag(flag);
            }
        }

        if(getBuildingType() == BuildingType.WuChao){
            var strongHoldService = Application.getBean(GVGStrongHoldService.class);
            PsMapWuChaoInfo wuChaoInfo = new PsMapWuChaoInfo();
            wuChaoInfo.setTransportBeginTime(strongHoldService.supplyWagonBeginTime());
            wuChaoInfo.setTransportEndTime(strongHoldService.supplyWagonEndTime());
            wuChaoInfo.setScore(strongHoldService.wuChaoNextCompleteScore());
            wuChaoInfo.setNextOpenTime(strongHoldService.wuChaoNextOpenTime());
            info.setWuChaoInfo(wuChaoInfo);
        }


        if(getBuildingType() == BuildingType.PiLiChe) {
            var strongHoldService = Application.getBean(GVGStrongHoldService.class);
            long piliCheNextAttackTime = strongHoldService.getPiliCheNextAttackTime(this);
            info.setPiLiCheNextAttackTime(piliCheNextAttackTime);
        }

        info.setFirstOccupy(!this.occupied);
        return info;
    }


    @Override
    public GcMapNodeUpdate toMapNodeUpdate(MapNodeUpdater updater) {
        return null;
    }


    @Override
    public String getBIConfigId() {
        return metaId;
    }

    @Override
    public int getGVGJoinArmyStaminaCost() {
        GvgSettingConfig gvgSettingConfig = Application.getBean(ConfigServiceImpl.class).getConfig(GvgSettingConfig.class);
        return gvgSettingConfig.getGvgJoinBuildingPVEDurability();
    }

    @Override
    public int getGVGRallyArmyStaminaCost() {
        GvgSettingConfig gvgSettingConfig = Application.getBean(ConfigServiceImpl.class).getConfig(GvgSettingConfig.class);
        return gvgSettingConfig.getGvgRallyBuildingPVEDurability();
    }

    @Override
    public boolean canRecon(Role role) {
        var strongHoldService = Application.getBean(GVGStrongHoldService.class);
        if(!strongHoldService.canInteract(this)){
            return false;
        }
        Alliance allianceByRole = Application.getBean(AllianceServiceImpl.class).getAllianceByRole(role);
        if (allianceByRole == null || allianceByRole.getPersistKey().equals(getAllianceId()))
            return false;
        return true;
    }

    @Override
    public void reconBaseInfo(ReconInfo reconInfo) {
        ReconRoleInfo roleInfo = new ReconRoleInfo();
        roleInfo.setX(getX());
        roleInfo.setY(getY());
        roleInfo.setTargetType(SceneNodeType.STRONGHOLD);
        roleInfo.setMetaId(getMetaId());
        reconInfo.setReconRoleInfo(roleInfo);

        reconToRole(reconInfo);
    }

    private void reconToRole(ReconInfo reconInfo) {
        ReconRoleInfo roleInfo = reconInfo.getReconRoleInfo();
        var armyManager = Application.getBean(ArmyManager.class);
        Alliance alliance = Application.getBean(AllianceServiceImpl.class).getAllianceById(getAllianceId());
        if (alliance != null) {
            roleInfo.setAllianceShortName(alliance.getAliasName());
            roleInfo.setAllianceName(alliance.getName());
        }

        var leaderId = leaderArmyId();
        var leaderArmy = armyManager.findById(leaderId);
        if(leaderArmy == null) {
            ErrorLogUtil.errorLog("[GVG]reconToRole leaderArmy is null", "leaderId",leaderId);
            return;
        }

        Role role = Application.getBean(RoleManager.class).getRole(leaderArmy.getRoleId());
        roleInfo.setRoleId(role.getPersistKey());
        roleInfo.setPlayerName(role.getName());
        roleInfo.setHeadIcon(role.getHead());
        roleInfo.setRoleInfo(role.toRoleInfo());

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : leaderArmy.getArmySoldiers().entrySet()) {
            reconArmyInfo.getSoldierMap().put(entry.getKey(), entry.getValue().getCount());
        }
        List<Hero> heroList = Lists.newArrayList();
        HeroServiceImpl heroService = Application.getBean(HeroServiceImpl.class);
        for (var entry : leaderArmy.getHeros()) {
            var hero = heroService.getHero(role, entry);
            heroList.add(hero);
            reconArmyInfo.getHeroInfoList().add(HeroOutput.toInfo(hero));
        }
        FightProp fightProp = new FightProp();
        fightProp.setRoleProps(role.getNumberProps());
        fightProp.setHeroBuffValues(FightArmy.heroProps(heroList, fightProp));
        reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, getNodeType()));
        reconInfo.setReconArmyInfo(reconArmyInfo);
    }

    /**
     * 已经到达的部队
     */
    public List<ArmyInfo> getArriveArmys() {
        var armys = Application.getBean(GVGStrongHoldService.class).getNodeFriendArmies(this);
        List<ArmyInfo> ret = new ArrayList<>();
        for (var army : armys) {
            if (army.getWorkType() == ArmyWorkType.DEFENDING) {
                ret.add(army);
            }
        }

        return ret;
    }

    @Override
    public Role reconOwnerRole() {
        return null;
    }

    @Override
    public List<ArmyInfo> reconReinforceInfo() {
        var gvgStrongHoldService = Application.getBean(GVGStrongHoldService.class);
        var armyList = gvgStrongHoldService.getNodeGarrisonArmies(this);
        List<ArmyInfo> reinforceList = new ArrayList<>();
        var leaderArmyId = leaderArmyId();
        for (var army : armyList) {
            // 去除领队的信息
            if (leaderArmyId == army.getPersistKey()) {
                continue;
            }

            reinforceList.add(army);
        }
        return reinforceList;
    }

    @Override
    public TBase<?, ?> buildMsg() {
        GcStrongHoldPanelInfo info = new GcStrongHoldPanelInfo();
        info.setId(getPersistKey().toString());
        return info;
    }

    public List<SimpleReportData> getReportDataList() {
        return reportDataList;
    }

    public Map<Long, Integer> getAttackCounts() {
        return attackCounts;
    }

    public Map<Long, Integer> getRallyCounts() {
        return rallyCounts;
    }

    @Override
    public PsMapNode._Fields getField() {
        return PsMapNode._Fields.STRONG_HOLD_INFO;
    }

    @Override
    public TBase<?, ?> getFieldValue() {
        return this.toPsMapStrongHoldInfo();
    }

    @Override
    public FightArmy createFightArmy(){
        var gvgStrongHoldService = Application.getBean(GVGStrongHoldService.class);
        var garrisonArmies = gvgStrongHoldService.getNodeGarrisonArmies(this);
        var armyManager = Application.getBean(ArmyManager.class);

        var leaderId = leaderArmyId();
        if(garrisonArmies.isEmpty()){
            return null;
        }

        // 这一步是为了保证队长在第一个位置，构建FightArmy时，第一个位置的队伍是队长
        for(int i = 0; i < garrisonArmies.size(); ++i){
            var garrisonArmy = garrisonArmies.get(i);
            if(garrisonArmy.getPersistKey().equals(leaderId)) {
                if(i != 0){
                    garrisonArmies.remove(i);
                    garrisonArmies.addFirst(garrisonArmy);
                } // 队长已经在第一个位置，直接break
                break;
            }
        }

        // 队长属性作为本次战斗防御方属性
        var garrisonLeader = armyManager.findById(leaderId);
        var leaderRole = garrisonLeader.getOwner();
        FightArmy armyInfo = new FightArmy(garrisonArmies, SceneNodeType.STRONGHOLD, leaderRole);

        return armyInfo;
    }

    // 这个方法每次会话调用一次，不要频繁调用
    public long leaderArmyId() {
        var armyManager = Application.getBean(ArmyManager.class);
        var army = armyManager.findById(leaderId);
        if(army == null || army.getWorkType() != ArmyWorkType.DEFENDING) {
            logger.info("[GVG]leaderArmyId needNewLeader, leaderId: {}, workType: {}",
                    leaderId, army == null ? "null" : army.getWorkType());
            autoChooseLeader();
        }

        return leaderId;
    }

    public void autoChooseLeader() {
        var gvgStrongHoldService = Application.getBean(GVGStrongHoldService.class);

        var garrisonArmies = gvgStrongHoldService.getNodeGarrisonArmies(this);
        if(garrisonArmies.isEmpty()) {
            logger.info("[GVG]autoChooseLeader {} noGarrison, oldLeaderId: {}", getPersistKey(), leaderId);
            leaderId = 0;
            return;
        }

        // 拿到最高战力的部队
        int maxPower = -1;  // 没有英雄的队伍是0，所以这里不能用0
        ArmyInfo maxPowerArmy = null;
        for(var garrisonArmy: garrisonArmies) {
            var armyHeroPower = armyHeroPower(garrisonArmy);
            if(armyHeroPower > maxPower) {
                maxPower = armyHeroPower;
                maxPowerArmy = garrisonArmy;
            }
        }

        if(maxPowerArmy != null) {
            logger.info("[GVG]autoChooseLeader {}, oldLeaderId: {}, newLeaderId: {}, power: {}", getPersistKey(),
                    leaderId, maxPowerArmy.getPersistKey(), maxPower);
            leaderId = maxPowerArmy.getPersistKey();
        } else {
            ErrorLogUtil.errorLog("[GVG]autoChooseLeader,can't find leader","oldLeaderId",leaderId, "power",maxPower);
            leaderId = 0;
        }
    }

    private int armyHeroPower(ArmyInfo armyInfo) {
        var heroService = Application.getBean(HeroServiceImpl.class);

        int power = 0;
        var heroMetas = armyInfo.getHeros();
        for(var heroMeta : heroMetas) {
            var hero = heroService.getHero(armyInfo.getRoleId(), heroMeta);
            if(hero != null) {
                power += hero.getPower();
            }
        }

        return power;
    }

    public boolean setLeader(ArmyInfo army) {
        if(army == null || army.getWorkType() != ArmyWorkType.DEFENDING || !army.getTargetNode().equals(this)) {
            return false;
        }

        leaderId = army.getPersistKey();
        return true;
    }

    public boolean hasOwner(){
        return allianceId != 0;
    }


    public long getLastActionTime() {
        return lastActionTime;
    }

    public void setLastActionTime(long lastActionTime) {
        this.lastActionTime = lastActionTime;
    }

    @Override
    public int getSize() {
        var strongHoldService = Application.getBean(GVGStrongHoldService.class);
        return strongHoldService.getStrongHoldSize(metaId);
    }

    public List<Object> hotFixBackFunc(List<Object> paramList) {
        return null;
    }

}
