package com.lc.billion.icefire.game.biz.async.gm;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.async.BindThreadOperation;
import com.lc.billion.icefire.game.biz.manager.RoleHeadManager;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState.Type;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.head.HeadOutput;
import com.lc.billion.icefire.game.biz.service.impl.head.HeadServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.protocol.GcPlayerNameExist;

/**
 * gm封禁玩家名字
 */
public class GmBanHeadOperation implements AsyncOperation {

	private Role role;

	private long time;

	public GmBanHeadOperation(Role role, long time) {
		this.role = role;
		this.time = time;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		return true;
	}

	@Override
	public void finish() {
		HeadServiceImpl headService = Application.getBean(HeadServiceImpl.class);
		RoleHeadManager headManager = Application.getBean(RoleHeadManager.class);

		if(role == null) {
			ErrorLogUtil.errorLog("GmBanHeadOperation error~role is null");
			return;
		}

		try {
			headService.resetToSystemHead(role);
			headManager.lock(role.getId(), time);
			role.send(HeadOutput.wrapperErrorUploadToken(HeadServiceImpl.BAN_TIME_ERROR, time));
		} catch (ExpectedException ignored) {

		} catch (Exception e) {
    		ErrorLogUtil.exceptionLog("GmBanHeadOperation error",e,"role",role.getId(), "time",time);
		}
	}

}
