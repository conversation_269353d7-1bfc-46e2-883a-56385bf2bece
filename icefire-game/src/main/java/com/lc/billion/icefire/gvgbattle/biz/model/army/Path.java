package com.lc.billion.icefire.gvgbattle.biz.model.army;

import com.lc.billion.icefire.core.graph.Position;
import com.lc.billion.icefire.core.support.Point;

public class Path {
    Position[] nodeArr_;
    float[] nodeDisArr_;
    Position[] segmentDirArr_;

    public Path(Position[] posList) {
        if (posList.length <= 1) {
            return;
        }
        nodeArr_ = posList;
        nodeDisArr_ = new float[posList.length];
        segmentDirArr_ = new Position[posList.length - 1];
        nodeDisArr_[0] = 0;
        for (int i = 1; i < posList.length; ++i) {
            nodeDisArr_[i] = distance(posList[i], posList[i - 1]) + nodeDisArr_[i - 1];
            segmentDirArr_[i - 1] = dir(posList[i - 1], posList[i]);
        }
    }

    public float totalLen() {
        if (nodeDisArr_.length <= 1) {
            return 0;
        } else {
            return nodeDisArr_[nodeDisArr_.length - 1];
        }
    }

    public Position[] nodes(){
        return nodeArr_;
    }

    public boolean invalid() {
        return nodeArr_ == null || nodeArr_.length <= 1;
    }

    public static float distance(Position pos1, Position pos2){
        float dx = pos2.getX() - pos1.getX();
        float dy = pos2.getY() - pos1.getY();
        return (float)Math.sqrt(dx * dx + dy * dy);
    }

    public static Position dir(Position pos1, Position pos2){
        float dx = pos2.getX() - pos1.getX();
        float dy = pos2.getY() - pos1.getY();
        float dis = (float)Math.sqrt(dx * dx + dy * dy);
        return new Position(dx / dis, dy / dis);
    }

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder();

        sb.append("Node:");
        for(Position pos: nodeArr_){
            sb.append('(');
            sb.append(pos.getX());
            sb.append(',');
            sb.append(pos.getY());
            sb.append(')');
        }

        sb.append("Dis:");
        for (float v : nodeDisArr_) {
            sb.append(v);
            sb.append(',');
        }

        sb.append("Dir:");
        for(var dir: segmentDirArr_){
            sb.append('(');
            sb.append(dir.getX());
            sb.append(',');
            sb.append(dir.getY());
            sb.append(')');
        }

        return sb.toString();
    }
}
