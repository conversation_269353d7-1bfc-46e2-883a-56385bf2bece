package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.lc.billion.icefire.protocol.GcGvgBattleServerTimeInfo;
import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.protocol.constant.PsGVGBattleServerStatus;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class GVGBattleFieldTimeLine extends AbstractEntity {

	private static final long serialVersionUID = -6670464059984828084L;

	@MongoId
	private Long id;

	// 战场开启时间
	private Long startTime;
	private boolean startFlag;
	// 战场准备结束时间
	private Long readyEndTime;
	private boolean readyEndFlag;
	// 战场结束时间
	private Long endTime;
	private boolean endFlag;

	// TVT战场为了关联队伍和临时联盟使用
	private Long allianceId1;

	// TVT战场为了关联队伍和临时联盟使用
	private Long allianceId2;

	private List<Integer> resourceRefreshFlag = new ArrayList<>();

	private List<String> noticedOpenFlag = new ArrayList<>();

	@Override
	public void setPersistKey(Long id) {
		this.id = id;
	}

	@Override
	public Long getPersistKey() {
		return id;
	}

	@Override
	public Long getGroupingId() {
		return id;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public PsGVGBattleServerStatus getCurrentPsGVGBattleServerStatus() {
		long now = TimeUtil.getNow();
		if (now < getStartTime()) {
			// 还没开始
			return PsGVGBattleServerStatus.READY;
		} else if (now < getReadyEndTime()) {
			return PsGVGBattleServerStatus.READY;
		} else if (now < getEndTime()) {
			return PsGVGBattleServerStatus.BATTLE;
		} else {
			return PsGVGBattleServerStatus.STOP;
		}
	}

	public long getNextStatusTime() {
		long now = TimeUtil.getNow();
		if (now < getStartTime()) {
			return getStartTime();
		} else if (now < getReadyEndTime()) {
			return getReadyEndTime();
		} else if (now < getEndTime()) {
			return getEndTime();
		} else {
			return getEndTime();
		}
	}

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public boolean isStartFlag() {
		return startFlag;
	}

	public void setStartFlag(boolean startFlag) {
		this.startFlag = startFlag;
	}

	public Long getReadyEndTime() {
		return readyEndTime;
	}

	public void setReadyEndTime(Long readyEndTime) {
		this.readyEndTime = readyEndTime;
	}

	public boolean isReadyEndFlag() {
		return readyEndFlag;
	}

	public void setReadyEndFlag(boolean readyEndFlag) {
		this.readyEndFlag = readyEndFlag;
	}

	public Long getEndTime() {
		return endTime;
	}

	public void setEndTime(Long endTime) {
		this.endTime = endTime;
	}

	public boolean isEndFlag() {
		return endFlag;
	}

	public void setEndFlag(boolean endFlag) {
		this.endFlag = endFlag;
	}

	public void setAllianceId2(Long allianceId2) {
		this.allianceId2 = allianceId2;
	}

	public void setAllianceId1(Long allianceId1) {
		this.allianceId1 = allianceId1;
	}

	public Long getAllianceId2() {
		return allianceId2;
	}

	public Long getAllianceId1() {
		return allianceId1;
	}

	public List<Integer> getResourceRefreshFlag() {
		return resourceRefreshFlag;
	}

	public void setResourceRefreshFlag(List<Integer> resourceRefreshFlag) {
		this.resourceRefreshFlag = resourceRefreshFlag;
	}

	public List<String> getNoticedOpenFlag() {
		return noticedOpenFlag;
	}

	public void setNoticedOpenFlag(List<String> noticedOpenFlag) {
		this.noticedOpenFlag = noticedOpenFlag;
	}

	public void putGvgBattleServerTimeInfo(GcGvgBattleServerTimeInfo gcGvgBattleServerTimeInfo) {
		gcGvgBattleServerTimeInfo.setCurrentStatus(getCurrentPsGVGBattleServerStatus());
		gcGvgBattleServerTimeInfo.setNextStatusTime(getNextStatusTime());
		gcGvgBattleServerTimeInfo.setBeginBattleTime(getReadyEndTime());
		gcGvgBattleServerTimeInfo.setGvgStartTime(getStartTime());
		gcGvgBattleServerTimeInfo.setEndBattleTime(getEndTime());
	}

}
