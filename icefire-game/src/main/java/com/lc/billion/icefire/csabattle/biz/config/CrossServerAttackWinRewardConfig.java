package com.lc.billion.icefire.csabattle.biz.config;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨服抢城任务配置
 * 
 * <AUTHOR>
 */
@Config(name = "CrossServerAttackWinReward", metaClass = CrossServerAttackWinRewardConfig.CrossServerAttackWinRewardMeta.class)
public class CrossServerAttackWinRewardConfig {

	private static final Logger logger = LoggerFactory.getLogger(CrossServerAttackWinRewardConfig.class);

	@MetaMap
	private Map<String, CrossServerAttackWinRewardMeta> dataById;

	public void init(List<CrossServerAttackWinRewardMeta> list) {
		dataById = new HashMap<>();
		for (CrossServerAttackWinRewardMeta meta : list) {
			dataById.put(meta.getId(), meta);
		}

		logger.debug("CrossServerAttackWinRewardConfig data = {}", dataById);
	}

	public CrossServerAttackWinRewardMeta getMeta(String metaId) {
		return dataById.get(metaId);
	}

	public Collection<CrossServerAttackWinRewardMeta> getAllMeta() {
		return dataById.values();
	}

	public static class CrossServerAttackWinRewardMeta extends AbstractMeta {
		/**
		 * 胜场数
		 */
		private int winNum;
		/**
		 * 奖励
		 */
		private String reward;
		/**
		 * 获得的奖杯id
		 */
		private String trophy;

		public int getWinNum() {
			return winNum;
		}

		public void setWinNum(int winNum) {
			this.winNum = winNum;
		}

		public String getReward() {
			return reward;
		}

		public void setReward(String reward) {
			this.reward = reward;
		}

		public String getTrophy() {
			return trophy;
		}

		public void setTrophy(String trophy) {
			this.trophy = trophy;
		}
	}
}
