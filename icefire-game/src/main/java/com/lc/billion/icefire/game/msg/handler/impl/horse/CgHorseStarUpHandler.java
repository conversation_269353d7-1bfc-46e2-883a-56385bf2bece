package com.lc.billion.icefire.game.msg.handler.impl.horse;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.horse.HorseServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgHorseStarUp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class CgHorseStarUpHandler extends CgAbstractMessageHandler<CgHorseStarUp> {

    @Autowired
    private HorseServiceImpl horseService;

    @Override
    protected void handle(Role role, CgHorseStarUp message) {
        horseService.horseStarUp(role, message.getHorseId());
    }
}
