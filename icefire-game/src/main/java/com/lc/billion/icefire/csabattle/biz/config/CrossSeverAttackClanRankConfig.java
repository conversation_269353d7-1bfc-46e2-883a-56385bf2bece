package com.lc.billion.icefire.csabattle.biz.config;

import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 跨服夺城胜利方触发联盟奖励分配的逻辑
 * 
 * <AUTHOR>
 */
@Config(name = "CrossSeverAttackClanRank", metaClass = CrossSeverAttackClanRankConfig.CrossSeverAttackRankMeta.class)
public class CrossSeverAttackClanRankConfig {

	private static final Logger logger = LoggerFactory.getLogger(CrossSeverAttackClanRankConfig.class);

	@MetaMap
	private Map<String, CrossSeverAttackRankMeta> dataById;

	private Map<Integer, List<CrossSeverAttackRankMeta>> groupByWinNum = new HashMap<>();

	public void init(List<CrossSeverAttackRankMeta> list) {
		dataById = new HashMap<>();
		for (CrossSeverAttackRankMeta meta : list) {
			dataById.put(meta.getId(), meta);
			groupByWinNum.compute(meta.getWinNum(), (k, v) -> v == null ? new ArrayList<>() : v).add(meta);
		}

		logger.debug("CrossSeverAttackClanRankConfig data = {}", dataById);
	}

	public CrossSeverAttackRankMeta getMeta(String metaId) {
		return dataById.get(metaId);
	}

	public Collection<CrossSeverAttackRankMeta> getAllMeta() {
		return dataById.values();
	}

	public Map<Integer, List<CrossSeverAttackRankMeta>> getGroupByWinNum() {
		return groupByWinNum;
	}

	public void setGroupByWinNum(Map<Integer, List<CrossSeverAttackRankMeta>> groupByWinNum) {
		this.groupByWinNum = groupByWinNum;
	}

	public static class CrossSeverAttackRankMeta extends AbstractMeta {

		/**
		 * 排名组
		 */
		protected int[] ranks;
		/**
		 * 排名奖励
		 */
		private String reward;
		/**
		 * 胜场数
		 */
		protected int winNum;

		@Override
		public void init(JsonNode jsonNode) {
			ranks = MetaUtils.parseInts(jsonNode.get("rank").asText(), META_SEPARATOR_2);
		}

		public int[] getRanks() {
			return ranks;
		}

		public void setRanks(int[] ranks) {
			this.ranks = ranks;
		}

		public String getReward() {
			return reward;
		}

		public void setReward(String reward) {
			this.reward = reward;
		}

		public int getWinNum() {
			return winNum;
		}

		public void setWinNum(int winNum) {
			this.winNum = winNum;
		}
	}
}
