package com.lc.billion.icefire.game.biz.model.official;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.lc.billion.icefire.protocol.structure.PsOfficialSkill;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * @ClassName OfficialSkillInfo
 * @Description 技能信息
 * <AUTHOR>
 * @Date 2024/8/9 18:16
 * @Version 1.0
 */
@Getter
@Setter
public class OfficialSkillInfo {

    /**
     * 技能信息
     */
    private Map<String, OfficialSkill> skillMap = Maps.newHashMap();

    /**
     * 操作记录
     */
    private List<OfficialOperateRecord> officialOperateRecords = Lists.newArrayList();

    /**
     * 被使用停火的玩家
     */
    private Set<Long> banAttackRoleIds = new HashSet<>();

    /**
     * 被使用驱逐的玩家
     */
    private Set<Long> expulsionRoleIds = new HashSet<>();



    public List<PsOfficialSkill> toPsOfficialSkillList() {
        List<PsOfficialSkill> officialSkillList = new ArrayList<>();
        for (OfficialSkill officialSkill:skillMap.values()) {
            officialSkillList.add(officialSkill.toPsOfficialSkill());
        }
        return officialSkillList;
    }
}
