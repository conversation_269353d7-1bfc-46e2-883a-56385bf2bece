package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import com.lc.billion.icefire.game.biz.model.favorites.RoleFavorite;
import org.jongo.marshall.jackson.oid.MongoId;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RoleGVGExtraInfo  extends AbstractEntity implements IRolesEntity {
    private static final long serialVersionUID = -8472331801693703146L;

    @MongoId
    private Long roleId;

    // 收藏点
    private List<RoleFavorite> favoriteList = new ArrayList<>();

    @Override
    public void setPersistKey(Long id) {
        roleId = id;
    }

    @Override
    public Long getPersistKey() {
        return roleId;
    }

    @Override
    public Long getGroupingId() {
        return roleId;
    }

    @Override
    public int hashCodeImpl() {
        return hashCodeForPersistKey();
    }

    @Override
    public boolean equalsImpl(Object obj) {
        return equalsForPersistKey(obj);
    }

    @Override
    public Long getRoleId() {
        return roleId;
    }

    @Override
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }


    public List<RoleFavorite> getFavoriteList() {
        if (favoriteList == null) {
            favoriteList = new ArrayList<>();
        }
        return favoriteList;
    }

    public RoleFavorite getFavorite(int id) {
        if (favoriteList == null) {
            return null;
        }
        for (RoleFavorite r : favoriteList) {
            if (r.getId() == id) {
                return r;
            }
        }
        return null;
    }

    public boolean deleteFavorite(int id) {
        RoleFavorite roleFavorite = getFavorite(id);
        if (roleFavorite != null) {
            getFavoriteList().remove(roleFavorite);
            return true;
        }
        return false;
    }

    public void addFavorite(RoleFavorite roleFavorite) {
        int id = roleFavorite.getServerId() * 100000000 + roleFavorite.getX() * 10000 + roleFavorite.getY();
        RoleFavorite old = getFavorite(id);
        if (old != null) {
            getFavoriteList().remove(old);
        }
        roleFavorite.setId(id);
        getFavoriteList().add(roleFavorite);
    }

    public void setFavoriteList(List<RoleFavorite> favoriteList) {
        this.favoriteList = favoriteList;
    }
}
