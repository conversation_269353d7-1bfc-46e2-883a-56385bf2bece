package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lc.billion.icefire.core.common.TimeUtil;
import org.jongo.marshall.jackson.oid.MongoId;

import com.lc.billion.icefire.game.biz.model.AbstractEntity;

/**
 * <AUTHOR>
 *
 */
public class AllianceBattlePoint extends AbstractEntity {

	private static final long serialVersionUID = -5808906810096390732L;

	@MongoId
	private Long allianceId;
	// 积分增长速度
	private double pointSpeed = 0;
	// 结束时间
	private long endTime = 0;
	// 上次结算时间
	private long lastCountTime = 0;
	// 上次结算后的累计值
	private int value = 0;

	// 积分分类，加一起为总积分
	// 杀怪积分
	private int killNpcScore;
	// 杀敌积分
	private int killEnemyScore;
	// 采集积分
	private int gatherScore;
	// 乌巢积分
	private int wuchaoScore;
	// 建筑积分
	private int buildingScore;

	// 总杀敌数
	private int killEnemyCount;
	// 占领官渡
	private long guanduOccupyTime = 0L;




	// 以下字段为OB新加
	//   0:成功 1:失败
	private int result = -1;

	// 中心城奖励积分
	private int armyFactoryScore;

	// 全场总治疗数
	private int totalCureScore;

	// 总集结数
	private int totalRallyCount;

	// 上次BI打点时，值是多少
	private long lastBiValue;


	public double getPointSpeed() {
		return pointSpeed;
	}

	public void setPointSpeed(double pointSpeed) {
		this.pointSpeed = pointSpeed;
	}

	public long getEndTime() {
		return endTime;
	}

	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public long getLastCountTime() {
		return lastCountTime;
	}

	public void setLastCountTime(long lastCountTime) {
		this.lastCountTime = lastCountTime;
	}

	public double additionPoint(long addTime){
        return (addTime - lastCountTime) * pointSpeed / TimeUtil.SECONDS_MILLIS;
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public int getArmyFactoryScore() {
		return armyFactoryScore;
	}

	public int getGatherScore() {
		return gatherScore;
	}

	public int getKillEnemyCount() {
		return killEnemyCount;
	}

	public int getKillEnemyScore() {
		return killEnemyScore;
	}

	public int getKillNpcScore() {
		return killNpcScore;
	}

	public int getResult() {
		return result;
	}

	public int getTotalCureScore() {
		return totalCureScore;
	}

	public int getTotalRallyCount() {
		return totalRallyCount;
	}

	public void setArmyFactoryScore(int armyFactoryScore) {
		this.armyFactoryScore = armyFactoryScore;
	}

	public void setGatherScore(int gatherScore) {
		this.gatherScore = gatherScore;
	}

	public void setKillEnemyScore(int killEnemyScore) {
		this.killEnemyScore = killEnemyScore;
	}

	public void setKillNpcScore(int killNpcScore) {
		this.killNpcScore = killNpcScore;
	}

	public void setResult(int result) {
		this.result = result;
	}

	public void setTotalCureScore(int totalCureScore) {
		this.totalCureScore = totalCureScore;
	}

	public void setTotalRallyCount(int totalRallyCount) {
		this.totalRallyCount = totalRallyCount;
	}

	public long getLastBiValue() {
		return lastBiValue;
	}

	public void setLastBiValue(long lastBiValue) {
		this.lastBiValue = lastBiValue;
	}

	@Override
	public void setPersistKey(Long id) {
		allianceId = id;
	}

	@Override
	public Long getPersistKey() {
		return allianceId;
	}

	@Override
	public Long getGroupingId() {
		return allianceId;
	}

	@Override
	public int hashCodeImpl() {
		return hashCodeForPersistKey();
	}

	@Override
	public boolean equalsImpl(Object obj) {
		return equalsForPersistKey(obj);
	}

	public int getWuchaoScore() {
		return wuchaoScore;
	}

	public void setWuchaoScore(int wuchaoScore) {
		this.wuchaoScore = wuchaoScore;
	}

	public int getBuildingScore() {
		return buildingScore;
	}

	public void setBuildingScore(int buildingScore) {
		this.buildingScore = buildingScore;
	}

	public long getGuanduOccupyTime() {
		return guanduOccupyTime;
	}

	public void setGuanduOccupyTime(long guanduOccupyTime) {
		this.guanduOccupyTime = guanduOccupyTime;
	}

	// 加官渡占领时间
	public void addGuanduOccupyTime(long add) {
		this.guanduOccupyTime += add;
	}

	// 加建筑积分
	public void addBuildingScore(int add) {
		this.buildingScore += add;
	}

	// 加乌巢积分
	public void addWuchaoScore(int add) {
		this.wuchaoScore += add;
	}

	public void addKillEnemyCount(int killCount) {
		killEnemyCount += killCount;
	}
}
