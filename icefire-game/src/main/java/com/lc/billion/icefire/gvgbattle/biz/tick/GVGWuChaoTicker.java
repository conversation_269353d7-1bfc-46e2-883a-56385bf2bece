package com.lc.billion.icefire.gvgbattle.biz.tick;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.common.AbstractTicker;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGWuChaoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class GVGWuChaoTicker extends AbstractTicker<Integer> {
    @Autowired
    GVGWuChaoManager wuChaoManager;

    public GVGWuChaoTicker() {
        super(TimeUtil.SECONDS_MILLIS);
    }

    @Override
    protected void tick(Integer nothing, long now) {
        wuChaoManager.tick();
    }

    @Override
    protected Collection<Integer> findAll() {
        return List.of(1);
    }
}
