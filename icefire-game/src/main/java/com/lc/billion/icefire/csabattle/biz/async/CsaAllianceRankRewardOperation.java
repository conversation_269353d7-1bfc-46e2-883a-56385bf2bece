package com.lc.billion.icefire.csabattle.biz.async;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackClanRankConfig;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackRankConfig;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackSetting;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivitySubType;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.alliance.benefitevent.AllianceBenefitsEventType;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerBattleInfo;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.redis.RedisDataType;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.CsaAlliancePointRank;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.CsaRoleBattlePointRank;
import com.lc.billion.icefire.game.config.ConfigHelper;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.resps.Tuple;

import java.util.*;

/**
 * @Author:CaoJian
 * @Date:2021/11/16 14:10
 */
public class CsaAllianceRankRewardOperation implements AsyncOperation {

    private static Logger logger = LoggerFactory.getLogger(CsaAllianceRankRewardOperation.class);

    private CSAActivityGroupContext context;

    private CSAServerBattleInfo csaServerBattleInfo;

    private AllianceServiceImpl allianceService;

    private RankDaoImpl rankDao;

    private AllianceMemberManager allianceMemberManager;

    private DropServiceImpl dropService;

    private CrossServerAttackServiceImpl crossServerAttackService;

    List<Alliance> allianceList = new ArrayList<>();


    public CsaAllianceRankRewardOperation(CSAActivityGroupContext context, CSAServerBattleInfo csaServerBattleInfo,
                                          AllianceServiceImpl allianceService, RankDaoImpl rankDao, AllianceMemberManager allianceMemberManager, DropServiceImpl dropService, CrossServerAttackServiceImpl crossServerAttackService) {
        this.context = context;
        this.csaServerBattleInfo = csaServerBattleInfo;
        this.allianceService = allianceService;
        this.rankDao = rankDao;
        this.allianceMemberManager = allianceMemberManager;
        this.dropService = dropService;
        this.crossServerAttackService = crossServerAttackService;
    }

    @Override
    public boolean run() {
        ConfigServiceImpl configService = Application.getBean(ConfigServiceImpl.class);
        int serverId = context.getSelfServerId();
        CsaAlliancePointRank rankHandler = Application.getBean(CsaAlliancePointRank.class);
        int rankListLimit = configService.getConfig(CrossSeverAttackSetting.class).getCsaRankListLimit();
        RankDaoImpl rankDao = Application.getBean(RankDaoImpl.class);
        String key = rankHandler.getCrossServerKey(serverId, RankType.CSA_ALLIANCE_RANK);
        // 获取排行榜数据
        var data = rankDao.getRank(key, 0, -1, RankType.CSA_ALLIANCE_RANK.getRedisType());
        if (!JavaUtils.bool(data)) {
            ErrorLogUtil.errorLog("run CsaAllianceRankRewardOperation is null", "serverId",serverId, "rankListLimit",rankListLimit);
            return false;
        }
        AllianceDao allianceDao = Application.getBean(AllianceDao.class);
        int rank = 0;
        for (Tuple t : data) {
            long memberId = Long.parseLong(t.getElement());
            Alliance alliance = allianceDao.findById(memberId);
            if (alliance == null) {
                ErrorLogUtil.errorLog("CsaAllianceRankRewardOperation| fail run alliance is null",
                        "rank",rank, "point",(int)t.getScore(),
                        "memberId",memberId);
            } else {
                logger.info("[CsaAllianceRankRewardOperation] | success rank={} point={} allianceId={} alliance={}", rank, (int)t.getScore(), memberId, alliance.getName());
            }
            // role可以为空
            allianceList.add(rank, alliance);
            rank = rank + 1;
            if (alliance != null) {
                crossServerAttackService.bi_csaCrossBattleAllianceRank(alliance.getoServerId(), alliance, t.getScore(), rank);
            }
        }
        sendAllianceReward();
        return false;
    }

    public void sendAllianceReward() {
        ConfigServiceImpl configService = ConfigHelper.getServiceInstance();
        CrossSeverAttackRankConfig crossSeverAttackRankConfig = configService.getConfig(CrossSeverAttackRankConfig.class);
        for (CrossSeverAttackRankConfig.CrossSeverAttackRankMeta rankMeta : crossSeverAttackRankConfig.getAllMeta()) {
            int[] ranks = rankMeta.getRanks();

            for (int j = ranks[0] - 1; j <= ranks[1] - 1; j++) {
                if (j > allianceList.size() - 1) {
                    break;
                }
                Alliance alliance = allianceList.get(j);
                if (alliance == null) {
                    continue;
                }

                logger.info("sendAllianceRankReward serverId:{},rankType:{},allianceId:{},rank:{},rankMetaId:{}", context.getSelfServerId(), RankType.CSA_ALLIANCE_RANK,
                        alliance.getPersistKey(), j, rankMeta.getId());

                if (csaServerBattleInfo == null) {
                    continue;
                }

                int allianceRank = j + 1;
                List<CrossSeverAttackClanRankConfig.CrossSeverAttackRankMeta> crossSeverAttackRankMetas = configService.getConfig(CrossSeverAttackClanRankConfig.class).getGroupByWinNum().get(csaServerBattleInfo.getCurrLeagueWinTime());
                if (crossSeverAttackRankMetas == null || crossSeverAttackRankMetas.isEmpty()) {
                    logger.info("config is null. win count is {}.", csaServerBattleInfo.getCurrLeagueWinTime());
                    continue;
                }

                String dropId = null;
                String sourceId = "";
                for (CrossSeverAttackClanRankConfig.CrossSeverAttackRankMeta crossSeverAttackRankMeta : crossSeverAttackRankMetas) {
                    if (allianceRank >= crossSeverAttackRankMeta.getRanks()[0] && allianceRank <= crossSeverAttackRankMeta.getRanks()[1]) {
                        sourceId = crossSeverAttackRankMeta.getId();
                        dropId = crossSeverAttackRankMeta.getReward();
                        break;
                    }
                }

                if (StringUtils.isEmpty(dropId)) {
                    logger.info("drop is null. win count is {}. rank is {}.", csaServerBattleInfo.getCurrLeagueWinTime(), allianceRank);
                    continue;
                }

                List<SimpleItem> drop = dropService.drop(dropId);
                if (drop == null || drop.isEmpty()) {
                    logger.info("drop is null. win count is {}. rank is {}.", csaServerBattleInfo.getCurrLeagueWinTime(), allianceRank);
                    continue;
                }

                String key = CsaRoleBattlePointRank.getKey(context.getRankId());
                RedisDataType redisDataType = RankType.CSA_ROLE_BATTLE_POINT_RANK.getRedisType();

                var data = rankDao.getRank(key, 0, Integer.MAX_VALUE, redisDataType);
                Map<Long, Long> memberScore = new HashMap<>();
                for (Tuple t : data) {
                    long memberId = Long.parseLong(t.getElement());
                    long score = (long) t.getScore();

                    AllianceMember member = allianceMemberManager.getMember(memberId);
                    if (member == null || !member.getAllianceId().equals(alliance.getId())) {
                        continue;
                    }

                    memberScore.put(memberId, score);
                }

                if (context.getSubType() == CSAActivitySubType.LEAGUE) {
                    allianceService.addAllianceBenefitsEvent(alliance, AllianceBenefitsEventType.CSA_WIN_COUNT, sourceId, drop, memberScore);
                }
            }
        }
    }

}
