package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.build;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmySetoutParam;
import com.lc.billion.icefire.game.biz.service.impl.army.build.DefaultRallyArmyBuilder;

/**
 * <AUTHOR>
 * @date 2021/04/29
 */
@Service
public class RallyPvpStrongHoldArmyBuilder extends DefaultRallyArmyBuilder {

	@Override
	public ArmyInfo build(Role role, ArmyType armyType, int stamina, SceneNode targetNode, ArmySetoutParam param) {
		ArmyInfo army = super.build(role, armyType, stamina, targetNode, param);
		army.getRallyContext().setGoInstantWhenRallyMemberMax(param.isGoInstantWhenRallyMemeberMax());
		return army;
	}
}
