package com.lc.billion.icefire.gvgbattle.biz.service.rpc.impl;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleServerInfoDao;
import com.lc.billion.icefire.game.biz.manager.RoleServerInfoManager;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.service.impl.gvg.GVGLocalTestService;
import com.lc.billion.icefire.game.biz.service.impl.role.RoleServiceImpl;
import com.lc.billion.icefire.rpc.service.gvg.IMigrateForGVGService;
import com.longtech.ls.rpc.migrate.MigrateRetCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 *
 */
@Service
public class MigrateForGVGServiceImpl implements IMigrateForGVGService {

	private static final Logger logger = LoggerFactory.getLogger(MigrateForGVGServiceImpl.class);

	@Autowired
	private RoleServerInfoManager roleServerInfoManager;
	@Autowired
	private RoleServiceImpl roleService;
	@Autowired
	private RoleServerInfoDao roleServerInfoDao;

	@Override
	public String doMigrateForGVG(Long roleId, int registerServerId, int srcServerId, int tarServerId, int homeServerId, Long allianceId, int gvgSide, boolean isBack) {

		MigrateRetCode retCode = MigrateRetCode.SUCCESS;
		logger.info("玩家 {} GVG迁服 start {}->{}，返回code{}", roleId, srcServerId, tarServerId, retCode);
		boolean loadResult = roleService.loadRoleDoGVGMigrate(roleId, registerServerId, srcServerId, tarServerId, isBack);
		logger.info("玩家 {} GVG迁服 end {}->{}，返回code {}, loadResult {}", roleId, srcServerId, tarServerId, retCode, loadResult);
		JSONObject result = new JSONObject();
		result.put("retCode", retCode.getId());
		return result.toJSONString();
	}

	public String doMigrateForGVGOld(Long roleId, int registerServerId, int srcServerId, int tarServerId, int homeServerId, Long allianceId, int gvgSide, boolean isBack) {
		MigrateRetCode retCode = MigrateRetCode.SUCCESS;
		// 生成RoleServerInfo
		RoleServerInfo roleServerInfo = roleServerInfoManager.createRoleServerInfoForMigrateGVG(roleId, registerServerId, tarServerId, homeServerId,gvgSide, isBack);
		logger.info("玩家{}GVG迁服{}->{}，返回code{}", roleId, srcServerId, tarServerId, retCode);
		// 在目标服捞取一下
		logger.info("捞取玩家防止其他系统拿不到玩家信息");
		roleService.loadUnactiveRoleFromDB(roleServerInfo.getRegisterServerId(), roleId);

		roleServerInfo.setActive(true);
		roleServerInfo.setIdle(false);
		roleServerInfoDao.saveRoleServerInfoToDB(roleServerInfo.getRegisterServerId(), roleServerInfo);

		JSONObject result = new JSONObject();
		result.put("retCode", retCode.getId());
		return result.toJSONString();
	}

	@Override
	public String clearGvGDataTest(boolean isClear) {
		if (!Application.isGVGBattleServer()) {
			return "wrong server";
		}
		GVGLocalTestService bean = Application.getBean(GVGLocalTestService.class);

		return bean.clearGVGBattleDataBase(isClear);
	}

	@Override
	public String checkAndGetService(int cType) {
		return switch (cType) {
			case 1 -> // mongo info
					Application.getConfigCenter().getLsConfig().getGameServers().get(Application.getServerId()).getMongoDb().toString();
			case 2 -> // redis info
					Application.getConfigCenter().getLsConfig().getGameServers().get(Application.getServerId()).getRedis().toString();
			default -> "success";
		};
	}
}
