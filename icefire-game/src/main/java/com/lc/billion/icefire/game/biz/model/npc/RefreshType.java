package com.lc.billion.icefire.game.biz.model.npc;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

/**
 * <AUTHOR>
 *
 */
public enum RefreshType implements IntEnum {

	/**
	 * 多怪模式，中心怪加九宫格怪物
	 */
	MAMY(0),
	/**
	 * 单怪模式，只刷中心
	 */
	ONE(1),
	//
	;

	private static final RefreshType[] INDEXES = EnumUtils.toArray(values());

	private int id;

	private RefreshType(int id) {
		this.id = id;
	}

	@Override
	public int getId() {
		return id;
	}

	public static RefreshType findById(int id) {
		if (id < 0 || id >= INDEXES.length) {
			return null;
		}
		return INDEXES[id];
	}

}
