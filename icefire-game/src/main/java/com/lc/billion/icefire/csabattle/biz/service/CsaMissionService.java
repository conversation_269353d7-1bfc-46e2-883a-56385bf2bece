package com.lc.billion.icefire.csabattle.biz.service;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csabattle.biz.async.CsaAllWorldRoleOperation;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackMissionConfig;
import com.lc.billion.icefire.csabattle.biz.dao.RoleCsaMissionDao;
import com.lc.billion.icefire.csabattle.biz.model.mission.CsaMissionType;
import com.lc.billion.icefire.csabattle.biz.model.mission.RoleCsaMission;
import com.lc.billion.icefire.csabattle.biz.model.mission.RoleCsaMissionItem;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityStatus;
import com.lc.billion.icefire.game.biz.manager.csa.CSAGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackActivityStatusListener;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackService;
import com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.MissionConstants;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.CsaRoleBattlePointRank;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcCsaMissionGetReward;
import com.lc.billion.icefire.protocol.GcCsaMissionList;
import com.lc.billion.icefire.protocol.GcCsaMissionUpdate;
import com.lc.billion.icefire.protocol.structure.PsMission;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 跨服抢城(CSA)活动-任务Service
 *
 * <AUTHOR>
 */
@Service
public class CsaMissionService implements ICrossServerAttackActivityStatusListener {

	private static final Logger logger = LoggerFactory.getLogger(CsaMissionService.class);

	@Autowired
	private ServiceDependency srvDep;
	@Autowired
	private RoleCsaMissionDao roleCsaMissionDao;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private CrossServerAttackServiceImpl crossServerAttackService;
	@Autowired
	private DropServiceImpl dropService;
	@Autowired
	private MailService mailService;
	@Autowired
	private ItemServiceImpl itemService;
	@Autowired
	private CsaBattleService csaBattleService;
	@Autowired
	protected WorldServiceImpl worldService;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private CSAGameDataVoManager csaGameDataVoManager;
	@Autowired
	private CsaRoleBattlePointRank csaRoleBattlePointRankHandler;

	@PostConstruct
	public void init() {
		logger.info("init");
	}

	public void startService() {
		logger.info("startService");
	}

	/**
	 * CSA活动开启时间
	 *
	 * @return 开启时间
	 */
	public long getCsaActivityStartTime() {
		CSAActivityVo activityVo = csaGameDataVoManager.getCsaActivity();
		if (activityVo == null) {
			return 0;
		}
		return activityVo.getStartTime();
	}

	/**
	 * 当前服武器是否为CSA目标服
	 *
	 * @return 是否为CSA目标服
	 */
	public boolean isCsaDefenceServer(int serverId) {
		return crossServerAttackService.getCSASide(serverId) == ICrossServerAttackService.CSA_SERVER_SIDE.CSA_DEFENCE;
	}

	public boolean isCsaAttacker(Role role) {
		return crossServerAttackService.isCSACrossPlayer(role);
	}

	/**
	 * 当角色进入世界时事件处理
	 *
	 * @param role
	 *            角色
	 */
	public void onEnterWorld(Role role) {

		logger.info("Role enter world. role = {}", role.getId());
		int serverId = role.getoServerId();
		// 活动结束后，玩家登录时的相关处理
		if (crossServerAttackService.isCsaActivityOver(serverId)) {
			this.onRoleCsaEnd(role);
			return;
		}

		if (!crossServerAttackService.isCSAServer(serverId)) {
			return;
		}

		// 玩家进入游戏后，如果当前csa活动已经开始
		this.onRoleCsaStart(role);

		boolean isCsaDefenceServer = this.isCsaDefenceServer(serverId);
		boolean isCsaAttacker = this.isCsaAttacker(role);

		// 当玩家做为攻击方进入目标服务器
		if (isCsaDefenceServer && isCsaAttacker) {
			this.onRoleEnterCsaTargetServer(role);
		}

		logger.info("Role enter World. role = {}, roleCsaMission = {}, isCsaTargetServer = {}, isCsaAttacker = {}", role.getId(), this.getRoleCsaMission(role), isCsaDefenceServer,
				isCsaAttacker);
	}

	public void onRoleCsaStart(Role role) {
		RoleCsaMission roleCsaMission = this.getRoleCsaMission(role);
		if (roleCsaMission == null) {
			roleCsaMission = this.createRoleCsaMission(role);
		}

		// 设置CSA开放时间
		if (roleCsaMission.getLastCsaOpenTime() == 0) {

			roleCsaMission.setLastCsaOpenTime(this.getCsaActivityStartTime());

			// 根据策划配置，调整任务，清除所有任务，并重新添加
			CrossSeverAttackMissionConfig config = configService.getConfig(CrossSeverAttackMissionConfig.class);
			roleCsaMission.clearMissionItems();
			for (CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta meta : config.getAllMeta()) {
				RoleCsaMissionItem roleCsaMissionItem = this.createMissionItem(meta);
				roleCsaMission.addItem(roleCsaMissionItem);
			}

			// 保存数据
			roleCsaMissionDao.save(roleCsaMission);
		}

		CSAActivityGroupContext ctx = crossServerAttackService.getCsaActivityGroupContext(role.getoServerId());
		CSAActivityStatus status = ctx == null ? null : ctx.getCsaActivityStatus();

		// 如果角色登录时，战斗已经结束了，将任务结算一下
		if (status == CSAActivityStatus.OVER) {
			// 根据ctx里面是否胜利的字段判断获胜服id
			final int winServerId = ctx.isActivityWin() ? ctx.getSelfServerId() : ctx.getAgainstServerId();
			settleRoleMission(role, winServerId);
		}

		// 发任务列表消息
		this.sendGcCsaMissionList(role);
	}

	@Override
	public void csaActivityStart(CSAActivityGroupContext ctx) {
		onCsaStart(ctx.getSelfServerId());
	}

	/**
	 * 当CSA开始时
	 */
	public void onCsaStart(int serverId) {

		logger.info("Server{} csa start.", serverId);

		// 异步处理
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::onRoleCsaStart, true, "onCsaStart", serverId);
		asyncOperationService.execute(operation);
	}

	/**
	 * 发邮件列表消息
	 *
	 * @param role
	 *            角色
	 */
	public void sendGcCsaMissionList(Role role) {
		RoleCsaMission roleCsaMission = this.getRoleCsaMission(role);
		if (roleCsaMission == null) {
			return;
		}

		Integer rank = csaRoleBattlePointRankHandler.getRank(role);
		GcCsaMissionList gcCsaMissionList = this.buildGcCsaMissionList(roleCsaMission);
		gcCsaMissionList.setRank(rank == null ? 0 : rank + 1);
		role.send(gcCsaMissionList);
		logger.info("Send GcCsaMissionList to Role. role = {}, gcCsaMissionList = {}", role.getId(), gcCsaMissionList);
	}

	/**
	 * 构建任务列表消息
	 *
	 * @param roleCsaMission
	 *            角色任务实体
	 * @return 任务列表消息
	 */
	public GcCsaMissionList buildGcCsaMissionList(RoleCsaMission roleCsaMission) {
		GcCsaMissionList gcCsaMissionList = new GcCsaMissionList();
		for (RoleCsaMissionItem item : roleCsaMission.getAllItems()) {
			CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta meta = this.getCrossSeverAttackMissionMeta(item.getMetaId());
			if (meta == null) {
				continue;
			}
			PsMission psMission = this.buildPsMission(item);
			gcCsaMissionList.addToMissions(psMission);
		}
		return gcCsaMissionList;
	}

	/**
	 * 构建任务消息
	 *
	 * @param item
	 *            角色任务Item实体
	 * @return 任务消息
	 */
	public PsMission buildPsMission(RoleCsaMissionItem item) {
		PsMission m = new PsMission();
		m.setMetaId(item.getMetaId());
		m.setProgress(item.getProgress());
		m.setCompleted(item.isFinish());
		m.setAward(item.isReceived());
		return m;
	}

	/**
	 * 获取任务meta数据
	 *
	 * @param missionMetaId
	 *            任务metaId
	 * @return 任务meta数据
	 */
	public CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta getCrossSeverAttackMissionMeta(String missionMetaId) {
		return configService.getConfig(CrossSeverAttackMissionConfig.class).getMeta(missionMetaId);
	}

	/**
	 * 领取任务奖励
	 *
	 * @param role
	 *            角色
	 * @param missionMetaId
	 *            任务metaId
	 */
	public void receiveMissionReward(Role role, String missionMetaId) {
		RoleCsaMission roleCsaMission = this.getRoleCsaMission(role);
		if (roleCsaMission == null) {
			return;
		}

		RoleCsaMissionItem missionItem = roleCsaMission.getMissionItem(missionMetaId);

		// 判断奖励是否可以
		if (!missionItem.isCanReceive()) {
			ErrorLogUtil.errorLog("Role can't Receive mission reward", "roleId",role.getId(), "missionId",missionMetaId);
			return;
		}

		// 掉落
		List<SimpleItem> list = dropService.drop(missionItem.getReward());
		// 给物品
		itemService.give(role, list, LogReasons.ItemLogReason.CSA_MISSION_REWARD);
		// 设置状态
		missionItem.setReceived(true);
		// 保存
		roleCsaMissionDao.save(roleCsaMission);

		// 创建返回消息
		GcCsaMissionGetReward gcCsaMissionGetReward = new GcCsaMissionGetReward();
		gcCsaMissionGetReward.setMetaId(missionItem.getMetaId());
		gcCsaMissionGetReward.setResult(true);

		// 发消息
		role.send(gcCsaMissionGetReward);

		// bi
		bi_csaCrossBattleTaskReward(role, missionItem.getMetaId(), missionItem.getReward());
	}

	/**
	 * 获取角色任务实体
	 *
	 * @param role
	 *            角色
	 * @return 角色任务实体
	 */
	public RoleCsaMission getRoleCsaMission(Role role) {
		return roleCsaMissionDao.findById(role.getId());
	}

	/**
	 * 创建角色任务实体
	 *
	 * @param role
	 *            角色
	 * @return 角色任务实体
	 */
	public RoleCsaMission createRoleCsaMission(Role role) {
		return roleCsaMissionDao.create(role);
	}

	/**
	 * 创建角色任务Item实体
	 *
	 * @param meta
	 *            Meta数据
	 * @return 任务Item实体
	 */
	public RoleCsaMissionItem createMissionItem(CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta meta) {
		RoleCsaMissionItem item = new RoleCsaMissionItem();
		item.setMetaId(meta.getId());
		item.setReward(meta.getReward());
		item.setType(meta.getMissionType());
		item.setStatus(MissionConstants.MISSION_STATUS_UNFINISH);
		item.setProgress(0);
		item.setReceived(false);
		return item;
	}

	/**
	 * 对角色每个任务Item进行函数处理
	 *
	 * @param role
	 *            角色
	 * @param function
	 *            处理函数
	 */
	public void forEachCsaMissionItem(Role role, Function<RoleCsaMissionItem, Boolean> function) {
		if (function == null) {
			return;
		}

		RoleCsaMission roleCsaMission = this.getRoleCsaMission(role);
		if (roleCsaMission == null || roleCsaMission.getAllItems().isEmpty()) {
			return;
		}

		GcCsaMissionUpdate gcCsaMissionUpdate = new GcCsaMissionUpdate();
		for (RoleCsaMissionItem missionItem : roleCsaMission.getAllItems()) {
			if (missionItem == null) {
				continue;
			}
			if (function.apply(missionItem)) {

				gcCsaMissionUpdate.addToMissions(this.buildPsMission(missionItem));

				if (missionItem.isFinish()) {
					onRoleMissionFinished(role, missionItem);
				}
			}
		}

		if (gcCsaMissionUpdate.getMissionsSize() > 0) {
			roleCsaMissionDao.save(roleCsaMission);
			role.send(gcCsaMissionUpdate);
		}

	}

	/**
	 * 当角色进入CSA目标服时事件处理
	 *
	 * @param role
	 *            角色
	 */
	public void onRoleEnterCsaTargetServer(Role role) {
		Function<RoleCsaMissionItem, Boolean> function = missionItem -> {

			CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta meta = this.getCrossSeverAttackMissionMeta(missionItem.getMetaId());
			if (meta == null) {
				return false;
			}

			if (missionItem.isFinish()) {
				return false;
			}

			if (missionItem.getType() != CsaMissionType.EnterCsaTargetServer.getId()) {
				return false;
			}

			missionItem.setProgress(1);
			missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
			return true;
		};

		// 处理
		forEachCsaMissionItem(role, function);
	}

	/**
	 * 当角色CSA积分变化时事件处理
	 *
	 * @param role
	 *            角色
	 */
	public void onRoleCsaPointChanged(Role role, long roleCsaPoint) {

		Function<RoleCsaMissionItem, Boolean> function = missionItem -> {

			if (missionItem.isFinish()) {
				return false;
			}

			CsaMissionType missionType = CsaMissionType.findById(missionItem.getType());
			CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta missionMeta = getCrossSeverAttackMissionMeta(missionItem.getMetaId());
			if (missionMeta == null) {
				return false;
			}

			if (missionType == CsaMissionType.CsaPointChanged) {
				// 积分任务
				missionItem.setProgress(roleCsaPoint);
				if (missionItem.getProgress() >= missionMeta.getParam1()) {
					missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
				}
				return true;
			} else if (missionType == CsaMissionType.CsaWin || missionType == CsaMissionType.CsaLose) {
				// 胜利失败任务
				missionItem.setProgress(Math.min(roleCsaPoint, missionMeta.getParam1()));
				return true;
			} else {
				return false;
			}

		};

		// 处理
		forEachCsaMissionItem(role, function);
	}

	/**
	 * 角色任务结算
	 *
	 * @param role
	 *            角色
	 */
	public void settleRoleMission(Role role, int winServerId) {

		final int roleOriginGameServerId = csaBattleService.getRoleOriginGameServer(role);
		final boolean win = (roleOriginGameServerId == winServerId);

		logger.info("csa settleRoleMission. role={}, roleOriginGameServerId={}, winServerId={}, win={}", role.getId(), roleOriginGameServerId, winServerId, win);

		Function<RoleCsaMissionItem, Boolean> function = null;

		if (win) {

			function = missionItem -> {
				if (missionItem.isFinish()) {
					return false;
				}
				if (missionItem.getType() != CsaMissionType.CsaWin.getId()) {
					return false;
				}
				CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta missionMeta = getCrossSeverAttackMissionMeta(missionItem.getMetaId());
				if (missionMeta == null || missionItem.getProgress() < missionMeta.getParam1()) {
					return false;
				}
				missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
				return true;
			};

		} else {
			function = missionItem -> {
				if (missionItem.isFinish()) {
					return false;
				}
				if (missionItem.getType() != CsaMissionType.CsaLose.getId()) {
					return false;
				}
				CrossSeverAttackMissionConfig.CrossSeverAttackMissionMeta missionMeta = getCrossSeverAttackMissionMeta(missionItem.getMetaId());
				if (missionMeta == null || missionItem.getProgress() < missionMeta.getParam1()) {
					return false;
				}
				missionItem.setStatus(MissionConstants.MISSION_STATUS_FINISH);
				return true;
			};
		}

		// 处理
		forEachCsaMissionItem(role, function);
	}

	public void onRoleMissionFinished(Role role, RoleCsaMissionItem missionItem) {
		// bi log
		bi_csaCrossBattleTaskFinish(role, missionItem.getMetaId());
	}

	public void onRoleCsaEnd(Role role) {

		RoleCsaMission roleCsaMission = this.getRoleCsaMission(role);
		if (roleCsaMission == null || roleCsaMission.getAllItems().isEmpty()) {
			return;
		}

		// 处理未领奖的任务
		List<SimpleItem> dropItemList = new ArrayList<>();
		for (RoleCsaMissionItem missionItem : roleCsaMission.getAllItems()) {
			// 如果任务已经可以领取奖励
			if (missionItem.isCanReceive()) {
				missionItem.setReceived(true);
				List<SimpleItem> dropItems = dropService.drop(missionItem.getReward());
				dropItemList.addAll(dropItems);
			}
		}

		// 发系统补发邮件
		if (!dropItemList.isEmpty()) {
			mailService.sendSystemEmail(role, EmailConstants.CSA_MISSION_UNRECEIVED_REWARD, dropItemList, new ArrayList<>());
		}

		// 清除设置活动开始时间
		roleCsaMission.setLastCsaOpenTime(0);

		// 清空任务项
		roleCsaMission.clearMissionItems();

		// 保存数据
		roleCsaMissionDao.save(roleCsaMission);

		// 发送清空任务消息
		GcCsaMissionList gcCsaMissionList = new GcCsaMissionList();
		gcCsaMissionList.setMissions(new ArrayList<>());
		Integer rank = csaRoleBattlePointRankHandler.getRank(role);
		gcCsaMissionList.setRank(rank == null ? 0 : rank + 1);
		role.send(gcCsaMissionList);
	}

	@Override
	public void csaActivitySettleStart(CSAActivityGroupContext context) {
		CSAActivityGroupContext ctx = crossServerAttackService.getCsaActivityGroupContext(context.getSelfServerId());

		// 根据ctx里面是否胜利的字段判断获胜服id
		final int winServerId = ctx.isActivityWin() ? ctx.getSelfServerId() : ctx.getAgainstServerId();

		logger.info("Server{} csaActivitySecondHalfEnd. winServerId = {}", ctx.getSelfServerId(), winServerId);

		CsaAllWorldRoleOperation op = new CsaAllWorldRoleOperation(srvDep, role -> settleRoleMission(role, winServerId), false, "csaActivitySettleStart settleRoleMission",
				ctx.getSelfServerId());
		op.exec();
		// asyncOperationService.execute(op);
	}

	@Override
	public void csaActivityEnd(CSAActivityGroupContext ctx) {

		logger.info("Server{} csaActivityEnd.", ctx.getSelfServerId());

		CsaAllWorldRoleOperation op = new CsaAllWorldRoleOperation(srvDep, this::onRoleCsaEnd, false, "csaActivityEnd onRoleCsaEnd", ctx.getSelfServerId());
		asyncOperationService.execute(op);
	}

	@Override
	public int getServiceOrder() {
		return 2;
	}

	private void bi_csaCrossBattleTaskFinish(Role role, String taskId) {
		try {
			Long roleId = role.getRoleId();
			int serverId = role.getoServerId();
			int mainBuildingLevel = role.getLevel();
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int sessionId = crossServerAttackService.bi_sessionId(serverId);
			srvDep.getBiLogUtil().csaCrossBattleTaskFinish(roleId, mainBuildingLevel, crossBattleId, timePeriod, attackServerId, defendServerId, sessionId, taskId);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleTaskFinish error", e);
		}
	}

	private void bi_csaCrossBattleTaskReward(Role role, String taskId, String rewardId) {
		try {
			Long roleId = role.getRoleId();
			int serverId = role.getoServerId();
			int mainBuildingLevel = role.getLevel();
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int sessionId = crossServerAttackService.bi_sessionId(serverId);
			srvDep.getBiLogUtil().csaCrossBattleTaskReward(roleId, mainBuildingLevel, crossBattleId, timePeriod, attackServerId, defendServerId, sessionId, taskId, rewardId);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleTaskReward error", e);
		}
	}
}
