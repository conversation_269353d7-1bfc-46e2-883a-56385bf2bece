package com.lc.billion.icefire.game.biz.async.arena;

import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName ArenaUpdateDefencePowerOperation
 * @Description
 * <AUTHOR>
 * @Date 2024/1/12 09:51
 * @Version 1.0
 */
public class ArenaUpdateDefencePowerOperation implements AsyncOperation {

    private final static Logger logger = LoggerFactory.getLogger(ArenaUpdateDefencePowerOperation.class);

    private final ServiceDependency srvDpd;
    private final Role role;

    public ArenaUpdateDefencePowerOperation(ServiceDependency srvDpd, Role role) {
        this.role = role;
        this.srvDpd = srvDpd;
    }

    @Override
    public boolean run() {
        try {
            srvDpd.getArenaService().updateDefencePower(role);
            return true;
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("ArenaUpdateDefencePowerOperation error", e);
            }
            return false;
        }
    }
}
