package com.lc.billion.icefire.game.biz.model.activity.mission;

import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.config.EventGroupConfig;
import com.lc.billion.icefire.game.biz.config.EventScoreRewardConfig;
import com.lc.billion.icefire.game.biz.model.activity.ActivityRecord;
import com.lc.billion.icefire.game.biz.model.mission.commonActivity.CommonActivityMissionItem;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.config.ConfigHelper;
import com.lc.billion.icefire.protocol.constant.PsRewardClaimStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Setter
@Getter
public class RoleCommonMission extends ActivityRecord {
    private static final long serialVersionUID = 5398702278073055682L;

    /**
     * 任务列表 key是missionId, value 是任务信息
     */
    private Map<String, CommonActivityMissionItem> missionItemMap = new HashMap<>();
    /**
     * 当天次数（客户端拿到的都是当天的总次数，要存上一次的，方便计算增量）key 是missionType, value 是次数
     */
    private Map<Integer, Integer> curDayCountMap = new HashMap<>();

    /**
     * 已完成的id列表
     */
    private Set<String> finishList = new HashSet<>();

    // 上次刷新时间
    private long lastRefreshTime;

    private int score;
    private List<PsRewardClaimStatus> commonRewards = new ArrayList<>();
    private List<PsRewardClaimStatus> vipRewards = new ArrayList<>();

    public void refreshReward(int vipLevel) {
        var activityMeta = ConfigHelper.getServiceInstance().getConfig(ActivityListConfig.class).getMetaById(getMetaId());
        if(activityMeta == null){
            ErrorLogUtil.errorLog("refreshReward activityMeta is null","metaId", getMetaId());
            return;
        }
        var subId = activityMeta.getGroup();
        var eventGroupMeta = ConfigHelper.getServiceInstance().getConfig(EventGroupConfig.class).getGroupMeta(subId);
        if(eventGroupMeta == null){
            ErrorLogUtil.errorLog("refreshReward eventGroupMeta is null","metaId", getMetaId());
            return;
        }
        var eventScoreRewardConfig = ConfigHelper.getServiceInstance().getConfig(EventScoreRewardConfig.class);
        if(eventScoreRewardConfig==null){
            return;
        }
        var groupMap = eventScoreRewardConfig.getGroupMap();
        if(groupMap==null){
            return;
        }
        var groupId = eventGroupMeta.getGroupid();
        var metaMap = groupMap.get(groupId);
        if(metaMap==null){
            return;
        }

        var metas = metaMap.values();
        for (var meta : metas) {
            var index = eventScoreRewardConfig.getIndexByMeta(meta);
            if(index < 0){
                continue;
            }
            if (score < meta.getActivity()) {
                continue;
            }

            if (this.commonRewards.get(index) == PsRewardClaimStatus.INVALID) {
                this.commonRewards.set(index, PsRewardClaimStatus.CAN_BE_CLAIMED);
            }

            if (this.vipRewards.get(index) == PsRewardClaimStatus.INVALID && vipLevel >= meta.getVipLevel()) {
                this.vipRewards.set(index, PsRewardClaimStatus.CAN_BE_CLAIMED);
            }
        }
    }

    @Override
    public void clear() {
        curDayCountMap.clear();
    }
}
