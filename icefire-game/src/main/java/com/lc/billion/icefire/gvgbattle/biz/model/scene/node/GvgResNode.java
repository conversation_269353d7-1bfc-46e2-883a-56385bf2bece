package com.lc.billion.icefire.gvgbattle.biz.model.scene.node;

import com.google.api.client.util.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.battle.FightArmy;
import com.lc.billion.icefire.game.biz.battle.FightArmyCreator;
import com.lc.billion.icefire.game.biz.battle.FightProp;
import com.lc.billion.icefire.game.biz.battle.calculator.FightUnitPropCalculate;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGResNodeDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconArmyInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconInfo;
import com.lc.billion.icefire.game.biz.model.email.recon.ReconRoleInfo;
import com.lc.billion.icefire.game.biz.model.hero.Hero;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.info.DefaultRoleInfo;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.armyTarget.IReconTargetNode;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.scene.MapNodeUpdater;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.protocol.GcMapNodeUpdate;
import com.lc.billion.icefire.protocol.structure.PsMapGvgResInfo;
import com.lc.billion.icefire.protocol.structure.PsMapNode;
import com.lc.billion.icefire.protocol.structure.PsSimpleArmyInfo;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.thrift.TBase;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * GVG战场物资点
 */
public class GvgResNode extends GVGSceneNode implements IReconTargetNode {
    private String metaId;

    /**
     * 归属roleId
     */
    private Long roleId;

    /**
     * 采集部队id
     */
    private Long armyId;

    /**
     * 下次刷新时间，也是消失时间
     */
    private AtomicLong nextRefreshTime = new AtomicLong();

    /**
     * 剩余资源
     */
    private Long resReversed;

    @Override
    public SceneNodeType getNodeType() {
        return SceneNodeType.GVG_RES;
    }

    public String getMetaId() {
        return metaId;
    }

    public void setMetaId(String metaId) {
        this.metaId = metaId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        Long oldOwnerId = this.roleId;
        this.roleId = roleId;
        Application.getBean(GVGResNodeDao.class).changeOwnerIndex(this, oldOwnerId);
    }

    public Long getArmyId() {
        return armyId;
    }

    public void setArmyId(Long armyId) {
        this.armyId = armyId;
    }

    public Long getNextRefreshTime() {
        return nextRefreshTime.get();
    }

    public void setNextRefreshTime(long nextRefreshTime) {
        this.nextRefreshTime.set(nextRefreshTime);
    }


    public Long getResReversed() {
        return resReversed;
    }

    public void setResReversed(Long resReversed) {
        this.resReversed = resReversed;
    }

    @Override
    public String toString() {
        return "GvgResNode [metaId=" + metaId + ",roleId=" + roleId + ",nextRefreshTime=" + nextRefreshTime + "]" + super.toString();
    }

    @Override
    public String getMetaName() {
        var meta = Application.getBean(ConfigServiceImpl.class).getConfig(GvgBuildingConfig.class).get(getMetaId());
        if (meta == null) {
            return "";
        }
        return meta.getBuildName();
    }

    public PsMapGvgResInfo toPsMapGvgResInfo() {
        PsMapGvgResInfo info = new PsMapGvgResInfo();
        info.setId(String.valueOf(getPersistKey()));
        info.setMetaId(this.getMetaId());
        info.setNextRefreshTime(this.getNextRefreshTime());
        if (hasOwner()) {
            info.setOwnerId(String.valueOf(roleId));
            RoleManager roleManager = Application.getBean(RoleManager.class);
            AllianceDao allianceDao = Application.getBean(AllianceDao.class);
            Role role = roleManager.getRole(this.roleId);
            if (role != null) {
                info.setName(role.getName() == null ? "" : role.getName());
                if (role.getAllianceId() != null) {
                    Alliance alliance = allianceDao.findById(role.getAllianceId());
                    if (alliance != null) {
                        info.setAllianceId(String.valueOf(role.getAllianceId()));
                        info.setAllianceAliasName(alliance.getAliasName() == null ? "" : alliance.getAliasName());
                    }
                }
                info.setHead(role.getHead() == null ? "" : role.getHead());
                info.setRoleInfo(role.toPsRoleInfo());
            }
        } else {
            // 前端需要判断变化
            info.setOwnerId("");
            info.setName("");
            info.setAllianceId("");
            info.setAllianceAliasName("");
            info.setHead("");
            info.setRoleInfo(DefaultRoleInfo.toPsRoleInfo());
        }

        ArmyInfo armyInfo = getArmyInfo();
        var gatherContext = armyInfo == null ? null : armyInfo.getGvgGatherContext();
        if (gatherContext != null) {
            info.setUpdateTime(gatherContext.getUpdateTime());
            info.setCurrExploitSpeed(gatherContext.getSpeed());
            info.setCurrentGatherAmount((long) gatherContext.getCurrentGatherAmount());
            info.setMaxCarry(gatherContext.getCarry());
        }

        info.setResReversed(resReversed);

        return info;
    }
    public ArmyInfo getArmyInfo() {
        return JavaUtils.bool(armyId) ? Application.getBean(ArmyDao.class).findById(this.getArmyId()) : null;
    }

    private PsSimpleArmyInfo armyIdToSimpleInfo(Long armyId) {
        if (!JavaUtils.bool(armyId)) {
            return null;
        }

        ArmyDao armyDao = Application.getBean(ArmyDao.class);
        ArmyInfo army = armyDao.findById(armyId);
        if (army == null) {
            return null;
        }
        Application.getBean(ServiceDependency.class);
        return toSimpleInfo(army);
    }

    public static PsSimpleArmyInfo toSimpleInfo(ArmyInfo army) {
        ServiceDependency srvDpd = Application.getBean(ServiceDependency.class);
        Role role = army.getOwner();
        PsSimpleArmyInfo info = new PsSimpleArmyInfo();
        info.setArmyId(army.getPersistKey());
        info.setOwnerId(army.getRoleId());
        info.setOwnerName(role.getName());
        info.setOwnerHead(role.getHead());
        info.setAllianceId(String.valueOf(role.getAllianceId()));
        info.setAllianceAlias(srvDpd.getAllianceService().getAllianceAliasName(role));
        info.setOwnerInfo(role.toPsRoleInfo());
        return info;
    }

    @Override
    public PsMapNode toPsMapNode() {
        PsMapNode psNode = super.toPsMapNode();
        PsSimpleArmyInfo armyInfo = armyIdToSimpleInfo(this.getArmyId());
        if (armyInfo != null) {
            psNode.setArmyInfo(armyInfo);
        }
        return psNode;
    }

    @Override
    public GcMapNodeUpdate toMapNodeUpdate(MapNodeUpdater updater) {
        GcMapNodeUpdate msg = new GcMapNodeUpdate();
        PsMapNode psNode = this.toPsMapNode();
        if (updater != null) {
            updater.accept(psNode);
        }
        msg.addToNodes(psNode);
        return msg;
    }

    @Override
    public String getBIConfigId() {
        return metaId == null ? "" : metaId;
    }

    public boolean inGathering() {
        return armyId != null && armyId.longValue() != 0l;
    }

    public boolean hasOwner() {
        return roleId != null && roleId.longValue() != 0l;
    }

    @Override
    public boolean canRecon(Role role) {
        if (!inGathering())
            return false;
        ArmyInfo resArmy = Application.getBean(ArmyManager.class).findById(getArmyId());
        if (resArmy == null)
            return false;
        Role targetRole = resArmy.getOwner();
        if (targetRole == null)
            return false;
        // 同联盟检测
        if (Application.getBean(AllianceServiceImpl.class).isSameAlliance(role.getPersistKey(), getRoleId()))
            return false;
        return true;
    }

    @Override
    public void reconBaseInfo(ReconInfo reconInfo) {
        ReconRoleInfo roleInfo = new ReconRoleInfo();
        roleInfo.setX(getX());
        roleInfo.setY(getY());
        ArmyInfo army = Application.getBean(ArmyManager.class).findById(armyId);
        if (army == null) {
            return;
        }
        Role role = Application.getBean(RoleManager.class).getRole(getRoleId());
        roleInfo.setRoleId(role.getPersistKey());
        roleInfo.setPlayerName(role.getName());
        roleInfo.setHeadIcon(role.getHead());
        roleInfo.setRoleInfo(role.toRoleInfo());
        Alliance allianceByRole = Application.getBean(AllianceServiceImpl.class).getAllianceByRole(role);
        if (allianceByRole != null) {
            roleInfo.setAllianceShortName(allianceByRole.getAliasName());
            roleInfo.setAllianceName(allianceByRole.getName());
        }
        reconInfo.setReconRoleInfo(roleInfo);

        ReconArmyInfo reconArmyInfo = new ReconArmyInfo();
        for (var entry : army.getArmySoldiers().entrySet()) {
            reconArmyInfo.getSoldierMap().put(entry.getKey(), entry.getValue().getCount());
        }
        List<Hero> heroList = Lists.newArrayList();
        HeroServiceImpl heroService = Application.getBean(HeroServiceImpl.class);
        for (var entry : army.getHeros()) {
            var hero = heroService.getHero(role, entry);
            heroList.add(hero);
            reconArmyInfo.getHeroInfoList().add(HeroOutput.toInfo(hero));
        }
        FightProp fightProp = new FightProp();
        fightProp.setRoleProps(role.getNumberProps());
        fightProp.setHeroBuffValues(FightArmy.heroProps(heroList, fightProp));
        reconInfo.setEffectMap(FightUnitPropCalculate.calcReconProp(fightProp, getNodeType()));
        reconInfo.setReconArmyInfo(reconArmyInfo);
    }

    @Override
    public Role reconOwnerRole() {
        return Application.getBean(RoleManager.class).getRole(getRoleId());
    }

    @Override
    public List<ArmyInfo> reconReinforceInfo() {
        return null;
    }


    @Override
    public PsMapNode._Fields getField() {
        return PsMapNode._Fields.GVG_RES_INFO;
    }

    @Override
    public TBase<?, ?> getFieldValue() {
        return toPsMapGvgResInfo();
    }

    @Override
    public FightArmy createFightArmy() {
        var armyInfo = getArmyInfo();
        if (armyInfo == null) {
            return null;
        }

        return Application.getBean(FightArmyCreator.class).createFightArmy(armyInfo, false);
    }

    public boolean isExpire() {
        return this.nextRefreshTime.get() < TimeUtil.getNow();
    }

    public boolean hasRemain() {
        return resReversed > 0;
    }

    public void decResource(long delta) {
        resReversed = resReversed < delta ? 0 : resReversed - delta;
    }
}
