package com.lc.billion.icefire.gvgbattle.biz.model.gvg;

import com.simfun.sgf.enumtype.EnumUtils;
import com.simfun.sgf.enumtype.IntEnum;

public enum AddPointType implements IntEnum {
    /**
     * debug
     * */
    DEBUG(0),

    /**
     * 采集
     * */
    GATHER(1),

    /**
     * 杀怪
     * */
    KILL_NPC(2),
    /**
     * 乌巢
     */
    WUCHAO_COMPLETE(3),
    /**
     *  首占
     */
    FIRST_OCCUPY(4),
    /**
     * 持续占领
     */
    OCCUPYING(5),
    /**
     * 杀敌
     * */
    KILL_ENEMY(6),
    ;

    private static final AddPointType[] INDEXES = EnumUtils.toArray(values());

    private final int type;

    private AddPointType(int type) {
        this.type = type;
    }

    @Override
    public int getId() {
        return type;
    }

    public int getPsType() { return  type; }

    public static AddPointType findById(int id) {
        if (id < 0 || id >= INDEXES.length) {
            return null;
        }
        return INDEXES[id];
    }
}
