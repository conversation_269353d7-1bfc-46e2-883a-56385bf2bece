package com.lc.billion.icefire.csabattle.biz.service;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.csabattle.biz.config.CrossServerAttackTrophyConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAServerTrophyDao;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerTrophy;
import com.lc.billion.icefire.game.biz.model.official.Official;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneCoverType;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.SceneCoverNode;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.officials.OfficialId;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.protocol.GcCSATrophyBuild;
import com.lc.billion.icefire.protocol.GcCSATrophyList;
import com.lc.billion.icefire.protocol.GcCSATrophyRetract;
import com.lc.billion.icefire.protocol.structure.PsCSATrophyListInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * @Author:CaoJian
 * @Date:2021/11/23 14:09
 */
@Service
public class CsaTrophyService {
    private static final int ERROR_CODE_SUCCESS = 1;
    private static final Logger logger = LoggerFactory.getLogger(CsaTrophyService.class);

    @Autowired
    private CSAServerTrophyDao serverTrophyDao;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ServiceDependency srvDep;
    @Autowired
    private CrossServerAttackServiceImpl crossServerAttackService;

    public void addServerTrophy(int serverId, String meta) {
        logger.info("[CsaTrophyService] | [跨服夺城获得奖杯] addServerTrophy [serverId:{}] [meta:{}]", serverId, meta);
    }

    public void placeServerTrophy(Role role, Long trophyId, int x, int y) {
        CSAServerTrophy trophy = serverTrophyDao.findById(trophyId);
        GcCSATrophyBuild gcCSATrophyBuild = new GcCSATrophyBuild();
        int errorCode = 0;
        Point oldPoint = null;
        PsCSATrophyListInfo trophyInfo = null;
        Point newPoint = Point.getInstance(x, y);
        SceneServiceImpl sceneService = srvDep.getSceneService();
        do {
            if (trophy == null) {
                errorCode = 2;
                break;
            }
            oldPoint = trophy.getPosition();
            if (trophy.getCurrentServerId() != role.getCurrentServerId()) {
                errorCode = 3;
                break;
            }
            if (crossServerAttackService.isCSACrossPlayer(role)) {
                errorCode = 7;
                break;
            }
            int serverId = role.getoServerId();
            Official official = srvDep.getOfficialsService().getRoleOfficial(role.getRoleId());
            if (official == null || !official.getMetaId().equals(OfficialId.KING)) {
                errorCode = 4;
                break;
            }
            CrossServerAttackTrophyConfig config = configService.getConfig(CrossServerAttackTrophyConfig.class);
            CrossServerAttackTrophyConfig.CrossSeverAttackTrophyMeta meta = config.getMeta(trophy.getMetaId());
            if (meta == null) {
                errorCode = 5;
                break;
            }
            Set<Point> pointSet = getCoverArea(newPoint, meta.getPlaceholders());
            pointSet.add(newPoint);
            boolean empty = true;
            for (Point point : pointSet) {
                SceneNode node = sceneService.getSceneNode(serverId, point);
                if (node != null) {
                    logger.info("[操作奖杯:{}] | role={} [老位置:{}] [奖杯ID:{}] [新位置{}不为空:{}]", oldPoint != null ? "移动" : "放置", role.getRoleId(), oldPoint, point, node);
                    empty = false;
                    continue;
                }
            }
            if (!empty) {
                errorCode = 6;
                break;
            }
            if (oldPoint != null) {
               remove(trophy);
            }
            trophy.setPosition(newPoint);
            sceneService.add(trophy, false);
            handleCover(serverId, trophyId, newPoint, meta.getPlaceholders());
            trophy.setPlaceTime(System.currentTimeMillis());
            errorCode = ERROR_CODE_SUCCESS;
            serverTrophyDao.save(trophy);
        } while (false);
        gcCSATrophyBuild.setErrCode(errorCode);
        if (trophyId != null) {
            gcCSATrophyBuild.setId(trophyId);
        }
        if (trophyInfo != null) {
            gcCSATrophyBuild.setTrophy(trophyInfo);
        }
        logger.info("[操作奖杯:{}] | role={} [老位置:{}] [位置:{},{}] [奖杯ID:{}] [结果:{}]", oldPoint != null ? "移动" : "放置", role.getRoleId(), oldPoint, x, y, trophyId, errorCode == ERROR_CODE_SUCCESS ? "成功" : "失败" + errorCode);
        role.send(gcCSATrophyBuild);
        if (oldPoint == null) {
            crossServerAttackService.bi_csaCrossBattlePlaceCup(role, trophy);
        } else {
            crossServerAttackService.bi_csaCrossBattleMoveCup(role, trophy);
        }
    }

    public void getCSATrophyList(Role role) {
        int oserverId = role.getoServerId();
        GcCSATrophyList trophyList = new GcCSATrophyList();
        for (CSAServerTrophy trophy : serverTrophyDao.findByCurrentServerId(oserverId)) {
            PsCSATrophyListInfo item = trophy.toPsCSATrophyListInfo();
            trophyList.addToTrophyInfo(item);
        }
        role.send(trophyList);
    }

    /**
     * @param centerPosition
     * @param placeholders
     * @return 获得联盟建筑除中心外其他占地坐标
     */
    public Set<Point> getCoverArea(Point centerPosition, int[][] placeholders) {
        Set<Point> ret = new HashSet<>();
        if (placeholders == null || placeholders.length <= 0) {
            return ret;
        }
        // 计算除中心位置其他占地坐标
        for (int[] placeholder : placeholders) {
            int x = placeholder[0];
            int y = placeholder[1];
            if (x == 0 && y == 0) {
                continue;
            }
            Point position = Point.getInstance(centerPosition.getX() + x, centerPosition.getY() + y);
            ret.add(position);
        }
        return ret;
    }

    public void startService() {
        CrossServerAttackTrophyConfig config = configService.getConfig(CrossServerAttackTrophyConfig.class);
        SceneServiceImpl sceneService = srvDep.getSceneService();
        for (CSAServerTrophy trophy : serverTrophyDao.findAll()) {
            Point point = trophy.getPosition();
            if (point == null) {
                continue;
            }
            CrossServerAttackTrophyConfig.CrossSeverAttackTrophyMeta meta = config.getMeta(trophy.getMetaId());
            if (meta == null) {
                continue;
            }
            int serverId = trophy.getoServerId();
            logger.info("[服务器启动{}] | 奖杯{}-{}放置到地图上{}", serverId, trophy.getId(), trophy.getMetaId(), trophy.getPosition());
            sceneService.add(trophy, true);
            handleCover(serverId, trophy.getId(), trophy.getPosition(), meta.getPlaceholders());
        }
    }

    private void handleCover(int serverId, Long id, Point centerPosition, int[][] placeholders) {
        if (centerPosition == null || placeholders == null) {
            return;
        }
        for (Point point : getCoverArea(centerPosition, placeholders)) {
            SceneCoverNode sceneCoverNode = new SceneCoverNode(SceneCoverType.TROPHY_COVER, id);
            sceneCoverNode.setCurrentServerId(serverId);
            sceneCoverNode.setoServerId(serverId);
            sceneCoverNode.setPosition(point);
            srvDep.getSceneService().add(sceneCoverNode, true);
        }
    }

    private void removeCover(int serverId, Long id, Point centerPosition, int[][] placeholders) {
        if (centerPosition == null || placeholders == null) {
            return;
        }
        SceneServiceImpl sceneService = srvDep.getSceneService();
        for (Point point : getCoverArea(centerPosition, placeholders)) {
            SceneNode sceneNode = sceneService.getSceneNode(serverId, point);
            if (sceneNode == null) {
                continue;
            }
            if (sceneNode.getNodeType() != SceneNodeType.SCENE_COVER) {
                continue;
            }
            SceneCoverNode coverNode = (SceneCoverNode) sceneNode;
            if (Objects.equals(coverNode.getOwnerId(), id)) {
                sceneService.remove(coverNode, false);
            }
        }
    }
    public void remove(Role role, Long trophyId) {
        GcCSATrophyRetract resp = new GcCSATrophyRetract();
        CSAServerTrophy trophy = serverTrophyDao.findById(trophyId);
        int errorCode = 0;
        Point oldPoint = null;
        int serverId = role.getoServerId();
        SceneServiceImpl sceneService = srvDep.getSceneService();
        CrossServerAttackTrophyConfig config = configService.getConfig(CrossServerAttackTrophyConfig.class);
        do {
            if (trophy == null) {
                errorCode = 2;
                break;
            }
            oldPoint = trophy.getPosition();
            if (trophy.getoServerId() != role.getoServerId()) {
                errorCode = 3;
                break;
            }
            Official official = srvDep.getOfficialsService().getRoleOfficial(role.getRoleId());
            if (official == null || !official.getMetaId().equals(OfficialId.KING)) {
                errorCode = 4;
                break;
            }
            CrossServerAttackTrophyConfig.CrossSeverAttackTrophyMeta meta = config.getMeta(trophy.getMetaId());
            if (meta == null) {
                errorCode = 5;
                break;
            }
            if (oldPoint == null) {
                errorCode = 6;
                break;
            }
            sceneService.remove(trophy, false);
            removeCover(serverId, trophyId, oldPoint, meta.getPlaceholders());
            trophy.setPlaceTime(0);
            errorCode = ERROR_CODE_SUCCESS;
            trophy.setPosition(null);
            serverTrophyDao.save(trophy);
        } while (false);
        resp.setErrCode(errorCode);
        if (trophyId != null) {
            resp.setId(trophyId);
        }
        logger.info("[收起奖杯] | [服务器:{}] role={} [老位置:{}] [奖杯ID:{}] [errorCode:{}]", serverId, role.getRoleId(), oldPoint, trophyId, errorCode);
        role.send(resp);
        crossServerAttackService.bi_csaCrossBattleRetrieveCup(role, trophy);
    }

    private void remove(CSAServerTrophy trophy) {
        if (trophy == null) {
            return;
        }
        Point point = trophy.getPosition();
        if (point == null) {
            return;
        }
        CrossServerAttackTrophyConfig config = configService.getConfig(CrossServerAttackTrophyConfig.class);
        CrossServerAttackTrophyConfig.CrossSeverAttackTrophyMeta meta = config.getMeta(trophy.getMetaId());
        if (meta == null) {
            return;
        }
        logger.info("[从地图上移除奖杯] | serverId={} tropyId:{} point={}", trophy.getoServerId(), trophy.getId(), point);
        srvDep.getSceneService().remove(trophy, true);
        removeCover(trophy.getoServerId(), trophy.getId(), point, meta.getPlaceholders());
        trophy.setPlaceTime(0);
        serverTrophyDao.save(trophy);
    }
}
