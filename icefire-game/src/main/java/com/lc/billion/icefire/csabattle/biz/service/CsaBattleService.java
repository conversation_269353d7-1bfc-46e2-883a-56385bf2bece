package com.lc.billion.icefire.csabattle.biz.service;

import com.google.api.client.util.ArrayMap;
import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csabattle.biz.async.CsaAllWorldRoleOperation;
import com.lc.billion.icefire.csabattle.biz.async.CsaAllianceRankRewardOperation;
import com.lc.billion.icefire.csabattle.biz.async.CsaRoleRankRewardOperation;
import com.lc.billion.icefire.csabattle.biz.async.CsaWorldCastleBroadcastOperation;
import com.lc.billion.icefire.csabattle.biz.config.CrossSeverAttackSetting;
import com.lc.billion.icefire.csabattle.biz.dao.RoleCsaBattleDao;
import com.lc.billion.icefire.csabattle.biz.model.battle.CrossWorldCastleSetoutType;
import com.lc.billion.icefire.csabattle.biz.model.battle.RoleCsaBattle;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityStatus;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAScoreType;
import com.lc.billion.icefire.csacontrol.biz.util.CSAUtils;
import com.lc.billion.icefire.game.biz.async.message.SceneUpdateToWatcherOperation;
import com.lc.billion.icefire.game.biz.battle.result.FightLostInfo;
import com.lc.billion.icefire.game.biz.battle.result.FightLostType;
import com.lc.billion.icefire.game.biz.config.SoldierConfig;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.WorldCastleNodeDao;
import com.lc.billion.icefire.game.biz.manager.AllianceMemberManager;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.manager.RoleServerInfoManager;
import com.lc.billion.icefire.game.biz.manager.csa.CSAGameDataVoManager;
import com.lc.billion.icefire.game.biz.model.activity.ArmyCampData;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerBattleInfo;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.email.RoleSimpleInfo;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.model.scene.node.WorldCastleNode;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackActivityStatusListener;
import com.lc.billion.icefire.game.biz.service.impl.csattack.ICrossServerAttackService;
import com.lc.billion.icefire.game.biz.service.impl.csattack.impl.CrossServerAttackServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.impl.CsaRoleBattlePointRank;
import com.lc.billion.icefire.game.biz.service.impl.scene.RegionGrid;
import com.lc.billion.icefire.game.biz.service.impl.scene.SceneServiceImpl;
import com.lc.billion.icefire.game.biz.service.rpc.GameRPCToCSAControlProxyService;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.structure.*;
import com.lc.billion.icefire.rpc.service.crossalliance.IGameRemoteCSAControlService;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class CsaBattleService implements ICrossServerAttackActivityStatusListener {
	private static final Logger logger = LoggerFactory.getLogger(CsaBattleService.class);

	@Autowired
	private ServiceDependency srvDep;
	@Autowired
	private ServiceDependency srvDpd;
	@Autowired
	private WorldCastleNodeDao worldCastleNodeDao;
	@Autowired
	private ArmyManager armyManager;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private RoleServerInfoManager roleServerInfoManager;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private RoleManager roleManager;
	@Autowired
	private SceneServiceImpl sceneService;
	@Autowired
	private MailService mailService;
	@Autowired
	private RoleCsaBattleDao roleCsaBattleDao;
	@Autowired
	private CsaMissionService csaMissionService;
	@Autowired
	private AllianceDao allianceDao;
	@Autowired
	private CsaRoleBattlePointRank csaRoleBattlePointRankHandler;
	@Autowired
	private AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private CrossServerAttackServiceImpl crossServerAttackService;
	@Autowired
	private CSAGameDataVoManager csaGameDataVoManager;
	@Autowired
	private GameRPCToCSAControlProxyService gameRPCToCSAControlProxyService;
	@Autowired
	private AllianceServiceImpl allianceService;
	@Autowired
	private RankDaoImpl rankDao;
	@Autowired
	private AllianceMemberManager allianceMemberManager;
	@Autowired
	private DropServiceImpl dropService;
	@Autowired
	private ArmyDao armyDao;

	@PostConstruct
	public void init() {
		logger.info("init");
	}

	public void startService() {
		logger.info("startService");
	}

	public void onEnterWorld(Role role) {
		logger.info("Role enter world. role = {}", role.getId());
		int serverId = role.getoServerId();

		// 推送跨服夺城buff
		pushCSABuff(role, serverId);

		// 活动结束后，玩家登录时的相关处理
		if (crossServerAttackService.isCsaActivityOver(serverId)) {
			this.onRoleActivityEnd(role);
			return;
		}

		if (!crossServerAttackService.isCSAServer(serverId)) {
			return;
		}

		// 当玩家进入游戏后，创建RoleCsaBattle实体
		RoleCsaBattle roleCsaBattle = this.getRoleCsaBattle(role);
		if (roleCsaBattle == null) {
			roleCsaBattle = this.createRoleCsaBattle(role);
		}

		// 如果当前服务器为目标服务器
		if (this.isCsaDefenceServer(serverId)) {
			// 是否进入过
			if (!roleCsaBattle.isEnterTargetServer()) {
				roleCsaBattle.setEnterTargetServer(true);
			}
		}

		GcCsaBattleScoreInfo gcCsaBattleScoreInfo = this.buildGcCsaBattleScoreInfo(serverId);
		role.send(gcCsaBattleScoreInfo);

		updateBattleBuff(role);
	}

	private void pushCSABuff(Role role, int serverId) {
		long buffEndTime = 0;
		CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
		CSAServerBattleInfo gameCsaServerBattleInfo = csaGameDataVoManager.getGameCsaServerBattleInfo(serverId);
		if (gameCsaServerBattleInfo != null && gameCsaServerBattleInfo.getBattleStartTime() != 0) {
			buffEndTime = gameCsaServerBattleInfo.getBattleStartTime() + crossSeverAttackSetting.getCsaBuffDuration() * TimeUtil.SECONDS_MILLIS;
		}

		if (buffEndTime > 0) {
			GcBuffUpdateInfo gcBuffUpdateInfo = new GcBuffUpdateInfo();
			gcBuffUpdateInfo.setBuffEndTime(buffEndTime);
			role.send(gcBuffUpdateInfo);
		}
	}

	public boolean isInFightStatus(int serverId) {
		CSAActivityStatus status = crossServerAttackService.getCurrCSAActivityStatus(serverId);
		return status == CSAActivityStatus.BATTLE_PREPARE_FIRST_HALF || status == CSAActivityStatus.BATTLE_FIRST_HALF || status == CSAActivityStatus.BATTLE_PREPARE_SECOND_HALF
				|| status == CSAActivityStatus.BATTLE_SECOND_HALF;
	}

	public List<WorldCastleNode> getZoneWorldCastleNode(RegionGrid grid) {
		Collection<WorldCastleNode> all = worldCastleNodeDao.findByCurrentServerId(grid.getServerId());
		List<WorldCastleNode> result = new ArrayList<>();
		all.forEach(node -> {
			if (node.getZoneIndex() == grid.getIndex()) {
				result.add(node);
			}
		});

		return result;
	}

	public void updateInCsaBattle(RegionGrid zoneGrid, long now) {
		if (!crossServerAttackService.isCanCSAFight(zoneGrid.getServerId())) {
			return;
		}

		CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
		Collection<WorldCastleNode> nodes = getZoneWorldCastleNode(zoneGrid);
		if (JavaUtils.bool(nodes)) {
			if (zoneGrid.getLastCSAOccupyTime() == 0 || now - zoneGrid.getLastCSAOccupyTime() >= 5 * TimeUtil.SECONDS_MILLIS) {
				for (WorldCastleNode node : nodes) {
					// 1:更新城市占领值
					updateWorldCastleOccupyValue(node, now);
				}
			}

			// 2:驻防加积分
			if (zoneGrid.getLastCSATickTime() == 0) {
				zoneGrid.setLastCSATickTime(now);
				return;
			}

			if (now - zoneGrid.getLastCSATickTime() >= crossSeverAttackSetting.getIndividualPointGarrison()[0] * TimeUtil.SECONDS_MILLIS) {
				for (WorldCastleNode node : nodes) {
					updateWorldCastleNode(node, now);
				}
				zoneGrid.setLastCSATickTime(now);
			}
		}
	}

	public void updateWorldCastleNode(WorldCastleNode worldCastleNode, long now) {
		CrossSeverAttackSetting csaSettingConfig = configService.getConfig(CrossSeverAttackSetting.class);
		if (csaSettingConfig.getIndividualPointGarrison().length < 2) {
			return;
		}

		int addScore = csaSettingConfig.getIndividualPointGarrison()[1];
		List<ArmyCampData> crossArmies = worldCastleNode.getCrossArmies();
		// 建筑里面的(Cross)军队玩家增加积分
		if (JavaUtils.bool(crossArmies)) {
			for (ArmyCampData armyCampData : crossArmies) {
				if (armyCampData.isArrive()) {
					Role role = roleManager.getRole(armyCampData.getRoleId());
					if (role != null) {
						addPoint(role, addScore);
					}
				}
			}
		}
	}

	/**
	 * 更新城市占领值
	 */
	public void updateWorldCastleOccupyValue(WorldCastleNode node, long now) {
		CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
		if (node.getLastAttackOccupyTime() == 0) {
			// 开始：无人驻扎、或者防守方驻扎无需计算城市占领值
			return;
		}
		int serverId = node.getCurrentServerId();
		boolean update = false;
		int oldBelongServerId = node.getCrossBelongServerId();
		int oldAttackOccupyValue = node.getAttackOccupyValue();

		logger.info("更新跨服夺城占领值-1 城市:{} 攻击方服：{} 防守方服:{} 旧归属服:{} 新归属服:{} 占领值：{} 占领值上限:{}", node.getMetaId(), crossServerAttackService.getAttackServerId(serverId),
				crossServerAttackService.getDefenceServerId(serverId), oldBelongServerId, node.getGarrisonServerId(), oldAttackOccupyValue, node.getOccupyLimit());

		if (node.getGarrisonServerId() == crossServerAttackService.getAttackServerId(serverId) && node.getAttackOccupyValue() < node.getOccupyLimit()) {
			update = true;
			// 当前是攻击方占领
			int interval = (int) ((now - node.getLastAttackOccupyTime()) * 1.0 / TimeUtil.SECONDS_MILLIS);
			int addValue = interval * crossSeverAttackSetting.getOccupationPointIncreaseInterval() * crossSeverAttackSetting.getOccupationPointIncreaseValue();
			int attackValue = Math.min(node.getAttackOccupyValue() + addValue, node.getOccupyLimit());
			node.setAttackOccupyValue(attackValue);

			// 上次更新时间需要一直更新
			node.setLastAttackOccupyTime(now);
			logger.info("更新跨服夺城占领值-2 城市:{} 占领值:{} 更新时间:{}", node.getMetaId(), node.getAttackOccupyValue(), node.getLastAttackOccupyTime());
		} else if ((node.getGarrisonServerId() == 0 && node.getAttackOccupyValue() > 0)
				|| (node.getGarrisonServerId() == crossServerAttackService.getDefenceServerId(serverId) && node.getAttackOccupyValue() > 0)) {
			update = true;
			// 当前是防守方占领
			int interval = (int) ((now - node.getLastAttackOccupyTime()) * 1.0 / TimeUtil.SECONDS_MILLIS);
			int subValue = interval * crossSeverAttackSetting.getOccupationPointIncreaseInterval() * crossSeverAttackSetting.getOccupationPointIncreaseValue();
			int attackValue = Math.max(node.getAttackOccupyValue() - subValue, 0);
			node.setAttackOccupyValue(attackValue);

			logger.info("更新跨服夺城占领值-3 城市:{} 现在:{} 上次:{} 间隔：{} 减少值:{} 占领值:{}", node.getMetaId(), now, node.getLastAttackOccupyTime(), interval, subValue, attackValue);

			// 攻击方的占领值为递减为0，则无需再计算攻击方占领值
			if (attackValue == 0) {
				node.setLastAttackOccupyTime(0);
			} else {
				// 上次更新时间需要一直更新
				node.setLastAttackOccupyTime(now);
			}
		}

		if (oldBelongServerId != node.getCrossBelongServerId()) {
			crossWorldCastleOwnerChange(node, oldBelongServerId, oldAttackOccupyValue);
		}

		// 落地
		worldCastleNodeDao.save(node);

		// aoi通知
		if (update)
			sceneService.update(node, null);
	}

	/**
	 * 热点城市逻辑
	 */
	public void hotSpotWorldCastle(WorldCastleNode worldCastleNode, int changeValue) {
		worldCastleNode.setSetoutCount(worldCastleNode.getSetoutCount() + changeValue);
		if (worldCastleNode.getSetoutCount() < 0) {
			worldCastleNode.setSetoutCount(0);
		}
	}

	/**
	 * 跨服夺城--城市占领值归属变化
	 */
	public void crossWorldCastleOwnerChange(WorldCastleNode worldCastleNode, int oldBelongServerId, int oldAttackOccupyValue) {
		logger.info("WorldCastleNode change owner. node = {}, belongServerId = {}", worldCastleNode.getPersistKey(), worldCastleNode.getCrossBelongServerId());
		int serverId = worldCastleNode.getCurrentServerId();
		int score = getWorldCastleNodeBattleScore(worldCastleNode);
		// 重新计算双方分数
		int atkScore = this.computeBattleScore(serverId);

		if (atkScore >= 0) {
			bi_csaCrossBattleScoreChange(worldCastleNode, score, atkScore);
		}
		// bi log

		broadcastCSAWorldCastleChangeOwner(serverId, worldCastleNode.getMetaId(), oldBelongServerId, worldCastleNode.getCrossBelongServerId());

	}

	private int getWorldCastleNodeBattleScore(WorldCastleNode node) {
		CrossSeverAttackSetting crossSeverAttackSetting = configService.getConfig(CrossSeverAttackSetting.class);
		return crossSeverAttackSetting.getWorldCastlePoint(node.getLevel());
	}

	/**
	 * 判断是否为同一个阵营
	 */
	public boolean isSameCamp(Role role, WorldCastleNode node, CrossWorldCastleSetoutType type) {
		int nodeGarrisonServerId = node.getGarrisonServerId();

		// 没有驻防
		if (nodeGarrisonServerId == 0) {
			if (type == CrossWorldCastleSetoutType.FIGHT) {
				return false;
			} else {
				return true;
			}
		} else {
			int roleOriginGameServerId = this.getRoleOriginGameServer(role);
			// 防守方的CsaHomeServerId=0，所以要获取一下防守方服务器id
			if (roleOriginGameServerId == 0) {
				roleOriginGameServerId = crossServerAttackService.getDefenceServerId(role.getoServerId());
			}

			return nodeGarrisonServerId == roleOriginGameServerId;
		}
	}

	/**
	 * 判断是否可侦查
	 */
	public boolean canRecon(Role role, WorldCastleNode node) {
		int nodeGarrisonServerId = node.getGarrisonServerId();

		// 0:没有驻防，可以侦查
		if (nodeGarrisonServerId == 0) {
			return true;
		} else {
			int roleOriginGameServerId = this.getRoleOriginGameServer(role);
			// 防守方的CsaHomeServerId=0，所以要获取一下防守方服务器id
			if (roleOriginGameServerId == 0) {
				roleOriginGameServerId = crossServerAttackService.getDefenceServerId(role.getCurrentServerId());
			}

			return nodeGarrisonServerId != roleOriginGameServerId;
		}
	}

	/**
	 * 判断是否为同一个服
	 */
	public boolean isSameServer(Role role, Role targetRole) {
		ICrossServerAttackService.CSA_ROLE_SIDE csaSide = crossServerAttackService.getRoleSide(role);
		ICrossServerAttackService.CSA_ROLE_SIDE targetSide = crossServerAttackService.getRoleSide(targetRole);
		return csaSide == targetSide;
	}

	/**
	 * 跨服夺城-援军最大队伍数
	 */
	public int getCrossWorldCastleStationMaxNum() {
		return configService.getConfig(CrossSeverAttackSetting.class).getCsaReinforcementMaxTeam();
	}

	/**
	 * 跨服夺城-援军每次出战最大队伍数
	 */
	public int getCrossWorldCastleFightTroopNum() {
		return configService.getConfig(CrossSeverAttackSetting.class).getCsaReinforcementBattleTeam();
	}

	public void returnStaminaAndNotice(ArmyInfo army) {
		Role role = army.getOwner();
		if (role != null && army.getUseStamina() > 0) {
			role.addCurrency(Currency.STAMINA, army.getUseStamina());
			mailService.sendSystemEmail(role, EmailConstants.WORLD_CASTLE_ARMY_STAMINA_RETURN);
		}
	}

	/**
	 * 跨服夺城：城市驻扎开始行军
	 */
	public boolean crossWorldCastleArmyStationStart(Long roleId, WorldCastleNode node, Long armyId) {
		if (node == null) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服夺城--城市驻扎开始行军 node is null",
					"roleId",roleId, "armyId",armyId);
			return false;
		}

		Role role = roleManager.getRole(roleId);
		if (role == null) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服夺城--城市驻扎开始行军 玩家为空", "roleId",roleId,
					"node",node.getPersistKey(), "nodeMeta",node.getMetaId(), "armyId",armyId);
			return false;
		}

		if (!JavaUtils.bool(role.getAllianceId()) || allianceDao.findById(role.getAllianceId()) == null) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服夺城--城市驻扎开始行军 联盟为空 ", "roleId",roleId,
					"allianceId",role.getAllianceId() == null ? "" : role.getAllianceId(),
					"node",node.getPersistKey(), "nodeMeta",node.getMetaId(), "armyId",armyId);
			return false;
		}

		// 判断是否可以驻扎
		if (!isSameCamp(role, node, CrossWorldCastleSetoutType.GARRISON)) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服夺城--城市驻扎开始行军 不是同一阵营", "roleId",roleId,
					"oServerId",getRoleOriginGameServer(role),
					"garrisonServerId",node.getGarrisonServerId(), "node",node.getPersistKey(),
					"nodeMeta",node.getMetaId(), "armyId",armyId);
			return false;
		}

		// 判断玩家在当前城市驻扎了几只部队
		List<ArmyCampData> armies = node.getCrossArmies();
		for (ArmyCampData armyCampData : armies) {
			if (armyCampData.getRoleId() == roleId.longValue()) {
				ErrorLogUtil.errorLog("CsaBattleService 跨服夺城--城市驻扎开始行军 一个城市只能驻扎一只部队",
						"roleId",roleId, "node",node.getPersistKey(), "nodeMeta",node.getMetaId());
				armyManager.returnArmy(armyManager.findById(armyId));
				return false;
			}
		}

		ArmyCampData armyCampData = new ArmyCampData();
		armyCampData.setArmyId(armyId);
		armyCampData.setJoinTime(0);
		armyCampData.setRoleId(roleId);
		armyCampData.setArrive(false);
		armyCampData.setAllianceId(role.getAllianceId());
		armies.add(armyCampData);
		worldCastleNodeDao.save(node);

		// aoi通知
		sceneService.update(node, null);

		broadcastCrossWorldCastleDetailInfoUpdate(node);

		logger.info("跨服夺城--玩家向城市驻扎行军开始:{},{},{},{}", roleId, node.getPersistKey(), node.getMetaId(), armyId);
		return true;
	}

	/**
	 * 跨服夺城：驻扎到达
	 */
	public boolean crossWorldCastleArmyStationArrive(Long roleId, WorldCastleNode node, Long armyId) {
		if (node == null) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服夺城驻扎到达 node is null",
					"roleId",roleId, "armyId",armyId);
			return false;
		}

		// 城市已驻扎到达人数是否达到上限
		ArmyInfo armyInfo = armyManager.findById(armyId);
		if (node.getRealStationCount() >= getCrossWorldCastleStationMaxNum()) {
			ErrorLogUtil.errorLog("跨服城市驻扎部队超量", "nodeMeta",node.getMetaId());
			armyManager.returnArmy(armyInfo);
			worldCastleNodeDao.save(node);
			return false;
		}

		List<ArmyCampData> armies = node.getCrossArmies();
		ArmyCampData roleArmyCamp = null;
		for (ArmyCampData armyCampData : armies)
			if (armyCampData.getArmyId().longValue() == armyId.longValue())
				roleArmyCamp = armyCampData;

		if (node.getRoleRealStationCount(roleId) > 0) {
			ErrorLogUtil.errorLog("CsaBattleService 跨服城市建筑驻扎到达 一个城市只能驻扎一只部队", "roleId",roleId,
					"node",node.getPersistKey(), "nodeMeta",node.getMetaId());
			armyManager.returnArmy(armyInfo);
			return false;
		}

		if (roleArmyCamp == null) {
			// 从其他类型行军转换而来。直接进驻的。比如战斗胜利，直接转换成驻防行军，进驻
			roleArmyCamp = new ArmyCampData();
			roleArmyCamp.setArmyId(armyId);
			roleArmyCamp.setJoinTime(0);
			roleArmyCamp.setRoleId(roleId);
			roleArmyCamp.setArrive(false);
			armies.add(roleArmyCamp);
		}

		fightFinish(roleManager.getRole(roleId), node, armyInfo, true);
		roleArmyCamp.setJoinTime(TimeUtil.getNow());
		roleArmyCamp.setArrive(true);
		worldCastleNodeDao.save(node);

		// aoi通知
		sceneService.update(node, null);
		broadcastCrossWorldCastleDetailInfoUpdate(node);
		logger.info("玩家向跨服城市驻扎行军到达{},{},{}", roleId, node.getMetaId(), armyId);
		return true;
	}

	/**
	 * 广播城市详细信息给联盟成员
	 */
	public void broadcastCrossWorldCastleDetailInfoUpdate(WorldCastleNode worldCastleNode) {
		int garrisonServerId = worldCastleNode.getGarrisonServerId();
		if (garrisonServerId <= 0) {
			return;
		}

		GcWorldCastleDetailInfo info = new GcWorldCastleDetailInfo();
		PsMapCastleInfo castleInfo = worldCastleNode.toPsMapCastleInfo();
		List<ArmyCampData> armies = worldCastleNode.getCrossArmies();
		if (JavaUtils.bool(armies)) {
			List<Long> dirtyArmyIds = new ArrayList<>();
			for (ArmyCampData armyCampData : armies) {
				ArmyInfo armyInfo = armyManager.findById(armyCampData.getArmyId());
				if (armyInfo == null) {
					ErrorLogUtil.errorLog("跨服城市的驻扎行军出错", "worldCastleNode",worldCastleNode.getPersistKey(),
							"armyId",armyCampData.getArmyId());
					dirtyArmyIds.add(armyCampData.getArmyId());
					continue;
				}
				castleInfo.addToStationArmys(toPsCrossWorldCastleArmyInfo(worldCastleNode.getPersistKey(), armyCampData));
			}

			// 修复脏数据
			if (JavaUtils.bool(dirtyArmyIds)) {
				ErrorLogUtil.errorLog("跨服城市驻扎 脏数据修复", "dirtyArmyId",dirtyArmyIds);
				Iterator<ArmyCampData> iterator = worldCastleNode.getCrossArmies().iterator();
				while (iterator.hasNext()) {
					ArmyCampData next = iterator.next();
					if (dirtyArmyIds.contains(next.getArmyId()))
						iterator.remove();
				}
				worldCastleNodeDao.save(worldCastleNode);
			}
		}

		castleInfo.setTroopMax(getCrossWorldCastleStationMaxNum());
		castleInfo.setFightTroopMax(getCrossWorldCastleFightTroopNum());
		info.setInfo(castleInfo);

		// 广播给驻防的人
		broadcast(worldCastleNode.getGarrisonServerId(), worldCastleNode, info);
	}

	private PsWorldCastleArmyInfo toPsCrossWorldCastleArmyInfo(Long worldCastleId, ArmyCampData armyCampData) {
		PsWorldCastleArmyInfo psArmy = new PsWorldCastleArmyInfo();
		psArmy.setDetailInfo(new PsStationArmyUnit());

		long roleId = armyCampData.getRoleId();
		Role role = roleManager.getRole(roleId);
		RoleSimpleInfo roleSimpleInfo = new RoleSimpleInfo(role, null);
		psArmy.getDetailInfo().setRoleInfo(roleSimpleInfo.toPsSimpleInfo());

		ArmyInfo armyInfo = armyManager.findById(armyCampData.getArmyId());
		if (armyInfo == null) {
			ErrorLogUtil.errorLog("跨服城市的驻扎行军出错", "worldCastleId",worldCastleId, "armyId",armyCampData.getArmyId());
		} else {
			psArmy.setStartTime(armyInfo.getStartWorkTime());
			psArmy.setTotalTime(armyInfo.getTotalWorkTime());
			psArmy.getDetailInfo().setArmyInfo(new PsArmySimpleInfo());
			psArmy.getDetailInfo().getArmyInfo().setSoldiers(armyInfo.getSoldierMap());
		}
		psArmy.setArrived(armyCampData.isArrive());
		psArmy.setArriveTime(armyCampData.getJoinTime());
		psArmy.setArmyId(armyCampData.getArmyId());

		return psArmy;
	}

	public void broadcast(int garrisonServerId, WorldCastleNode node, GcWorldCastleDetailInfo detailInfo) {
		// 异步处理
		asyncOperationService.execute(new SceneUpdateToWatcherOperation(srvDep.getSceneService(), node.getPosition(), detailInfo, node.getWatchers()));
	}

	/**
	 * 判断是否可以驻防
	 */
	public boolean canGarrisonCrossWorldCastle(Role role, WorldCastleNode castleNode) {
		int serverId = role.getCurrentServerId();
		if (!crossServerAttackService.isCSAEnable() || !crossServerAttackService.isCSADefendServer(serverId)) {
			return false;
		}

		// 准备阶段，只有防守方才能驻防
		if (crossServerAttackService.isCanCSAPrepare(serverId)) {
			// 跨服过来的玩家，都是攻击方，在活动准备阶段不可以驻防
			if (crossServerAttackService.isCSACrossPlayer(role)) {
				return false;
			} else {
				return true;
			}
		}

		// 战斗阶段
		if (crossServerAttackService.isCanCSAFight(serverId))
			return isSameCamp(role, castleNode, CrossWorldCastleSetoutType.GARRISON);
		return false;
	}

	/**
	 * 战斗结束
	 */
	public void fightFinish(Role role, WorldCastleNode node, ArmyInfo armyInfo, boolean isWin) {
		if (node == null) {
			ErrorLogUtil.errorLog("跨服夺城 fightFinish node is null", "roleId",role.getId());
			return;
		}

		// 添加积分
		addFighterKillPower(node, armyInfo);

		int oldGarrisonServerId = node.getGarrisonServerId();
		if (isWin) {
			// 易主：此刻，胜利方的行军还未真正设置为到达
			if (node.getRealStationCount() == 0) {
				long now = TimeUtil.getNow();
				// 切记：先更新城市占领值，用旧归属
				// 当攻击方第一次占领：则不需要调用
				updateWorldCastleOccupyValue(node, now);

				int winServerId = getRoleOriginGameServer(role);
				winServerId = winServerId == 0 ? role.getCurrentServerId() : winServerId;
				node.setGarrisonServerId(winServerId);

				// 设置上次更新时间
				node.setLastAttackOccupyTime(now);

				// 落地
				worldCastleNodeDao.save(node);

				// aoi通知
				sceneService.update(node, null);

				// bi log
				bi_csaCrossBattleOccupyChange(role, node);
			}
		}

		logger.info("CsaBattleService fightFinish Role:{} node:{} nodeMeta:{} 结果:{} 驻扎旧服Id：{} 驻扎服Id：{}", role.getId(), node.getPersistKey(), node.getMetaId(), isWin ? 1 : 0,
				oldGarrisonServerId, node.getGarrisonServerId());
	}

	/**
	 * 添加杀兵积分
	 */
	public void addFighterKillPower(WorldCastleNode node, ArmyInfo armyInfo) {
		var fightResult = armyInfo.getFightContext().getFightResult();
		addFighterKillPower(fightResult.getAttackerLostInfo());
		addFighterKillPower(fightResult.getDefenderLostInfo());
	}

	public void addFighterKillPower(FightLostInfo lostInfo) {
		SoldierConfig soldierConfig = configService.getConfig(SoldierConfig.class);
		CrossSeverAttackSetting csaSettingConfig = configService.getConfig(CrossSeverAttackSetting.class);
		var lostIds = lostInfo.getLostMap().rowKeySet();
		for (var lostId : lostIds) {
			Role role = roleManager.getRole(Long.parseLong(lostId));
			if (role == null) {
				ErrorLogUtil.errorLog("跨服夺城--玩家加积分 玩家为空", "lostId",lostId);
				continue;
			}

			Map<Integer, Integer> roleKillMap = new ArrayMap<>();
			var kv = lostInfo.getValueDetail(lostId, FightLostType.KILL);
			for (var entry : kv.entrySet()) {
				SoldierConfig.SoldierMeta soldierMeta = soldierConfig.get(entry.getKey());
				if (soldierMeta != null) {
					roleKillMap.compute(soldierMeta.getEra(), (k, v) -> v == null ? entry.getValue() : v + entry.getValue());
				}
			}

			int addScore = 0;
			for (Integer soldierLevel : roleKillMap.keySet()) {
				addScore += csaSettingConfig.getIndividualPointKillSoldierPoint(soldierLevel) * roleKillMap.get(soldierLevel);
			}

			// 给玩家加积分
			addPoint(role, addScore);
		}
	}

	/**
	 * 广播给所有在线的人：跨服夺城分数归属变化
	 */
	public void broadcastCSAWorldCastleChangeOwner(int serverId, String metaId, int oldBelongServerId, int newBelongServerId) {
		GcCSAWorldCastleChangeOwner msg = new GcCSAWorldCastleChangeOwner();
		msg.setMetaId(metaId);
		msg.setOldBelongServerId(oldBelongServerId);
		msg.setNewBelongServerId(newBelongServerId);

		asyncOperationService.execute(new CsaWorldCastleBroadcastOperation(serverId, srvDpd, msg));
	}

	/**
	 * 更新角色属性Buff
	 *
	 * @param role
	 *            角色
	 */
	public void updateBattleBuff(Role role) {
		logger.info("Server{} updateBattleBuff. Role = {}", role.getCurrentServerId(), role.getId());
		int roleOriginGameServer = getRoleOriginGameServer(role);
		pushCSABuff(role, roleOriginGameServer);
	}

	/**
	 * 战前处理
	 */
	private void beforeBattle(int serverId) {

		logger.info("Server{} before CSA Battle. isCsaDefenceServer = {}", serverId, this.isCsaDefenceServer(serverId));
		// 如果当前不是防守服，跳过
		if (!this.isCsaDefenceServer(serverId)) {
			return;
		}

		Collection<WorldCastleNode> castleNodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(castleNodes)) {
			for (WorldCastleNode node : castleNodes) {
				// 清理节点
				cleanWorldCastleNodeBeforeBattle(node);
			}
			logger.info("跨服争夺--活动开始： 清理服{} 所有城市信息", serverId);
		}

		// 异步更新一下服务器所有玩家的属性（防守方的），进攻方会在登录战斗服务器后计算
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::updateBattleBuff, true, "beforeBattle updateBattleBuff", serverId);
		asyncOperationService.execute(operation);

		// 计算双方得分
		this.computeBattleScore(serverId);

		logger.info("Server{} before CSA Battle.", serverId);
	}

	/**
	 * 清理城市节点
	 *
	 * @param node
	 */
	private void cleanWorldCastleNodeBeforeBattle(WorldCastleNode node) {
		// 召回当前建筑里面的(Local)军队
		var armyQueue = armyDao.getArmysByTargetPoint(node.getCurrentServerId(), node.getPosition());
		if (JavaUtils.bool(armyQueue)) {
			logger.info("Server{} cleanWorldCastleNodeBeforeBattle node={} localArmies={}", node.getCurrentServerId(), node.getMetaId(), armyQueue.size());
			for (var army : armyQueue) {
				armyManager.returnArmy(army);
			}
		}

		// 设置攻击方的占领值为0
		node.setAttackOccupyValue(0);

		// 设置驻防的服务器Id
		node.setGarrisonServerId(node.getCurrentServerId());

		// 设置上次计算攻击方占领值时间
		node.setLastAttackOccupyTime(0);

		worldCastleNodeDao.save(node);

		// aoi通知
		sceneService.update(node, null);
	}

	private void cleanWorldCastleNodeAfterBattle(WorldCastleNode node) {

		// 召回当前建筑里面的(Cross)军队
		List<ArmyCampData> crossArmies = node.getCrossArmies();

		if (JavaUtils.bool(crossArmies)) {

			logger.info("Server{} cleanWorldCastleNodeBeforeBattle node={} crossArmies={}", node.getCurrentServerId(), node.getMetaId(), crossArmies.size());

			Object[] armyCampDataArray = crossArmies.toArray();
			crossArmies.clear();

			for (Object obj : armyCampDataArray) {
				ArmyCampData armyCampData = (ArmyCampData) obj;
				ArmyInfo campArmy = armyManager.findById(armyCampData.getArmyId());
				if (campArmy != null) {
					RoleServerInfo roleServerInfo = roleServerInfoManager.findRoleServerInfo(campArmy.getRoleId());
					int csaHomeServerId = roleServerInfo.getCsaHomeServerId();
					if (csaHomeServerId == 0) {
						armyManager.returnArmy(campArmy);
						logger.info("Server{} cleanWorldCastleNodeAfterBattle csaHomeServerId={}, returnArmy={}", node.getCurrentServerId(), csaHomeServerId,
								campArmy.getPersistKey());
					} else {
						// 如果非本服玩家，部队立即返回
						armyManager.returnArmyImmediately(campArmy);
						logger.info("Server{} cleanWorldCastleNodeAfterBattle csaHomeServerId={}, returnArmyImmediately={}", node.getCurrentServerId(), csaHomeServerId,
								campArmy.getPersistKey());
					}
				} else {
					ErrorLogUtil.errorLog("cleanWorldCastleNodeAfterBattle armyManager can't find army",
							"curServerId",node.getCurrentServerId(), "armyId",armyCampData.getArmyId());
				}
			}

		}

		// 设置攻击方的占领值为0
		node.setAttackOccupyValue(0);

		// 设置驻防的服务器Id
		node.setGarrisonServerId(node.getCurrentServerId());

		// 设置上次计算攻击方占领值时间
		node.setLastAttackOccupyTime(0);

		worldCastleNodeDao.save(node);

		// aoi通知
		sceneService.update(node, null);
	}

	/**
	 * 计算服务器分数
	 */
	private int computeBattleScore(int serverId) {

		logger.info("Compute BattleScore. CurrentServer = {}, isCsaDefenceServer = {}", serverId, this.isCsaDefenceServer(serverId));
		// 如果当前不是防守服 或者 处于不可计算服务器分数的状态
		if (!this.isCsaDefenceServer(serverId) || !crossServerAttackService.isCanUpdateCSAServerScore()) {
			return -1;
		}

		CSAActivityStatus status = crossServerAttackService.getCurrCSAActivityStatus(serverId);

		logger.info("Compute BattleScore. CurrentServer = {}, status = {}", serverId, status);

		// 以下阶段不能计算分数
		if (status == CSAActivityStatus.WARMUP || status == CSAActivityStatus.CHOOSE_TIME || status == CSAActivityStatus.BATTLE_NOTICE || status == CSAActivityStatus.BATTLE_REST
				|| status == CSAActivityStatus.OVER) {
			return -1;
		}

		// 判断上下半场
		CSAScoreType csaScoreType = CSAScoreType.FIRST_HALF_SCORE;
		if (status == CSAActivityStatus.BATTLE_PREPARE_SECOND_HALF || status == CSAActivityStatus.BATTLE_SECOND_HALF) {
			csaScoreType = CSAScoreType.SECOND_HALF_SCORE;
		}

		int atkServerId = crossServerAttackService.getAttackServerId(serverId);
		int defServerId = crossServerAttackService.getDefenceServerId(serverId);
		int atkScore = 0;
		// int defScore = 0;

		Collection<WorldCastleNode> castleNodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(castleNodes)) {
			for (WorldCastleNode node : castleNodes) {
				int score = this.getWorldCastleNodeBattleScore(node);
				if (node.getCrossBelongServerId() == atkServerId) {
					atkScore += score;
				}
			}
		}

		// 上传分数到Control服
		IGameRemoteCSAControlService controlServiceProxy = this.getControlServiceProxy();
		if (controlServiceProxy == null) {
			ErrorLogUtil.errorLog("game to CSA中控服连接失败", "serverId",serverId);
			return atkScore;
		}

		if (crossServerAttackService.isCanUpdateCSAServerScore()) {
			controlServiceProxy.uploadStageScoreToCSAControl(atkServerId, atkScore, csaScoreType);
			logger.info("Compute BattleScore. CurrentServer = {}, csaScoreType = {}, atk = {} {}. def = {} {}", serverId, csaScoreType, atkServerId, atkScore, defServerId, 0);
		}
		return atkScore;
	}

	private IGameRemoteCSAControlService getControlServiceProxy() {
		return gameRPCToCSAControlProxyService.getGameRemoteCSAControlService();
	}

	/**
	 * 战后处理
	 */
	private void afterBattle(int serverId) {

		logger.info("Server{} after CSA Battle. isCsaDefenceServer = {}", serverId, this.isCsaDefenceServer(serverId));

		// 如果当前不是防守服，跳过
		if (!this.isCsaDefenceServer(serverId)) {
			return;
		}

		// 更新一下服务器所有玩家的属性（这里先用同步调用）
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::updateBattleBuff, true, "afterBattle updateBattleBuff", serverId);
		asyncOperationService.execute(operation);

		this.computeBattleScore(serverId);

		Collection<WorldCastleNode> castleNodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(castleNodes)) {
			for (WorldCastleNode node : castleNodes) {
				// 清理节点
				cleanWorldCastleNodeAfterBattle(node);
			}
		}

	}

	public int getRoleCsaHomeServerId(Role role) {
		RoleServerInfo roleServerInfo = roleServerInfoManager.findRoleServerInfo(role.getId());
		if (roleServerInfo == null) {
			ErrorLogUtil.errorLog("跨服夺城 RoleServerInfo is null", "roleId",role.getId());
			return 0;
		}
		return roleServerInfo.getCsaHomeServerId();
	}

	public int getRoleOriginGameServer(Role role) {
		RoleServerInfo roleServerInfo = roleServerInfoManager.findRoleServerInfo(role.getId());
		if (roleServerInfo == null) {
			ErrorLogUtil.errorLog("CSA get RoleServerInfo is null", "roleId",role.getId());
			return 0;
		}

		int serverId;
		if (roleServerInfo.getCsaHomeServerId() <= 0) {
			serverId = role.getoServerId();
		} else {
			int againstServerId = roleServerInfo.getoServerId();
			CSAActivityVo csaActivity = csaGameDataVoManager.getCsaActivity();
			if (csaActivity == null) {
				ErrorLogUtil.errorLog("CSA get csa activity is null", "roleId",role.getId());
				return 0;
			}

			CSAActivityContext activityContext = csaActivity.getActivityContext();
			CSAActivityGroupContext againstCSAActivityContest = activityContext.getServerInfoMap().get(againstServerId);
			if (againstCSAActivityContest == null) {
				ErrorLogUtil.errorLog("CSA get csa activity context is null", "roleId",role.getId(),
						"againstServerId",againstServerId);
				return 0;
			}

			serverId = againstCSAActivityContest.getAgainstServerId();
		}

		return serverId;
	}

	public int getWorldCastleNodeCsaOccupyValue(WorldCastleNode worldCastleNode) {
		return worldCastleNode.getAttackOccupyValue();
	}

	/**
	 * 增加个人积分
	 *
	 * @param role
	 *            角色
	 * @param addPoint
	 *            增加分数
	 */
	public void addPoint(Role role, int addPoint) {

		if (addPoint <= 0) {
			return;
		}

		// 当前服不是目标服
		if (!this.isCsaDefenceServer(role.getCurrentServerId())) {
			return;
		}

		RoleCsaBattle roleCsaBattle = this.getRoleCsaBattle(role);
		if (roleCsaBattle == null) {
			ErrorLogUtil.errorLog("Can't find RoleCsaBattle", "roleId",role.getId());
			return;
		}

		long newPoint = roleCsaBattle.getBattlePoint() + addPoint;
		roleCsaBattle.setBattlePoint(newPoint);
		this.saveRoleCsaBattle(roleCsaBattle);

		// 更新给客户端
		GcCsaRoleBattleInfo gcCsaRoleBattleInfo = new GcCsaRoleBattleInfo();
		gcCsaRoleBattleInfo.setBattlePoint(roleCsaBattle.getBattlePoint());
		role.send(gcCsaRoleBattleInfo);

		// 更新排行
		csaRoleBattlePointRankHandler.updateRoleRankBattlePoint(role, newPoint, addPoint);

		// 触发任务更新
		csaMissionService.onRoleCsaPointChanged(role, newPoint);

		// 添加参与玩家
		int server = getRoleOriginGameServer(role);
		crossServerAttackService.getAttendRole().putIfAbsent(server, new ArrayList<>());
		List<Long> attendRoleList = crossServerAttackService.getAttendRole().get(server);
		if (!attendRoleList.contains(role.getRoleId())) {
			attendRoleList.add(role.getRoleId());
		}

		logger.info("Role add CSA point. role = {}, nowPoint = {}, add = {}", role.getRoleId(), newPoint, addPoint);
	}

	/**
	 * 清空玩家活动战斗数据，包括积分和排行榜
	 *
	 * @param role
	 *            角色
	 */
	public void onRoleActivityEnd(Role role) {

		RoleCsaBattle roleCsaBattle = this.getRoleCsaBattle(role);
		if (roleCsaBattle == null) {
			ErrorLogUtil.errorLog("Can't find RoleCsaBattle", "roleId",role.getId());
			return;
		}

		roleCsaBattle.clear();
		this.saveRoleCsaBattle(roleCsaBattle);

		// 更新给客户端
		GcCsaRoleBattleInfo gcCsaRoleBattleInfo = new GcCsaRoleBattleInfo();
		gcCsaRoleBattleInfo.setBattlePoint(roleCsaBattle.getBattlePoint());
		role.send(gcCsaRoleBattleInfo);

		// 更新角色buff
		updateBattleBuff(role);

		// 从排行榜移除角色
		// csaRoleBattlePointRankHandler.removeRoleRank(role);
	}

	/**
	 * 获取RoleCsaBattle
	 *
	 * @param role
	 *            角色
	 * @return 角色CSA战斗实体
	 */
	public RoleCsaBattle getRoleCsaBattle(Role role) {
		return roleCsaBattleDao.findById(role.getId());
	}

	/**
	 * 创建RoleCsaBattle
	 *
	 * @param role
	 *            角色
	 * @return 角色CSA战斗实体
	 */
	public RoleCsaBattle createRoleCsaBattle(Role role) {
		return roleCsaBattleDao.create(role);
	}

	/**
	 * 保存RoleCsaBattle
	 *
	 * @param roleCsaBattle
	 *            角色CSA战斗实体
	 */
	public void saveRoleCsaBattle(RoleCsaBattle roleCsaBattle) {
		roleCsaBattleDao.save(roleCsaBattle);
	}

	/**
	 * 获取角色CSA活动个人积分
	 *
	 * @param role
	 *            角色
	 * @return 个人积分
	 */
	public long getRoleCsaBattlePoint(Role role) {
		RoleCsaBattle roleCsaBattle = this.getRoleCsaBattle(role);
		if (roleCsaBattle == null) {
			// logger.error("Can't find RoleCsaBattle. role = {}", role.getId());
			return 0;
		}
		return roleCsaBattle.getBattlePoint();
	}

	/**
	 * 当前服务器是否为防守服务器
	 *
	 * @return 是否目标服务器
	 */
	private boolean isCsaDefenceServer(int serverId) {
		return crossServerAttackService.isCSADefendServer(serverId);
	}

	/**
	 * 获取CSA活动状态
	 *
	 * @return 活动状态
	 */
	private CSAActivityStatus getCSAActivityStatus(int serverId) {
		return crossServerAttackService.getCurrCSAActivityStatus(serverId);
	}

	/**
	 * 跨服抢城延迟城市争夺战状态
	 *
	 * @param serverId
	 * @param delayedMillSeconds
	 */
	public void csaDelay(int serverId, long delayedMillSeconds) {
		logger.info("[CSA跨服抢城开始] | 服务器{} 联盟城市争夺推迟毫秒数{}", serverId, delayedMillSeconds);
		Collection<WorldCastleNode> castleNodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(castleNodes)) {
			castleNodes.forEach(node -> csaDelay(node, delayedMillSeconds));
		}
	}

	/**
	 * 根据相应的逻辑 延长联盟城市的状态时间
	 *
	 * @param node
	 * @param delayedMillSeconds
	 */
	private void csaDelay(WorldCastleNode node, long delayedMillSeconds) {
		long now = System.currentTimeMillis();
		if (node.getNpcResumeTime() >= now && (node.getNpcResumeTime() - now) <= delayedMillSeconds) {
			node.setNpcResumeTime(node.getNpcResumeTime() + delayedMillSeconds);
			logger.info("[CSA跨服抢城开始] | 城市{} npcResumeTime延长", node);
		}
		if (node.getPeaceTime() >= now && (node.getPeaceTime() - now) <= delayedMillSeconds) {
			node.setPeaceTime(node.getPeaceTime() + delayedMillSeconds);
			logger.info("[CSA跨服抢城开始] | 城市{} 和平期延长", node);
		}
		if (node.getOpenPublicTime() >= now && node.getOpenPublicTime() - now <= delayedMillSeconds) {
			logger.info("[CSA跨服抢城开始] | 城市{} PVP开放时间延长", node);
			node.setOpenPublicTime(node.getOpenPublicTime() + delayedMillSeconds);
		}
		if (node.getTiredEndTime() >= now && node.getTiredEndTime() - now <= delayedMillSeconds) {
			logger.info("[CSA跨服抢城开始] | 城市{} tiredEndTime时间延长", node);
			node.setTiredEndTime(node.getTiredEndTime() + delayedMillSeconds);
		}
		if (node.getTiredEndTime2() >= now && node.getTiredEndTime2() - now <= delayedMillSeconds) {
			logger.info("[CSA跨服抢城开始] | 城市{} tiredEndTime2时间延长", node);
			node.setTiredEndTime2(node.getTiredEndTime2() + delayedMillSeconds);
		}
		if (node.getEndTime() >= now && node.getEndTime() - now <= delayedMillSeconds) {
			logger.info("[CSA跨服抢城开始] | 城市{} endTime 时间延长", node);
			node.setEndTime(node.getEndTime() + delayedMillSeconds);
		}
	}

	/**
	 * 活动上半场开始
	 */
	@Override
	public void csaActivityFirstHalfStart(CSAActivityGroupContext ctx) {
		logger.info("Server{} csa activity first half start. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));
		this.beforeBattle(ctx.getSelfServerId());
		csaDelay(ctx.getSelfServerId(), CSAUtils.getTotalBattleTime());

		// 异步更新一下服务器所有玩家的属性
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::updateBattleBuff, false, "csaActivityFirstHalfStart updateBattleBuff",
				ctx.getSelfServerId());
		asyncOperationService.execute(operation);
	}

	@Override
	public void csaActivityFirstHalfBattleStart(CSAActivityGroupContext ctx) {
		// 异步更新一下服务器所有玩家的属性
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::updateBattleBuff, false, "csaActivityFirstHalfBattleStart updateBattleBuff",
				ctx.getSelfServerId());
		asyncOperationService.execute(operation);
	}

	/**
	 * 活动上半场结束
	 */
	@Override
	public void csaActivityFirstHalfBattleEnd(CSAActivityGroupContext ctx) {
		logger.info("Server{} csaActivityFirstHalfEnd. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));
		this.afterBattle(ctx.getSelfServerId());

		// bi log
		this.bi_csaCrossBattleHalfResult(ctx.getSelfServerId());
		this.bi_csaCrossBattleHalfPersonalScore(ctx.getSelfServerId());
	}

	/**
	 * 活动下半场开始
	 */
	@Override
	public void csaActivitySecondHalfStart(CSAActivityGroupContext ctx) {
		logger.info("Server{} csaActivitySecondHalfStart. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));
		this.beforeBattle(ctx.getSelfServerId());
	}

	/**
	 * 活动下半场结束
	 */
	@Override
	public void csaActivitySecondHalfBattleEnd(CSAActivityGroupContext ctx) {
		logger.info("Server{} csaActivitySecondHalfEnd. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));
		this.afterBattle(ctx.getSelfServerId());
	}

	/**
	 * 活动结算开始
	 */
	@Override
	public void csaActivitySettleStart(CSAActivityGroupContext ctx) {

		logger.info("Server{} csaActivitySettleStart. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));

		// 发排行榜奖励
		CSAActivityVo csaActivityVo = csaGameDataVoManager.getCsaActivity();
		CsaRoleRankRewardOperation csaRoleRankRewardOperation = new CsaRoleRankRewardOperation(ctx.getSelfServerId(), csaActivityVo.getMetaId());
		// csaRoleRankRewardOperation.exec();
		asyncOperationService.execute(csaRoleRankRewardOperation);

		// bi log
		this.bi_csaCrossBattleFinalResult(ctx);
		this.bi_csaCrossBattleFinalPersonalScore(ctx.getSelfServerId());
		if (ctx.isActivityWin()) {
			CSAServerBattleInfo csaServerBattleInfo = csaGameDataVoManager.getCsaServerBattleInfoMap().get(ctx.getSelfServerId());
			asyncOperationService
					.execute(new CsaAllianceRankRewardOperation(ctx, csaServerBattleInfo, allianceService, rankDao, allianceMemberManager, dropService, crossServerAttackService));
			logger.info("Server{} csaActivitySettleStart win sendCsaAllianceRankRewardOperation", ctx.getSelfServerId());
		}
	}

	public int getBattleScoreByStatus(CSAActivityStatus status, CSAActivityGroupContext context) {
		if (context == null) {
			return 0;
		}

		int score = 0;
		switch (status) {
		case WARMUP:
		case CHOOSE_TIME:
		case BATTLE_NOTICE:
		default:
			score = 0;
			break;

		case BATTLE_PREPARE_FIRST_HALF:
		case BATTLE_FIRST_HALF:
		case BATTLE_FIRST_SETTLEMENT:
			score = context.getServerScore();
			break;

		case BATTLE_REST:
		case OVER:
			score = context.getServerScore();
			break;

		case BATTLE_PREPARE_SECOND_HALF:
		case BATTLE_SECOND_HALF:
		case BATTLE_SECOND_SETTLEMENT:
			score = context.getServerScore();
			break;

		}
		return score;
	}

	public GcCsaBattleScoreInfo buildGcCsaBattleScoreInfo(int serverId) {
		int atkServerId = crossServerAttackService.getClientAttackServerId(serverId);
		int defServerId = crossServerAttackService.getClientDefenceServerId(serverId);
		CSAActivityGroupContext atkServerContext = crossServerAttackService.getCsaActivityGroupContext(atkServerId);
		CSAActivityGroupContext defServerContext = crossServerAttackService.getCsaActivityGroupContext(defServerId);

		logger.info("GcCsaBattleScoreInfo atkServerId = {}, atkServerContext = {}", atkServerId, atkServerContext);
		logger.info("GcCsaBattleScoreInfo defServerId = {}, defServerContext = {}", defServerId, defServerContext);

		CSAActivityStatus status = crossServerAttackService.getCurrCSAActivityStatus(serverId);

		int atkScore = this.getBattleScoreByStatus(status, atkServerContext);
		int defScore = this.getBattleScoreByStatus(status, defServerContext);

		// 同步战斗双方分数消息
		GcCsaBattleScoreInfo gcCsaBattleScoreInfo = new GcCsaBattleScoreInfo();
		gcCsaBattleScoreInfo.setAttackServerId(atkServerId);
		gcCsaBattleScoreInfo.setAttackScore(atkScore);
		gcCsaBattleScoreInfo.setDefenceServerId(defServerId);
		gcCsaBattleScoreInfo.setDefenceScore(defScore);

		logger.info("GcCsaBattleScoreInfo status={} atk={} {}, def={} {}", status, atkServerId, atkScore, defServerId, defScore);

		return gcCsaBattleScoreInfo;
	}

	/**
	 * 活动结束
	 */
	@Override
	public void csaActivityEnd(CSAActivityGroupContext ctx) {

		logger.info("Server{} csaActivityEnd. status={}", ctx.getSelfServerId(), this.getCSAActivityStatus(ctx.getSelfServerId()));

		// 清空玩家活动战斗数据，包括积分和排行榜
		CsaAllWorldRoleOperation operation = new CsaAllWorldRoleOperation(srvDep, this::onRoleActivityEnd, false, "csaActivityEnd onRoleActivityEnd", ctx.getSelfServerId());
		asyncOperationService.execute(operation);

	}

	public void debug_csaAddRolePoint(Role role, int addPoint) {
		this.addPoint(role, addPoint);
	}

	public void debug_csaRandCityOccupyValue(int serverId) {
		long now = TimeUtil.getNow();
		Integer[] rand = new Integer[2];
		rand[0] = crossServerAttackService.getAttackServerId(serverId);
		rand[1] = crossServerAttackService.getDefenceServerId(serverId);

		Collection<WorldCastleNode> castleNodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(castleNodes)) {
			for (WorldCastleNode node : castleNodes) {
				int occupyLimit = node.getOccupyLimit();
				int randServerId = RandomUtils.randomFromArray(rand);
				int value = RandomUtils.randomIntValue(10, occupyLimit - 10);
				node.setGarrisonServerId(randServerId);
				node.setAttackOccupyValue(value);
				node.setLastAttackOccupyTime(now);

			}
		}

		this.computeBattleScore(serverId);
	}

	@Override
	public int getServiceOrder() {
		return 1;
	}

	public void debug_csaResetStageEndTime(int serverId, int waitEndTime) {
		this.getControlServiceProxy().adjustCurrentStageEndTime(serverId, waitEndTime);
	}

	private void bi_csaCrossBattleOccupyChange(Role role, WorldCastleNode node) {
		try {
			Long roleId = role.getRoleId();
			int serverId = role.getCurrentServerId();
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int sessionId = crossServerAttackService.bi_sessionId(serverId);
			String castleId = node.getMetaId();
			int occupyPoint = node.getAttackOccupyValue();
			int occupyServerId = node.getGarrisonServerId();
			srvDep.getBiLogUtil().csaCrossBattleOccupyChange(roleId, crossBattleId, timePeriod, attackServerId, defendServerId, sessionId, castleId, occupyPoint, occupyServerId);

		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleOccupyChange error", e);
		}
	}

	private void bi_csaCrossBattleScoreChange(WorldCastleNode worldCastleNode, int scoreChange, int serverCurrentScore) {
		try {
			int serverId = worldCastleNode.getCurrentServerId();
			Long kingRoleId = crossServerAttackService.bi_kingRoleId(serverId);
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int sessionId = crossServerAttackService.bi_sessionId(serverId);
			String castleId = worldCastleNode.getMetaId();
			srvDep.getBiLogUtil().csaCrossBattleScoreChange(kingRoleId, crossBattleId, timePeriod, attackServerId, defendServerId, sessionId, castleId, scoreChange,
					serverCurrentScore);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleScoreChange error", e);
		}
	}

	private void bi_csaCrossBattleHalfResult(int serverId) {
		try {
			Long kingRoleId = crossServerAttackService.bi_kingRoleId(serverId);
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int attackScore = crossServerAttackService.bi_firstAttackServerScore(serverId);
			srvDep.getBiLogUtil().csaCrossBattleHalfResult(kingRoleId, crossBattleId, timePeriod, attackServerId, defendServerId, attackScore);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleHalfResult error", e);
		}
	}

	private void bi_csaCrossBattleFinalResult(CSAActivityGroupContext csaActivityGroupContext) {
		try {
			int serverId = csaActivityGroupContext.getSelfServerId();
			Long kingRoleId = crossServerAttackService.bi_kingRoleId(serverId);
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);
			int attackScore = crossServerAttackService.bi_firstAttackServerScore(serverId);
			int defendScore = crossServerAttackService.bi_firstDefenceServerScore(serverId);
			boolean isWin = csaActivityGroupContext.getSelfServerId() == attackServerId ? csaActivityGroupContext.isActivityWin() : !csaActivityGroupContext.isActivityWin();
			srvDep.getBiLogUtil().csaCrossBattleFinalResult(kingRoleId, crossBattleId, timePeriod, attackServerId, defendServerId, attackScore, defendScore, isWin);
		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleFinalResult error", e);
		}

	}

	private void bi_csaCrossBattleHalfPersonalScore(int serverId) {
		try {
			Collection<RoleCsaBattle> csaBattleList = roleCsaBattleDao.findAll();
			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);

			for (RoleCsaBattle csaBattle : csaBattleList) {
				if (csaBattle.getoServerId() != serverId) {
					continue;
				}

				Long roleId = csaBattle.getRoleId();
				long personalScore = csaBattle.getBattlePoint();

				int mainBuildingLevel = 0;
				Role role = roleDao.findById(roleId);
				if (role != null) {
					mainBuildingLevel = role.getLevel();
				}

				srvDep.getBiLogUtil().csaCrossBattleHalfPersonalScore(roleId, mainBuildingLevel, crossBattleId, timePeriod, attackServerId, defendServerId, personalScore);
			}

		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleHalfPersonalScore error", e);
		}
	}

	private void bi_csaCrossBattleFinalPersonalScore(int serverId) {
		try {

			Collection<RoleCsaBattle> csaBattleList = roleCsaBattleDao.findAll();
			if (!JavaUtils.bool(csaBattleList)) {
				return;
			}

			String crossBattleId = crossServerAttackService.bi_crossBattleId(serverId);
			long timePeriod = crossServerAttackService.bi_timePeriod(serverId);
			int attackServerId = crossServerAttackService.bi_firstAttackServerId(serverId);
			int defendServerId = crossServerAttackService.bi_firstDefenceServerId(serverId);

			for (RoleCsaBattle rankMember : csaBattleList) {
				if (rankMember.getoServerId() != serverId) {
					continue;
				}

				Long roleId = rankMember.getRoleId();
				long personalScore = rankMember.getBattlePoint();

				int mainBuildingLevel = 0;
				Role role = roleDao.findById(roleId);
				if (role != null) {
					mainBuildingLevel = role.getLevel();
				}

				srvDep.getBiLogUtil().csaCrossBattleFinalPersonalScore(roleId, mainBuildingLevel, crossBattleId, timePeriod, attackServerId, defendServerId, personalScore);
			}

		} catch (ExpectedException ignored){

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("bi_csaCrossBattleFinalPersonalScore error", e);
		}
	}

	public GcCSAMinMapWorldCastle wrapperCSAMinMapWorldCastle(int serverId) {
		GcCSAMinMapWorldCastle msg = new GcCSAMinMapWorldCastle();
		Collection<WorldCastleNode> nodes = worldCastleNodeDao.findByCurrentServerId(serverId);
		if (JavaUtils.bool(nodes)) {
			for (WorldCastleNode node : nodes) {
				if (node.getAttackOccupyValue() > 0) {
					PsCSAMinMapCastle minMapCastle = new PsCSAMinMapCastle();
					minMapCastle.setCsaAttackOccupy(node.getAttackOccupyValue());
					minMapCastle.setGarrisonServerId(node.getGarrisonServerId());
					minMapCastle.setMetaId(node.getMetaId());
					minMapCastle.setSetoutCount(node.getSetoutCount());
					msg.addToAlliances(minMapCastle);
				}
			}
		}

		return msg;
	}

	public void sendMinMapWorldCastle(Role role) {
		role.send(wrapperCSAMinMapWorldCastle(role.getCurrentServerId()));
	}
}
