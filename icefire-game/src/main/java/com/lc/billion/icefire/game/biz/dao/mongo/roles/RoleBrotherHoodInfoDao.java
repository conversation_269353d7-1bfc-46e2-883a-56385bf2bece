package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import com.lc.billion.icefire.game.biz.dao.RolesSingleEntityDao;
import com.lc.billion.icefire.game.biz.model.activity.brother.RoleBrotherHoodInfo;
import com.lc.billion.icefire.game.biz.model.role.Role;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */

@Repository
public class RoleBrotherHoodInfoDao extends RolesSingleEntityDao<RoleBrotherHoodInfo> {

    protected RoleBrotherHoodInfoDao() {
        super(RoleBrotherHoodInfo.class);
    }

    public RoleBrotherHoodInfo create(Role role, int turn) {
        RoleBrotherHoodInfo instance = new RoleBrotherHoodInfo();
        instance.setRoleId(role.getId());
        instance.setTurn(turn);
        instance.setTaskRewards(new ArrayList<>());
        return createEntity(role, instance);
    }

    public RoleBrotherHoodInfo createInstance(Role role, RoleBrotherHoodInfo instance) {
        return createEntity(role, instance);
    }
}
