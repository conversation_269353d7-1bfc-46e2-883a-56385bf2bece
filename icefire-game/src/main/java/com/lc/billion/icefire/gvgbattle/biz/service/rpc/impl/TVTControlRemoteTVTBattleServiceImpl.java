package com.lc.billion.icefire.gvgbattle.biz.service.rpc.impl;

import java.util.List;

import com.lc.billion.icefire.rpc.service.tvt.ITvtControlRemoteTvtBattleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lc.billion.icefire.gvgbattle.biz.manager.gvg.GVGBattleDataVoManager;
import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGAllianceSignUpInfoVo;
import com.lc.billion.icefire.rpc.vo.gvg.GvgBattleServerDispatchRecordVo;

/**
 * TVT中控服-->远程要调用-->TVT战斗服：战斗服执行的逻辑--战斗服的接口
 * */
@Service
public class TVTControlRemoteTVTBattleServiceImpl implements ITvtControlRemoteTvtBattleService {
	@Autowired
	private GVGBattleDataVoManager gvgDataVoManager;

	@Override
	public String broadcastTVTBattleServerDispatchRecord(GvgBattleServerDispatchRecordVo gvgBattleServerDispatchRecordVo) {
		gvgDataVoManager.updateGvgBattleServerDispatchRecordVo(gvgBattleServerDispatchRecordVo);
		gvgDataVoManager.initBattleFieldTimeLine();
		return null;
	}

	@Override
	public String broadcastTVTActivity(ActivityVo activityVo) {
		gvgDataVoManager.updateActivityVo(activityVo);
		return null;
	}

	@Override
	public void broadcastTVTAllianceSignUpInfo(List<GVGAllianceSignUpInfoVo> gvgAllianceSignUpInfoVos) {
		gvgDataVoManager.updateGvgAllianceSignUpInfoVo(gvgAllianceSignUpInfoVos);
	}
}
