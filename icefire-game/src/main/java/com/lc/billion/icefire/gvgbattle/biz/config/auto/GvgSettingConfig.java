package com.lc.billion.icefire.gvgbattle.biz.config.auto;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.model.gvg.GvgMatchType;
import com.lc.billion.icefire.game.biz.model.prop.AttributeValue;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.gvgcontrol.biz.GVGBattleServerCreateConfig;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;
import lombok.Getter;
import org.apache.commons.lang3.math.NumberUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Config(name = "gvgSetting", metaClass = GvgSettingConfig.GvgSettingMeta.class)
public class GvgSettingConfig {
    @MetaMap
    private final Map<String, GvgSettingMeta> metaMap = new HashMap<>();    // 这个是默认的Map，通过@MetaMap注解来自动生成

    /**
     * - gvgSetting id顺延500为tvt配置，完全拷贝一份gvgSetting的配置
     * - tvt配置的name字段前缀加tvt_
     * - 每次增加配置，依照上面2条规则，需要gvg部分，和tvt部分各增加一条
     */
    public static int TVT_SETTING_GAP = 500;
    public record ActiveRequire(int atLeastActiveDay, int atLeastActiveCount) {}
    private HashMap<GvgMatchType, ActiveRequire> activeRequireMap = new HashMap<>();

    @Getter
    private long wuChaoCDTime;
    @Getter
    private long supplyWagonTravelTime;
    private List<Integer> wuChaoCompletionScoreList;
    // 战场禁用道具
    @Getter
    private Set<String> gvgbanItem = new HashSet<>();

    @Getter
    private String gvgWuChaoTransportCompleteBroadcast;

    @Getter
    private int gvgPilicheTime;

    @Getter
    private int gvgPilicheDamage;

    @Getter
    private int gvgQingnangshu;

    @Getter
    private int gvgActivityNoEntryLimit;

    @Getter
    private int gvgActivityAutoOpenCondition;

    // 计算战力的士兵数
    @Getter
    private int gvgPersonalMatchScoreSoldierFixCount;

    // 士兵战力的系数
    @Getter
    private int gvgPersonalMatchScoreSoldierFixCoefficient;

    private String gvgAllianceID;

    // <server, allianceIds>
    @Getter
    private Map<Integer, Set<Long>> testMatchAllianceInfo = new HashMap<>();

    // <allianceId, index>
    @Getter
    private Map<Long, Integer> testMatchAllianceIndexInfo = new HashMap<>();

    @Getter
    private Map<Integer, Double> gvgPersonalMatchScoreRankFixMap = new HashMap<>();

    @Getter
    private List<Integer> gvgMatchGroupSeverOpenDaysList = new ArrayList<>();

    private Map<Integer, List<Point>> safeAreaBornPoint;

    @Getter
    private int gvgKillPoint;   // 除了攻击玩家基地之外，个人杀兵得分，每击杀X战力的士兵，获得1分

    @Getter
    private int gvgBaseKillPoint;   // 攻击玩家基地，个人杀兵得分，每击杀X战力的士兵，获得1分

    @Getter
    private boolean gvgLegion2MatchStartEarly;   // 攻击玩家基地，个人杀兵得分，每击杀X战力的士兵，获得1分

    public void init(List<GvgSettingMeta> list) {
        for(var meta: list){
            parse(meta.key, meta.value);
        }

        // 判断时间合法性
        GVGBattleServerCreateConfig gvgBattleServerCreateConfig = ServerConfigManager.getInstance().getGvgBattleServerCreateConfig();
        if (gvgBattleServerCreateConfig != null) {
            int checkTime = gvgBattleServerCreateConfig.getCheckTime();
            if (checkTime > 0) {
                List<GvgActivityTime> gvgActivityTimes = getGvgActivityTime();
                List<GvgActivityBattleTimeSelect> gvgActivityBattleTimeSelects = getGvgActivityBattleTimeSelects();
                GvgActivityTime gvgActivityTime = gvgActivityTimes.get(1);
                for (GvgActivityBattleTimeSelect gvgActivityBattleTimeSelect : gvgActivityBattleTimeSelects) {
                    if (gvgActivityTime.getWeek() != gvgActivityBattleTimeSelect.getWeek()) {
                        continue;
                    }
                    int diff = gvgActivityBattleTimeSelect.getLocalTime().toSecondOfDay() - gvgActivityTime.getLocalTime().toSecondOfDay();
                    if (diff > checkTime * TimeUtil.MINUTE_SECONDS) {
                        continue;
                    }
                    throw new AlertException("入场时间与第一场开始时间差小于临界值,通知策划改表" ,"gvgActivityTime", gvgActivityTime,"gvgActivityBattleTimeSelect",gvgActivityBattleTimeSelect);
                }
            }
        }
    }

    public void parse(String key, String value){
        if (key.endsWith("gvgWuChaoCDTime")) {
            wuChaoCDTime = Long.parseLong(value);
        } else if (key.endsWith("gvgWuchaoTransportTime")) {
            supplyWagonTravelTime = Long.parseLong(value);
        } else if (key.endsWith("gvgWuchaoTransportScore")) {
            wuChaoCompletionScoreList = new ArrayList<>();
            String[] foo = Objects.requireNonNull(MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2));
            for (String s : foo) {
                wuChaoCompletionScoreList.add(Integer.parseInt(s));
            }
        } else if (key.endsWith("gvgInitialBattlefieldPosition")) {
            safeAreaBornPoint = new HashMap<>();
            String[] pointsArray = Objects.requireNonNull(MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_1));
            for (int index = 0; index < pointsArray.length; index++) {
                safeAreaBornPoint.put(index, new ArrayList<>());
                String[] parse = Objects.requireNonNull(MetaUtils.parse(pointsArray[index], AbstractMeta.META_SEPARATOR_2));
                for (String point : parse) {
                    String[] xy = Objects.requireNonNull(MetaUtils.parse(point, AbstractMeta.META_SEPARATOR_3));
                    if (xy.length == 2) {
                        safeAreaBornPoint.get(index).add(Point.getInstance(Integer.parseInt(xy[0]), Integer.parseInt(xy[1])));
                    }
                }
            }
        } else if (key.endsWith("gvgItem")) {
            String[] parse = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
            if (parse != null) {
                gvgbanItem = Arrays.stream(parse).collect(Collectors.toSet());
            } else {
                gvgbanItem = new HashSet<>();
            }
        } else if (key.endsWith("gvgWuChaoTransportCompleteBroadcast")) {
            this.gvgWuChaoTransportCompleteBroadcast = value;
        } else if (key.endsWith("gvgPilicheTime")) {
            this.gvgPilicheTime = Integer.parseInt(value);
        } else if (key.endsWith("gvgPilicheDamage")) {
            this.gvgPilicheDamage = Integer.parseInt(value);
        } else if (key.endsWith("gvgQingnangshu")) {
            this.gvgQingnangshu = Integer.parseInt(value);
        } else if (key.endsWith("gvgAllianceID")) {
            this.gvgAllianceID = value;
            if (JavaUtils.bool(value)) {
                String[] parse = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
                if (parse == null) {
                    return;
                }
                for (int index = 0; index < parse.length; index++) {
                    String[] info = MetaUtils.parse(parse[index], AbstractMeta.META_SEPARATOR_3);
                    if (info == null || info.length != 2) {
                        continue;
                    }
                    for (String serverAlliance : info) {
                        String[] serverAllianceArray = MetaUtils.parse(serverAlliance, AbstractMeta.META_SEPARATOR_1);
                        if (serverAllianceArray == null || serverAllianceArray.length != 2) {
                            continue;
                        }
                        int serverId = Integer.parseInt(serverAllianceArray[0]);
                        if (!testMatchAllianceInfo.containsKey(serverId)) {
                            testMatchAllianceInfo.put(serverId, new HashSet<>());
                        }
                        testMatchAllianceInfo.get(serverId).add(Long.parseLong(serverAllianceArray[1]));

                        testMatchAllianceIndexInfo.put(Long.parseLong(serverAllianceArray[1]), index);
                    }
                }
            }
        } else if (key.endsWith("gvgActivityNoEntryLimit")) {
            gvgActivityNoEntryLimit = Integer.parseInt(value);
        } else if (key.endsWith("gvgActivityAutoOpenCondition")) {
            gvgActivityAutoOpenCondition = Integer.parseInt(value);
        } else if (key.endsWith("gvgActiveRequire")) {
            String[] activeLimitList = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
            if (activeLimitList != null) {
                for (String activeLimit : activeLimitList) {
                    var activeLimitArray = MetaUtils.parseInts(activeLimit, AbstractMeta.META_SEPARATOR_1);
                    if (activeLimitArray == null || activeLimitArray.length != 3) {
                        continue;
                    }

                    GvgMatchType gvgMatchType = GvgMatchType.findById(activeLimitArray[0]);
                    var atLeastActiveDay = activeLimitArray[1];
                    var atLeastActiveCount = activeLimitArray[2];
                    ActiveRequire activeRequireObj = new ActiveRequire(atLeastActiveDay, atLeastActiveCount);
                    activeRequireMap.put(gvgMatchType, activeRequireObj);
                }
            }
        } else if (key.endsWith("gvgPersonalMatchScoreSoldierFixCount")) {
            gvgPersonalMatchScoreSoldierFixCount = Integer.parseInt(value);
        } else if (key.endsWith("gvgPersonalMatchScoreSoldierFixCoefficient")) {
            gvgPersonalMatchScoreSoldierFixCoefficient = Integer.parseInt(value);
        } else if (key.equals("gvgKillPoint")) {
            gvgKillPoint = Integer.parseInt(value);
        } else if (key.equals("gvgBaseKillPoint")) {
            gvgBaseKillPoint = Integer.parseInt(value);
        } else if (key.equals("gvgPersonalMatchScoreRankFix")) {
            String[] matchScoreRateArray = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
            Map<Integer, Double> gvgPersonalMatchScoreRankFixMapTemp = new HashMap<>();
            if (matchScoreRateArray != null) {
                for (String matchScoreRate : matchScoreRateArray) {
                    String[] rankRateArray = MetaUtils.parse(matchScoreRate, AbstractMeta.META_SEPARATOR_3);
                    int[] rankArray = MetaUtils.parseInts(rankRateArray[0], '-');
                    double rate = Double.parseDouble(rankRateArray[1]);
                    for (int index = rankArray[0]; index <= rankArray[1]; index ++) {
                        gvgPersonalMatchScoreRankFixMapTemp.put(index, rate);
                    }
                }
            }
            gvgPersonalMatchScoreRankFixMap = gvgPersonalMatchScoreRankFixMapTemp;
        } else if (key.equals("gvgMatchGroupSeverOpenDays")) {
            gvgMatchGroupSeverOpenDaysList = MetaUtils.parseIntegerList(value, AbstractMeta.META_SEPARATOR_2);
        } else if (key.equals("gvgLegion2MatchStartEarly")) {
            gvgLegion2MatchStartEarly = "1".equals(value);
        }

    }



    public Map<String, GvgSettingMeta> getMetaMap() {
        return metaMap;
    }

    public GvgSettingMeta get(String id) {
        return metaMap.get(id);
    }


    public static class GvgSettingMeta extends AbstractMeta {
        // 4|00:00:00,6|00:00:00,7|23:59:59
        private List<GvgActivityTime> gvgActivityTime;
        private int gvgActivityRestTime;
        private int gvgActivityStartDay;
        private int gvgActivityAllianceNum;
        private int gvgActivitySeasonAllianceNum;
        private List<GvgActivityBattleTimeSelect> gvgActivityBattleTimeSelects;
        private int gvgActivityBattleTime;
        private int gvgActivityBattleReadyTime;
        private GvgActivitySafeArea gvgActivitySafeArea;
        private int[] gvgActivityMatchPoint;
        private double[] gvgActivityMatchRevise;
        private int[] gvgSignUpNum;
        private int gvgSignUpLevel;
        private int gvgEnterTime;
        //gvg匹配服务器分组。 <虚拟组号，服务器集合>
        private Map<Integer, Set<Integer>> gvgMatchServerGroup;
        private String value;

        // 约战活动时间点
        private List<GvgActivityTime> rzeActivityTime;
        // 约战场次
        private List<GvgActivityBattleTimeSelect> rzeActivityBattleTimeSelects;

        // <serverId,prosperityLimit> 不同服务器gvg资格要求的势力值不同
        private Map<Integer, Integer> gvgEngageActivityProsperityLimit;

        // gvg优化6期 新增的，对可选时间点进行分组 <组号，可选时间点列表>
        private Map<Integer, List<Integer>> gvgTimeSelectIndexByGroup;

        private String key;
        @Override
        public void init(JsonNode metaJson) {
            JsonNode nameJsonNode = metaJson.get("name");
            Objects.requireNonNull(nameJsonNode);
            key = nameJsonNode.asText();
            if (key.endsWith("gvgActivityTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    gvgActivityTime = new ArrayList<>();
                    String[] strings = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    for (String string : strings) {
                        String[] parse = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
                        int week = NumberUtils.toInt(parse[0]);
                        LocalTime localTime = LocalTime.parse(parse[1], DateTimeFormatter.ISO_LOCAL_TIME);
                        GvgActivityTime gvgActivityTime = new GvgActivityTime(week, localTime);
                        this.gvgActivityTime.add(gvgActivityTime);
                    }
                }
            } else if (key.endsWith("gvgActivityRestTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivityRestTime = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivityStartDay")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivityStartDay = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivityAllianceNum")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivityAllianceNum = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivitySeasonAllianceNum")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivitySeasonAllianceNum = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivityBattleTimeSelect")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    gvgActivityBattleTimeSelects = new ArrayList<>();
                    String[] strings = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    for (String string : strings) {
                        String[] parse = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
                        int week = NumberUtils.toInt(parse[0]);
                        LocalTime localTime = LocalTime.parse(parse[1], DateTimeFormatter.ISO_LOCAL_TIME);
                        int maxNum = NumberUtils.toInt(parse[2]);
                        GvgActivityBattleTimeSelect gvgActivityBattleTimeSelect = new GvgActivityBattleTimeSelect(week, localTime, maxNum);
                        this.gvgActivityBattleTimeSelects.add(gvgActivityBattleTimeSelect);
                    }
                }
            } else if (key.endsWith("gvgActivityBattleTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivityBattleTime = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivityBattleReadyTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgActivityBattleReadyTime = valueJsonNode.asInt();
            } else if (key.endsWith("gvgActivitySafeArea")) {
                // 10,60|110,60|5,15
                JsonNode valueJsonNode = metaJson.get("value");
                String string = valueJsonNode.asText();
                String[] parse1s = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
                Point position1 = null;
                Point position2 = null;
                int xLength = 0;
                int yLength = 0;
                if (JavaUtils.bool(parse1s)) {
                    for (int i = 0; i < parse1s.length; i++) {
                        String[] strings = MetaUtils.parse(parse1s[i], AbstractMeta.META_SEPARATOR_1);
                        if (i == 0) {
                            position1 = Point.getInstance(NumberUtils.toInt(strings[0]), NumberUtils.toInt(strings[1]));
                        } else if (i == 1) {
                            position2 = Point.getInstance(NumberUtils.toInt(strings[0]), NumberUtils.toInt(strings[1]));
                        } else if (i == 2) {
                            xLength = NumberUtils.toInt(strings[0]);
                            yLength = NumberUtils.toInt(strings[1]);
                        }
                    }
                }
                gvgActivitySafeArea = new GvgActivitySafeArea(position1, position2, xLength, yLength);
                if (!gvgActivitySafeArea.isEffective()) {
                    throw new AlertException("战斗服出生点策划数据出错");
                }
            } else if (key.endsWith("gvgActivityMatchPoint")) {
                JsonNode valueJsonNode = metaJson.get("value");
                String asText = valueJsonNode.asText();
                gvgActivityMatchPoint = MetaUtils.parseInts(asText, AbstractMeta.META_SEPARATOR_2);
            } else if (key.endsWith("gvgActivityMatchRevise")) {
                JsonNode valueJsonNode = metaJson.get("value");
                String asText = valueJsonNode.asText();
                gvgActivityMatchRevise = MetaUtils.parseDoubles(asText, AbstractMeta.META_SEPARATOR_2);
            } else if (key.endsWith("gvgSignUpNum")) {
                JsonNode valueJsonNode = metaJson.get("value");
                String asText = valueJsonNode.asText();
                gvgSignUpNum = MetaUtils.parseInts(asText, AbstractMeta.META_SEPARATOR_2);
            } else if (key.endsWith("gvgSignUpLevel")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgSignUpLevel = valueJsonNode.asInt();
            } else if (key.endsWith("gvgEnterTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                gvgEnterTime = valueJsonNode.asInt();
            } else if (key.endsWith("gvgEngageActivityTime")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    rzeActivityTime = new ArrayList<>();
                    String[] strings = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    for (String string : strings) {
                        String[] parse = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
                        int week = NumberUtils.toInt(parse[0]);
                        LocalTime localTime = LocalTime.parse(parse[1], DateTimeFormatter.ISO_LOCAL_TIME);
                        GvgActivityTime gvgActivityTime = new GvgActivityTime(week, localTime);
                        this.rzeActivityTime.add(gvgActivityTime);
                    }
                }
            } else if (key.endsWith("rzeActivityBattleTimeSelects")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    rzeActivityBattleTimeSelects = new ArrayList<>();
                    String[] strings = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    for (String string : strings) {
                        String[] parse = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
                        int week = NumberUtils.toInt(parse[0]);
                        LocalTime localTime = LocalTime.parse(parse[1], DateTimeFormatter.ISO_LOCAL_TIME);
                        int maxNum = NumberUtils.toInt(parse[2]);
                        GvgActivityBattleTimeSelect rzeActivityBattleTimeSelect = new GvgActivityBattleTimeSelect(week, localTime, maxNum);
                        this.rzeActivityBattleTimeSelects.add(rzeActivityBattleTimeSelect);
                    }
                }
            } else if (key.endsWith("gvgMatchServerSetting")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    String[] foobar = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    gvgMatchServerGroup = new HashMap<>();
                    for (int i = 0; i < foobar.length; ++i) {
                        String[] bar = MetaUtils.parse(foobar[i], AbstractMeta.META_SEPARATOR_2);
                        for (String serverIdStr : bar) {
                            int serverId = Integer.parseInt(serverIdStr);
                            gvgMatchServerGroup.compute(i, (k, v) -> v == null ? new HashSet<>() : v).add(serverId);
                        }
                    }

                }
            } else if (key.endsWith("gvgEngageActivityProsperityLimitNew")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    gvgEngageActivityProsperityLimit = new HashMap<>();
                    for (String foo : MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1)) {
                        String[] bar = MetaUtils.parse(foo, AbstractMeta.META_SEPARATOR_3);
                        int prosperityLimit = Integer.parseInt(bar[1]);
                        for (String foobar : MetaUtils.parse(bar[0], AbstractMeta.META_SEPARATOR_2)) {
                            int serverId = Integer.parseInt(foobar);
                            gvgEngageActivityProsperityLimit.put(serverId, prosperityLimit);
                        }
                    }
                }
            } else if (key.endsWith("gvgActivityBattleTimeSelectNew")) {
                JsonNode valueJsonNode = metaJson.get("value");
                if (valueJsonNode != null) {
                    gvgTimeSelectIndexByGroup = new HashMap<>();
                    String[] foo = MetaUtils.parse(valueJsonNode.asText(), AbstractMeta.META_SEPARATOR_1);
                    for (int i = 0; i < foo.length; ++i) {
                        for (String bar : MetaUtils.parse(foo[i], AbstractMeta.META_SEPARATOR_2)) {
                            int index = Integer.parseInt(bar);
                            gvgTimeSelectIndexByGroup.compute(i, (k, v) -> v == null ? new ArrayList<>() : v).add(index);
                        }
                    }
                }
            }
        }

        public Map<Integer, List<Integer>> getGvgTimeSelectIndexByGroup() {
            return gvgTimeSelectIndexByGroup;
        }

        public Map<Integer, Integer> getGvgEngageActivityProsperityLimit() {
            return gvgEngageActivityProsperityLimit;
        }

        public Map<Integer, Set<Integer>> getGvgMatchServerGroup() {
            return gvgMatchServerGroup;
        }

        public List<GvgActivityBattleTimeSelect> getRzeActivityBattleTimeSelects() {
            return rzeActivityBattleTimeSelects;
        }

        public List<GvgActivityTime> getGvgActivityTime() {
            return gvgActivityTime;
        }

        public int getGvgActivityRestTime() {
            return gvgActivityRestTime;
        }

        public int getGvgActivityStartDay() {
            return gvgActivityStartDay;
        }

        public int getGvgActivityAllianceNum() {
            return gvgActivityAllianceNum;
        }

        public int getGvgActivitySeasonAllianceNum() {
            return gvgActivitySeasonAllianceNum;
        }

        public List<GvgActivityBattleTimeSelect> getGvgActivityBattleTimeSelects() {
            return gvgActivityBattleTimeSelects;
        }

        public int getGvgActivityBattleTime() {
            return gvgActivityBattleTime;
        }

        public int getGvgActivityBattleReadyTime() {
            return gvgActivityBattleReadyTime;
        }

        public GvgActivitySafeArea getGvgActivitySafeArea() {
            return gvgActivitySafeArea;
        }

        public int[] getGvgActivityMatchPoint() {
            return gvgActivityMatchPoint;
        }

        public double[] getGvgActivityMatchRevise() {
            return gvgActivityMatchRevise;
        }

        public int[] getGvgSignUpNum() {
            return gvgSignUpNum;
        }

        public int getGvgSignUpLevel() {
            return gvgSignUpLevel;
        }

        public int getGvgEnterTime() {
            return gvgEnterTime;
        }

        public List<GvgActivityTime> getRzeActivityTime() {
            return rzeActivityTime;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 体力购买上限
     */
    public int getBuyStaminaMax() {
        String value = getMetaMap().get("1").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(1 + TVT_SETTING_GAP + "").getValue();
        }
        String[] costs = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return costs.length;
    }

    /**
     * 获取购买体力花费
     */
    public int getBuyStaminaCost(int count) {
        String value = getMetaMap().get("1").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(1 + TVT_SETTING_GAP + "").getValue();
        }
        String[] costs = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return Integer.parseInt(costs[count - 1]);
    }

    /**
     * 迁城购买上限
     */
    public int getBuyMoveMax() {
        String value = getMetaMap().get("2").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(2 + TVT_SETTING_GAP + "").getValue();
        }
        String[] costs = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return costs.length;
    }

    /**
     * 获取购买迁城花费
     */
    public int getBuyMoveCost(int count) {
        String value = getMetaMap().get("2").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(2 + TVT_SETTING_GAP + "").getValue();
        }
        String[] costs = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return Integer.parseInt(costs[count - 1]);
    }

    /**
     * 购买的体力数
     */
    public int getBuyStaminaNum() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return Integer.parseInt(getMetaMap().get(3 + TVT_SETTING_GAP + "").getValue());
        }
        return Integer.parseInt(getMetaMap().get("3").getValue());
    }

    public List<GvgActivityTime> getGvgActivityTime() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(4 + TVT_SETTING_GAP + "").getGvgActivityTime();
        }
        return getMetaMap().get("4").getGvgActivityTime();
    }

    public int getGvgActivityRestTime() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(4 + TVT_SETTING_GAP + "").getGvgActivityRestTime();
        }
        return getMetaMap().get("4").getGvgActivityRestTime();
    }

    public static class GvgActivityTime {

        private int week;
        private LocalTime localTime;

        public GvgActivityTime(int week, LocalTime localTime) {
            super();
            this.week = week;
            this.localTime = localTime;
        }

        public int getWeek() {
            return week;
        }

        public LocalTime getLocalTime() {
            return localTime;
        }

        @Override
        public String toString() {
            return "GvgActivityTime:" + week + "|" + localTime.format(DateTimeFormatter.ISO_LOCAL_TIME);
        }

    }

    public static class GvgActivityBattleTimeSelect {
        // 6|14:00:00|100,6|20:00:00|100,7|03:00:00|100,7|09:00:00|100
        private int week;
        private LocalTime localTime;
        private int maxNum;

        public GvgActivityBattleTimeSelect(int week, LocalTime localTime, int maxNum) {
            super();
            this.week = week;
            this.localTime = localTime;
            this.maxNum = maxNum;
        }

        public int getWeek() {
            return week;
        }

        public LocalTime getLocalTime() {
            return localTime;
        }

        public int getMaxNum() {
            return maxNum;
        }

        @Override
        public String toString() {
            return "GvgActivityBattleTimeSelect:" + week + "|" + localTime.format(DateTimeFormatter.ISO_LOCAL_TIME) + "|" + maxNum;
        }
    }

    public static class GvgActivitySafeArea {

        private Point position1;
        private Point position2;
        private int xLength;
        private int yLength;

        public GvgActivitySafeArea(Point position1, Point position2, int xLength, int yLength) {
            super();
            this.position1 = position1;
            this.position2 = position2;
            this.xLength = xLength;
            this.yLength = yLength;
        }

        public boolean isEffective() {
            return position1 != null && position2 != null && xLength > 0 && yLength > 0;
        }

        public Point getPosition1() {
            return position1;
        }

        public Point getPosition2() {
            return position2;
        }

        public int getxLength() {
            return xLength;
        }

        public int getyLength() {
            return yLength;
        }

    }

    public static Date getGVGActivityNextDate(int week, LocalTime localTime) {
        // 获取当前日期和时间的日历实例
        Calendar calendar = Calendar.getInstance();
        // 获取当前是周几，1表示周日，2表示周一，依次类推
        int day = calendar.get(Calendar.DAY_OF_WEEK);
        // 策划设定的目标周几，1-7表示周一到周日
        int targetDay = week;
        // 将当前周几转换为1-7的表示，1表示周一，7表示周日
        day = day - 1;
        if (day == 0) {
            day = 7;
        }
        // 标记是否需要跳到下周，默认为需要
        boolean nextWeek = true;
        // 如果当前周几小于目标周几，活动本周仍未到，不需要跳到下周
        if (day < targetDay) {
            nextWeek = false;
        } else if (day == targetDay) {
            // 如果当前周几等于目标周几，比较时间是否已经过了目标时间
            // 获取当前小时、分钟、秒
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            // 创建当前时间的LocalTime对象
            LocalTime localTime2 = LocalTime.of(hour, minute, second);
            // 判断当前时间是否晚于目标时间
            boolean isAfter = localTime.isAfter(localTime2);
            if (isAfter) {
                // 当前时间未过目标时间，不需要跳到下周
                nextWeek = false;
            }
        }

        // 如果不需要跳到下周，计算到本周目标日的日期差值
        if (!nextWeek) {
            calendar.add(Calendar.DATE, targetDay - day);
        } else {
            // 如果需要跳到下周，计算到下周目标日的日期差值
            calendar.add(Calendar.DATE, 7 - day + targetDay);
        }
        // 设置目标时间的小时、分钟和秒
        calendar.set(Calendar.HOUR_OF_DAY, localTime.getHour());
        calendar.set(Calendar.MINUTE, localTime.getMinute());
        calendar.set(Calendar.SECOND, localTime.getSecond());
        // 返回计算后的目标日期
        return calendar.getTime();
    }


    /**
     * 兵工厂添加点数上限
     */
    public int getArmFactoryMaxCount() {
        String value = getMetaMap().get("6").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(6 + TVT_SETTING_GAP + "").getValue();
        }
        String[] points = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return points.length;
    }

    /**
     * 获取兵工厂每10分钟增加的积分
     */
    public int getArmFactoryAddPoint(int count) {
        String value = getMetaMap().get("6").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(6 + TVT_SETTING_GAP + "").getValue();
        }
        String[] points = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return Integer.parseInt(points[count]);
    }

    /**
     * 获取兵工厂增加的积分时间间隔
     */
    public int getNewArmFactoryInterval(int count) {
        String value = getMetaMap().get("66").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(66 + TVT_SETTING_GAP + "").getValue();
        }
        String[] points = MetaUtils.parse(value, AbstractMeta.META_SEPARATOR_2);
        return Integer.parseInt(points[count]);
    }

    /**
     * 获取体力增加间隔
     */
    public int getStaminaInterval() {
        String value = getMetaMap().get("9").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(9 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * 获取迁城次数增加间隔
     */
    public int getMoveInterval() {
        String value = getMetaMap().get("10").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(10 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * 开服超过30天的服务器才能参加这个活动
     */
    public int getGvgActivityStartDay() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(8 + TVT_SETTING_GAP + "").getGvgActivityStartDay();
        }
        return getMetaMap().get("8").getGvgActivityStartDay();
    }

    /**
     * 每个服务器器前10联盟有资格报名
     */
    public int getGvgActivityAllianceNum() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(11 + TVT_SETTING_GAP + "").getGvgActivityAllianceNum();
        }
        return getMetaMap().get("11").getGvgActivityAllianceNum();
    }

    public int getGvgActivitySeasonAllianceNum() {
        return getMetaMap().get("101").getGvgActivitySeasonAllianceNum();
    }

    /**
     * 可以选择的比赛开始时间：周几|开始时间|结束时间|最大可报名联盟数量
     */
    public List<GvgActivityBattleTimeSelect> getGvgActivityBattleTimeSelects() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(14 + TVT_SETTING_GAP + "").getGvgActivityBattleTimeSelects();
        }
        return getMetaMap().get("14").getGvgActivityBattleTimeSelects();
    }

    /**
     * 战场持续时间（秒）
     */
    public int getGvgActivityBattleTime() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(15 + TVT_SETTING_GAP + "").getGvgActivityBattleTime();
        }
        return getMetaMap().get("15").getGvgActivityBattleTime();
    }

    /**
     * 战场准备时间（秒）
     */
    public int getGvgActivityBattleReadyTime() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(16 + TVT_SETTING_GAP + "").getGvgActivityBattleReadyTime();
        }
        return getMetaMap().get("16").getGvgActivityBattleReadyTime();
    }

    /**
     * 获取物资点刷新个数
     */
    public int getRefreshResourceNum() {
        String[] points = MetaUtils.parse(getMetaMap().get("12").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(12 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[0]);
    }

    /**
     * 获取物资点刷新间隔
     */
    public int getRefreshResourceInterval() {
        String[] points = MetaUtils.parse(getMetaMap().get("12").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(12 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[1]);
    }

    /**
     * 获取物资点持续时间
     */
    public int getRefreshResourceTime() {
        String[] points = MetaUtils.parse(getMetaMap().get("12").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(12 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[2]);
    }

    /**
     * 获取物资点刷新时间
     */
    public int[] getRefreshResourceRefreshTime() {
        return MetaUtils.parseInts(getMetaMap().get("89").getValue(), AbstractMeta.META_SEPARATOR_2);
    }

    /**
     * 获取僵尸刷新触发条件
     */
    public int getRefreshNpcTrigger() {
        String[] points = MetaUtils.parse(getMetaMap().get("13").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(13 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[0]);
    }

    /**
     * 获取僵尸刷新数量
     */
    public int getRefreshNpcNum() {
        String[] points = MetaUtils.parse(getMetaMap().get("13").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(13 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[1]);
    }

    /**
     * 获取僵尸刷新间隔
     */
    public int getRefreshNpcInterval() {
        String[] points = MetaUtils.parse(getMetaMap().get("13").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(13 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[2]);
    }

    /**
     * 获取僵尸刷新范围
     */
    public int getRefreshNpcWidth() {
        String[] points = MetaUtils.parse(getMetaMap().get("13").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(13 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(points[3]);
    }

    /**
     * 获取僵尸Id
     */
    public String getRefreshNpcId() {
        String[] points = MetaUtils.parse(getMetaMap().get("13").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            points = MetaUtils.parse(getMetaMap().get(13 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return points[4];
    }

    /**
     * 获取物资点刷新坐标
     */
    public int[] getRefreshResPoints() {
        String[] ranges = MetaUtils.parse(getMetaMap().get("17").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            ranges = MetaUtils.parse(getMetaMap().get(17 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        if (ranges != null) {
            int[] points = new int[ranges.length * 4];
            int index = 0;
            for (int i = 0; i < ranges.length; i++) {
                int[] pointsConfig = MetaUtils.parseInts(ranges[i], AbstractMeta.META_SEPARATOR_1);
                for (int j = 0; j < pointsConfig.length; j++) {
                    points[index] = pointsConfig[j];
                    index++;
                }
            }

            return points;
        }

        return null;
    }

    /**
     * GVG采集的积分重量
     *
     * @return
     */
    public int getGvgResCarry() {
        String[] arr = MetaUtils.parse(getMetaMap().get("19").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            arr = MetaUtils.parse(getMetaMap().get(19 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(arr[0]);
    }

    /**
     * GVG采集速度
     *
     * @return
     */
    public double getGvgGatherSpeed() {
        String[] arr = MetaUtils.parse(getMetaMap().get("86").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            arr = MetaUtils.parse(getMetaMap().get(86 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Double.parseDouble(arr[0]);
    }

    /**
     * GVG最大采集量
     *
     * @return
     */
    public long getGvgGatherResourceMax() {
        String[] arr = MetaUtils.parse(getMetaMap().get("86").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            arr = MetaUtils.parse(getMetaMap().get(86 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Long.parseLong(arr[1]);
    }

    /**
     * GVG负重和采集量的转换系数
     *
     * @return
     */
    public long getGvgCarryToPointRate() {
        String[] arr = MetaUtils.parse(getMetaMap().get("86").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            arr = MetaUtils.parse(getMetaMap().get(86 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Long.parseLong(arr[2]);
    }

    /**
     * GVG单次最大采集量
     *
     * @return
     */
    public int getGvgGatherMaxAmount() {
        String[] arr = MetaUtils.parse(getMetaMap().get("19").getValue(), AbstractMeta.META_SEPARATOR_2);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            arr = MetaUtils.parse(getMetaMap().get(19 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return Integer.parseInt(arr[2]);
    }

    /**
     * 战场胜利得分:联盟点数最大上限
     */
    public int getGvgWinPoint() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return Integer.parseInt(getMetaMap().get(18 + TVT_SETTING_GAP + "").getValue());
        }
        return Integer.parseInt(getMetaMap().get("18").getValue());
    }

    /**
     * 指挥中心控制可迁城区域（长宽）
     */
    public int getGvgMoveArea() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return Integer.parseInt(getMetaMap().get(20 + TVT_SETTING_GAP + "").getValue());
        }
        return Integer.parseInt(getMetaMap().get("20").getValue());
    }

    public GvgActivitySafeArea getGvgActivitySafeArea() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(23 + TVT_SETTING_GAP + "").getGvgActivitySafeArea();
        }
        return getMetaMap().get("23").getGvgActivitySafeArea();
    }

    /**
     * 所有成员行军速度增加百分比
     */
    public AttributeValue getGvgSpeedAddRate() {
        AttributeValue[] property = MetaUtils.parseObjects(getMetaMap().get("21").getValue(), AttributeValue.class);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            property = MetaUtils.parseObjects(getMetaMap().get(21 + TVT_SETTING_GAP + "").getValue(), AttributeValue.class);
        }
        if (property != null) {
            return property[0];
        }

        return null;
    }

    /**
     * 医院容量增加百分比
     */
    public AttributeValue getGvgHospitalCapacityAddRate() {
        AttributeValue[] property = MetaUtils.parseObjects(getMetaMap().get("22").getValue(), AttributeValue.class);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            property = MetaUtils.parseObjects(getMetaMap().get(22 + TVT_SETTING_GAP + "").getValue(), AttributeValue.class);
        }
        if (property != null) {
            return property[0];
        }
        return null;
    }

    public int[] getGvgActivityMatchPoint() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(24 + TVT_SETTING_GAP + "").getGvgActivityMatchPoint();
        }
        return getMetaMap().get("24").getGvgActivityMatchPoint();
    }

    public double[] getGvgActivityMatchRevise() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(25 + TVT_SETTING_GAP + "").getGvgActivityMatchRevise();
        }
        return getMetaMap().get("25").getGvgActivityMatchRevise();
    }

    /**
     * 普通体力增加间隔
     */
    public int getCommonStaminaInterval() {
        String value = getMetaMap().get("26").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(26 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * 首次进战场迁城次数
     */
    public int getFirstMoveCount() {
        String value = getMetaMap().get("27").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(27 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * 首次进战场行动力
     */
    public int getFirstStamina() {
        String value = getMetaMap().get("28").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(28 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgScoutDurability() {
        String value = getMetaMap().get("29").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(29 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgAttackNpcDurability() {
        String value = getMetaMap().get("30").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(30 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgAttackResDurability() {
        String value = getMetaMap().get("31").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(31 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgGatherResDurability() {
        String value = getMetaMap().get("32").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(32 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgGarrisonDurability() {
        String value = getMetaMap().get("33").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(33 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgAttackBuildingPVEDurability() {
        String value = getMetaMap().get("34").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(34 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgAttackBuildindPVPDurability() {
        String value = getMetaMap().get("35").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(35 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgRallyBuildingPVEDurability() {
        String value = getMetaMap().get("36").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(36 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgRallyBuildingPVPDurability() {
        String value = getMetaMap().get("37").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(37 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgAttackPVPDurability() {
        String value = getMetaMap().get("38").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(38 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgRallyPVPDurability() {
        String value = getMetaMap().get("39").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(39 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgReinforceDurability() {
        String value = getMetaMap().get("40").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(40 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgJoinBuildingPVEDurability() {
        String value = getMetaMap().get("41").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(41 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgJoinBuildingPVPDurability() {
        String value = getMetaMap().get("42").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(42 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgJoinPVPDurability() {
        String value = getMetaMap().get("43").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(43 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * GVG个人杀兵得分
     */
    public int getGvgKillSoldier() {
        String value = getMetaMap().get("44").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(44 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int[] getGvgSignUpNum() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(45 + TVT_SETTING_GAP + "").getGvgSignUpNum();
        }
        return getMetaMap().get("45").getGvgSignUpNum();
    }

    public String[] getRandomHeroCount() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return MetaUtils.parse(getMetaMap().get(48 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return MetaUtils.parse(getMetaMap().get("48").getValue(), AbstractMeta.META_SEPARATOR_2);
    }

    public String[] getGvgKillNpcPoint() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return MetaUtils.parse(getMetaMap().get(51 + TVT_SETTING_GAP + "").getValue(), AbstractMeta.META_SEPARATOR_2);
        }
        return MetaUtils.parse(getMetaMap().get("51").getValue(), AbstractMeta.META_SEPARATOR_2);
    }

    public int getGvgSignUpLevel() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(50 + TVT_SETTING_GAP + "").getGvgSignUpLevel();
        }
        return getMetaMap().get("50").getGvgSignUpLevel();
    }

    /**
     * GVG地图上僵尸总数
     */
    public int getGvgNpcTotalCount() {
        String value = getMetaMap().get("52").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(52 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * buff生效时间
     */
    public int getOccupyEffectTime() {
        String value = getMetaMap().get("53").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(53 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public int getGvgEnterTime() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(54 + TVT_SETTING_GAP + "").getGvgEnterTime();
        }
        return getMetaMap().get("54").getGvgEnterTime();
    }

    /**
     * 战场准备结束时间
     *
     * @param gvgBattleStartTime
     * @return
     */
    public long getGVGBattleReadEndTime(long gvgBattleStartTime) {
        return gvgBattleStartTime + getGvgActivityBattleReadyTime() * TimeUtil.SECONDS_MILLIS;
    }

    /**
     * 战场结束时间
     */
    public long getGVGBattleEndTime(long gvgBattleStartTime) {
        // 准备阶段结束时间
        long readyEndTime = getGVGBattleReadEndTime(gvgBattleStartTime);
        long endTime = readyEndTime + getGvgActivityBattleTime() * TimeUtil.SECONDS_MILLIS;
        return endTime;
    }

    /**
     * GVG服专用初始医疗速度提升buff
     */
    public AttributeValue getGvgHospitalAddSpeedRate() {
        AttributeValue[] property = MetaUtils.parseObjects(getMetaMap().get("55").getValue(), AttributeValue.class);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            property = MetaUtils.parseObjects(getMetaMap().get(55 + TVT_SETTING_GAP + "").getValue(), AttributeValue.class);
        }
        if (property != null) {
            return property[0];
        }

        return null;
    }

    /**
     * GVG服专用初始医疗消耗降低buff
     */
    public AttributeValue getGvgHospitalReduceResourceRate() {
        AttributeValue[] property = MetaUtils.parseObjects(getMetaMap().get("56").getValue(), AttributeValue.class);
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            property = MetaUtils.parseObjects(getMetaMap().get(56 + TVT_SETTING_GAP + "").getValue(), AttributeValue.class);
        }
        if (property != null) {
            return property[0];
        }

        return null;
    }

    /**
     * GVG战场体力
     */
    public int getMaxStamina() {
        String value = getMetaMap().get("46").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(46 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }


    /**
     * gvg胜利的可分配奖励
     */
    public String getGvgVictoryAllocationGroupId() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(57 + TVT_SETTING_GAP + "").getValue();
        }
        return getMetaMap().get("57").getValue();
    }

    /**
     * gvg失败的可分配奖励
     */
    public String getGvgDefeatAllocationGroupId() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(60 + TVT_SETTING_GAP + "").getValue();
        }
        return getMetaMap().get("60").getValue();
    }

    /**
     * GvG匹配组，单组成员数量
     */
    public int getMatchGroupSize() {
        String value = getMetaMap().get("63").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(63 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * GVG使用医院技能CD
     */
    public int getCureWoundedSkillCD() {
        String value = getMetaMap().get("64").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(64 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * GVG战斗战报概览 过滤的击杀战力值
     */
    public int getGVGObBattleGuideLimitation() {
        String value = getMetaMap().get("68").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(68 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    public List<GvgActivityTime> getRzeActivityTimes() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(69 + TVT_SETTING_GAP + "").getRzeActivityTime();
        }
        return getMetaMap().get("69").getRzeActivityTime();
    }

    /**
     * GVG约战：联盟势力值条件限制
     */
    public int getRZEProsperityLimit() {
        String value = getMetaMap().get("72").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(72 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * RZE 可以选择的比赛开始时间：周几|开始时间|结束时间|最大可报名联盟数量
     */
    public List<GvgActivityBattleTimeSelect> getRzeActivityBattleTimeSelects() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(70 + TVT_SETTING_GAP + "").getRzeActivityBattleTimeSelects();
        }
        return getMetaMap().get("70").getRzeActivityBattleTimeSelects();
    }

    /**
     * RZE 参赛资格 需要的最低联盟势力值
     *
     * @return
     */
    public long getRZEQualifyProsperity() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return Long.parseLong(getMetaMap().get(72 + TVT_SETTING_GAP + "").getValue());
        }
        return Long.parseLong(getMetaMap().get("72").getValue());
    }

    /**
     * gvg匹配分组数据
     *
     * @return
     */
    public Map<Integer, Set<Integer>> getGvgMatchServerGroup() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(74 + TVT_SETTING_GAP + "").getGvgMatchServerGroup();
        }
        return getMetaMap().get("74").getGvgMatchServerGroup();
    }

    /**
     * 不同server 参加GVG需要的势力值
     *
     * @return
     */
    public Map<Integer, Integer> getGvgEngageActivityProsperityLimit() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(75 + TVT_SETTING_GAP + "").getGvgEngageActivityProsperityLimit();
        }
        return getMetaMap().get("75").getGvgEngageActivityProsperityLimit();
    }

    /**
     * gvg可选时间段的分组
     *
     * @return
     */
    public Map<Integer, List<Integer>> getGvgTimeSelectByGroup() {
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            return getMetaMap().get(76 + TVT_SETTING_GAP + "").getGvgTimeSelectIndexByGroup();
        }
        return getMetaMap().get("76").getGvgTimeSelectIndexByGroup();
    }

    /**
     * gvgf 选几个时间
     * @return
     */
    public int getGvgActivityBattleTimeSelectMode(){
        String value = getMetaMap().get("77").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(77 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * gvg/TVT打点联盟积分的积分间隔
     * @return
     */
    public int getBIAlliancePointInterval(){
        String value = getMetaMap().get("78").getValue();
        if(Application.getServerType() == ServerType.TVT_BATTLE || Application.getServerType() == ServerType.TVT_CONTROL){
            value = getMetaMap().get(78 + TVT_SETTING_GAP + "").getValue();
        }
        return Integer.parseInt(value);
    }

    /**
     * gvg 自动报名开关 1-开启 0-关闭
     * @return
     */
    public int getGvgAutoRegisterSwitch(){
        String value = getMetaMap().get("79").getValue();
        return Integer.parseInt(value);
    }


    /**
     * gvg gvg匹配分数基础值为战力最高的{}名玩家的战力值之和
     * @return
     */
    public int getGvgActivityMatchMemberSelectNum(){
        String value = getMetaMap().get("80").getValue();
        return Integer.parseInt(value);
    }

    /**
     * gvg 匹配战力差在{}%（可配）以内的随机一个同盟
     * @return
     */
    public double getGvgMatchScoreGap(){
        String value = getMetaMap().get("81").getValue();
        return Double.parseDouble(value);
    }

    /**
     * gvg 匹配战力差在getGvgMatchScoreGap()以内的随机一个同盟,每次扩大{}%（可配）范围
     * @return
     */
    public double getGvgMatchScoreGapFixAdd(){
        String value = getMetaMap().get("82").getValue();
        return Double.parseDouble(value);
    }

    /**
     * 同盟中被选择参战的主力+替补，总人数≥{} 才可以进行匹配
     * @return
     */
    public int getGvGMatchMemberCountLimit() {
        String value = getMetaMap().get("83").getValue();
        return Integer.parseInt(value);
    }

    /**
     * 同盟中被选择参战的主力+替补，总人数≥{} 才可以进行匹配
     * @return
     */
    public int getGvGAllianceEnterCountLimit() {
        String value = getMetaMap().get("84").getValue();
        return Integer.parseInt(value);
    }

    /**
     * 选玩家时对玩家离线时长限制。
     * @return
     */
    public int getGVGSelectOffLineLimit() {
        String value = getMetaMap().get("85").getValue();
        return Integer.parseInt(value);
    }

    /**
     * 免费迁城的cd
     */
    public int getFreeMoveCityTime() {
        String value = getMetaMap().get("90").getValue();
        return Integer.parseInt(value);
    }

    public int getWuChaoCompleteScore(int completeCount){
        int index = completeCount;
        if(index < 0) {
            index = 0;
        } else if(index >= wuChaoCompletionScoreList.size()){
            index = wuChaoCompletionScoreList.size() - 1;
        }

        return wuChaoCompletionScoreList.get(index);
    }

    public List<Point> getBornPointListByIndex(int areaIndex) {
        if (safeAreaBornPoint == null) {
            return null;
        }
        return safeAreaBornPoint.get(areaIndex);
    }

    public ActiveRequire getGVGActiveRequire(GvgMatchType gvgMatchType) {
        return activeRequireMap.get(gvgMatchType);
    }
}
