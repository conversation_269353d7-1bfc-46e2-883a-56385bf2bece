package com.lc.billion.icefire.game.biz.service.impl.scene;

import com.lc.billion.icefire.core.common.RandomUtils;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.model.MetaServerType;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.core.support.Point;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.async.scenenoderefresh.ActivityWhispererClearOperation;
import com.lc.billion.icefire.game.biz.async.scenenoderefresh.GvgResourceSceneNodeRefreshOperation;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.config.NewResRefreshConfig.NewResRefreshMeta;
import com.lc.billion.icefire.game.biz.config.NpcConfig.NpcMeta;
import com.lc.billion.icefire.game.biz.config.ParcellevelConfig.ParcellevelMeta;
import com.lc.billion.icefire.game.biz.config.WorldBossConfig.WorldBossMeta;
import com.lc.billion.icefire.game.biz.config.kvk.KvkNewResRefreshConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkNpcLevelConfig;
import com.lc.billion.icefire.game.biz.config.kvk.KvkParcellevelConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGNpcNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGResNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.NewResNodeDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.NpcNodeDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.ResourceOutputManager;
import com.lc.billion.icefire.game.biz.manager.RoleCityManager;
import com.lc.billion.icefire.game.biz.manager.RoleExtraManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.npc.NpcType;
import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleCity;
import com.lc.billion.icefire.game.biz.model.role.RoleExtra;
import com.lc.billion.icefire.game.biz.model.scene.*;
import com.lc.billion.icefire.game.biz.model.scene.node.*;
import com.lc.billion.icefire.game.biz.service.ShutdownHook;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.army.GatherService;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogAlertType;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.notice.NoticeConstants;
import com.lc.billion.icefire.game.biz.service.impl.scene.scenenoderefresh.WorldRefreshService;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.worldarea.WorldAreaService;
import com.lc.billion.icefire.game.biz.service.impl.worldboss.WorldBossServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgBuildingConfig.GvgBuildingMeta;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root.GVGBattleFieldTimeLineDao;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.BuildingType;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.GVGBattleFieldTimeLine;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.protocol.GcGiveUpNewRes;
import com.lc.billion.icefire.protocol.GcMapSearch;
import com.lc.billion.icefire.protocol.GcMapSearchInfo;
import com.lc.billion.icefire.protocol.constant.PsSearchType;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.simfun.sgf.utils.JavaUtils;
import com.simfun.sgf.utils.TimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 地图事件服务
 */
@Service
public class SceneNodeRefreshServiceImpl implements ShutdownHook {
    private static final Logger logger = LoggerFactory.getLogger(SceneNodeRefreshServiceImpl.class);

    private static final long NPC_REFRESH_TIME = TimeUtils.DAY_MILLIS;

    @Autowired
    private WorldRefreshService worldRefreshService;
    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private AsyncOperationServiceImpl asyncOperationService;
    @Autowired
    private NpcNodeDao npcNodeDao;
    @Autowired
    private NewResNodeDao newResNodeDao;
    @Autowired
    private GVGResNodeDao gvgResNodeDao;
    @Autowired
    private GVGNpcNodeDao gvgNpcNodeDao;
    @Autowired
    private ArmyDao armyDao;
    @Autowired
    private RoleCityManager roleCityManager;
    @Autowired
    private RoleExtraManager roleExtraManager;
    @Autowired
    private SceneServiceImpl sceneService;
    @Autowired
    private WorldServiceImpl worldService;
    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ArmyManager armyManager;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private ResourceOutputManager resourceOutputManager;
    @Autowired
    private GatherService gatherService;
    @Autowired
    private WorldAreaService worldAreaService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private WorldBossServiceImpl worldBossService;
    @Autowired
    private GVGBattleFieldTimeLineDao gvgBattleFieldTimeLineDao;

    public void startService() {
        worldRefreshService.startService();
    }

    public void initWhispererBoss() {
        List<Integer> allServerIds = Application.getAllServerIds();
        allServerIds.forEach(serverId -> {
            worldRefreshService.initWhispererBossNode(serverId);
        });
    }

    public void initRegionCapital() {
        List<Integer> allServerIds = Application.getAllServerIds();
        allServerIds.forEach(serverId -> {
            worldRefreshService.initRegionCapital(serverId);
        });
    }

    public void initSiegeEngines() {
        worldRefreshService.initSiegeEngines(Application.getServerId());
    }

    /**
     * 清理和刷新GVG战场的资源点
     *
     * @return
     */
    public void asyncRefreshGvgRes() {
        if (Application.isBattleServer()) {
            logger.info("GVG战场物资点异步刷新...............");
            asyncOperationService.execute(new GvgResourceSceneNodeRefreshOperation(this));
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-26 15:33:02.598
     * @Param [role]
     * @Return int
     * @Description 获取玩家地块允许生成最高资源等级
     */
    public int getRoleParcelLevel(Role role) {
        RoleCity city = roleCityManager.getRoleCity(role.getId());
        MapData mapData = worldService.getWorld().getMapData(role.getCurrentServerId());
        var cityGrid = mapData.getGrid(city.getPosition());
        if (Application.getServerType(role.getCurrentServerId()) == ServerType.KVK_SEASON) {
            var parcellevelConfig = configService.getConfig(KvkParcellevelConfig.class);
            var meta = parcellevelConfig.get(cityGrid.getRegionId());
            return meta == null ? 0 : meta.getResLevel();
        } else {
            ParcellevelConfig parcellevelConfig = configService.getConfig(ParcellevelConfig.class);
            ParcellevelMeta parcellevelMeta = parcellevelConfig.getMeta(cityGrid.getRegionId());
            return parcellevelMeta == null ? 0 : parcellevelMeta.getResLevel();
        }
    }

    /**
     * 刷新一个资源田
     */
    public NewResNode refreshNewRes(Role role, int type, int level, List<MapGrid> canRefreshMapGrids) {
        // 判断是否符合资源地等级上限
        var levelLimit = getRoleParcelLevel(role);
        if (level > levelLimit) {
            logger.warn("refreshNewRes level exceed the limit : {} > {}", level, levelLimit);
            return null;
        }

        String metaId = null;
        int resReversed = 0;
        KvkNewResRefreshConfig resRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
        if (Application.getServerType(role.getCurrentServerId()) == ServerType.KVK_SEASON) {
            var kvkNewResRefreshMeta = resRefreshConfig.getMetaByTypeAndLevel(type, level);
            // 判空
            if (kvkNewResRefreshMeta == null) {
                ErrorLogUtil.errorLog("kvkNewResRefreshMeta_not_found", ErrorLogUtil.ALERT_TYPE_KEY, ErrorLogAlertType.ATTACK, "type", type, "level", level);
                return null;
            }

            metaId = kvkNewResRefreshMeta.getId();
            resReversed = kvkNewResRefreshMeta.getResReserves();
        } else {
            NewResRefreshMeta newResRefreshMeta = configService.getConfig(NewResRefreshConfig.class).getByTypeAndLevel(type, level);
            // 判空
            if (newResRefreshMeta == null) {
                ErrorLogUtil.errorLog("newResRefreshMeta_not_found", ErrorLogUtil.ALERT_TYPE_KEY, ErrorLogAlertType.ATTACK, "type", type, "level", level);
                return null;
            }
            metaId = newResRefreshMeta.getId();
            resReversed = newResRefreshMeta.getResReserves();
        }

        // todo 只要是特等资源 都不允许刷出来 临时方案
        List<String> resIds = List.of("10709", "10909", "11109", "10610", "10710", "10910", "11110", "10611", "10711", "10911", "11111", "10612", "10712", "10912", "11112", "10613", "10713", "10913", "11113", "10614", "21001", "21002", "21003", "21004", "21005", "22001", "22002", "22003", "22004", "22005", "23001", "23002", "23003", "23004", "23005", "24001", "24002", "24003", "24004", "24005");
        if (resIds.contains(metaId)) {
            ErrorLogUtil.errorLog("refreshNewRes id limit", "metaId",metaId, "roleId",role.getRoleId());
            return null;
        }

        long nextRefreshTime = configService.getConfig(SettingConfig.class).getResDuration() * TimeUtils.SECONDS_MILLIS;
        if (JavaUtils.bool(canRefreshMapGrids)) {
            var mapGrid = RandomUtils.random(canRefreshMapGrids);
            NewResNode newResNode = newResNodeDao.create(role.getCurrentServerId(), metaId, TimeUtil.getNow() + nextRefreshTime, mapGrid.getPosition(), resReversed);
            newResNodeDao.save(newResNode);
            sceneService.add(newResNode, false);
            logger.info("最近的坐标{} 新资源田刷新{} level:{} type:{} metaId:{} roleId:{}", mapGrid.getPosition(), newResNode.getPosition(), level, type, metaId, role.getRoleId());
            return newResNode;
        }

        return null;
    }

    /**
     * 刷新GVG战场的物资点
     */
    public int refreshGvgRes() {
        long now = TimeUtil.getNow();
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        // 格子刷新物资点总数量
        int zoneRefreshCount = gvgSettingConfig.getRefreshResourceNum();
        // 物资点坐标配置
        int[] pointsConfig = gvgSettingConfig.getRefreshResPoints();
        // 坐标
        Map<Integer, Set<Point>> indexPoints = new HashMap<>();
        // 每一个区域刷新数量
        Map<Integer, Integer> indexCounts = new HashMap<>();
        int index = 1;
        for (int i = 0; i < pointsConfig.length; i = i + 4) {
            int x = pointsConfig[i];
            int y = pointsConfig[i + 1];
            int size = pointsConfig[i + 2];
            int count = pointsConfig[i + 3];
            Set<Point> points = new HashSet<>();
            for (int m = 0; m <= size; m++) {
                for (int n = 0; n <= size; n++) {
                    int px = x + m;
                    int py = y + n;
                    points.add(Point.getInstance(px, py));
                }
            }
            indexPoints.put(index, points);
            indexCounts.put(index, count);
            index++;
        }

        int has = 0;
        if (gvgResNodeDao.findAll() != null) {
            has = gvgResNodeDao.findAll().size();
        }
        logger.warn("GVG战场物资点刷新开始，当前有物资点:{} 准备刷新列表:{}", has, indexPoints.size());

        // 当前刷新总数量
        List<GvgResNode> nodes = new ArrayList<>();
        int serverId = Application.getServerId();
        for (Integer key : indexPoints.keySet()) {
            if (nodes.size() >= zoneRefreshCount)
                break;
            List<Point> pointList = new ArrayList<>(indexPoints.get(key));
            List<Point> retPointList = RandomUtils.randomSelectEvenly(pointList, indexCounts.get(key));
            for (Point point : retPointList) {
                if (nodes.size() >= zoneRefreshCount)
                    break;
                if (sceneService.canBirthForGvgNpcAndResource(serverId, point)) {
                    GvgResNode gvgResNode = createGvgResNode(serverId, point);
                    if (gvgResNode != null) {
                        sceneService.add(gvgResNode, false);
                        nodes.add(gvgResNode);

                        // 多地格处理
                        initGvgResCoverArea(gvgResNode);
                    }
                } else {
                    logger.warn("GVG战场物资点:{} {}不可刷新", point.getX(), point.getY());
                }
            }
        }

        if (!nodes.isEmpty()) {
            logger.warn("GVG战场物资点 可刷{} 刷新{} 用时{}", zoneRefreshCount, nodes.size(), (TimeUtil.getNow() - now));
        }

        return nodes.size();
    }

    /**
     * 初始化gvg物资点占地块
     */
    private void initGvgResCoverArea(GvgResNode gvgResNode) {
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        GvgBuildingMeta meta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.ZiYuan.getId());
        List<Point> resSizePoints = gvgBuildingConfig.getGvgResSizePoints(meta, gvgResNode.getPosition());
        if (JavaUtils.bool(resSizePoints)) {
            for (Point position : resSizePoints) {
                SceneCoverNode gvgResCover = new SceneCoverNode(SceneCoverType.GVG_RES, gvgResNode.getPersistKey());
                gvgResCover.setCurrentServerId(gvgResNode.getCurrentServerId());
                gvgResCover.setPosition(position);
                try {
                    // 永远不广播
                    sceneService.add(gvgResCover, true);
                }catch (ExpectedException ignored) {

                } catch (Exception e) {
                    ErrorLogUtil.exceptionLog("GVG物资点多地格报错", e,"x",gvgResNode.getPosition().getX(), "y",gvgResNode.getPosition().getY());
                }
            }
        }
    }

    /**
     * 刷新GVG战场的僵尸
     */
    public int refreshGvgNpc(GvgBuildingMeta meta) {
        if (meta == null || meta.getBuildingPosition() == null) {
            return 0;
        }

        long now = TimeUtil.getNow();
        Set<Point> points = srvDpd.getGVGStrongHoldService().getGvgBuildingRefreshNpcPoint(meta);
        if (!JavaUtils.bool(points)) {
            return 0;
        }

        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        // 获取地图上僵尸总数
        int totalCount = gvgSettingConfig.getGvgNpcTotalCount();
        // 获取地图上现有的gvg战场npc个数
        int curCount = 0;
        Collection<GvgNpcNode> gvgNpcNodes = gvgNpcNodeDao.findAll();
        if (JavaUtils.bool(gvgNpcNodes)) {
            curCount = gvgNpcNodes.size();
        }

        // 可刷新怪物数量
        int canRefreshCount = Math.min(gvgSettingConfig.getRefreshNpcNum(), points.size());
        canRefreshCount = Math.min(totalCount - curCount, canRefreshCount);

        // 随机
        List<Point> pointList = new ArrayList<>(points);
        List<Point> retPointList = RandomUtils.randomSelectEvenly(pointList, canRefreshCount);
        if (JavaUtils.bool(retPointList)) {
            int serverId = Application.getServerId();
            List<GvgNpcNode> nodes = new ArrayList<>();
            for (Point point : retPointList) {
                if (sceneService.canBirthForGvgNpcAndResource(serverId, point)) {
                    GvgNpcNode gvgNpcNode = createGvgNpcNode(serverId, point);
                    if (gvgNpcNode != null) {
                        sceneService.add(gvgNpcNode, false);
                        nodes.add(gvgNpcNode);
                    }
                }
            }

            if (!nodes.isEmpty()) {
                logger.warn("GVG战场建筑:{} 周围僵尸 可刷{} 刷新{} 用时{}", meta.getId(), canRefreshCount, nodes.size(), (TimeUtil.getNow() - now));
            }
        }

        return 0;
    }

    private GvgResNode createGvgResNode(int serverId, Point point) {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);

        GVGBattleFieldTimeLine gvgBattleFieldTimeLine = gvgBattleFieldTimeLineDao.find();
        long nextRefreshTime = TimeUtil.getNow() + 7 * TimeUtil.DAY_MILLIS;
        if (gvgBattleFieldTimeLine != null) {
            nextRefreshTime = gvgBattleFieldTimeLine.getEndTime();
        }
        long gvgGatherResourceMax = gvgSettingConfig.getGvgGatherResourceMax();
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        GvgBuildingMeta gvgBuildingMeta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.ZiYuan.getId());
        if (gvgBuildingMeta != null) {
            return gvgResNodeDao.create(serverId, gvgGatherResourceMax, gvgBuildingMeta.getId(), nextRefreshTime, point);
        }

        return null;
    }

    private GvgNpcNode createGvgNpcNode(int serverId, Point point) {
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        String npcMetaId = gvgSettingConfig.getRefreshNpcId();
        if (npcMetaId != null) {
            GvgNpcNode gvgNpcNode = gvgNpcNodeDao.create(serverId, point, npcMetaId, 0L);
            return gvgNpcNode;
        }

        return null;
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-19 02:43:35.629
     * @Param [role, mapGrid]
     * @Return boolean
     * @Description 检查地块是否可用来生成事件
     */
    private boolean gridValidCheck(Role role, MapGrid mapGrid) {
        if (!worldAreaService.canEnterArea(role.getCurrentServerId(), mapGrid.getPosition().getX(), mapGrid.getPosition().getY())) {
            return false;
        }
        if (!srvDpd.getKvkSeasonService().isCanEnterArea(role.getoServerId(), role.getCurrentServerId(), mapGrid.getPosition().getX(), mapGrid.getPosition().getY())) {
            return false;
        }

        return true;
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-27 16:33:22.702
     * @Param [role, mapGrid]
     * @Return boolean
     * @Description 可创建返回true
     */
    private boolean gridCreateNewCheck(Role role, MapGrid mapGrid, PsSearchType type) {
        if (!sceneService.canBirthForNpcAndResourceAndBoss(role.getCurrentServerId(), mapGrid)) {
            return false;
        }

        var result = false;
        switch (type) {
            case NPC:
                result = mapGrid.isRefreshNpc();
                break;
            case New_Res:
                result = mapGrid.isRefreshRes();
                break;
            case WORLD_BOSS:
                result = mapGrid.isRefreshBoss();
                break;
            default:
        }

        return result;
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-27 16:33:22.702
     * @Param [role, mapGrid]
     * @Return boolean
     * @Description 有行军返回true
     */
    private boolean gridArmCheck(int serverId, MapGrid mapGrid) {
        // 有行军
        var arms = armyDao.getArmysByTargetPoint(serverId, mapGrid.getPosition());
        if (arms != null && !arms.isEmpty()) {
            return true;
        }

        return false;
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-03 14:51:16.934
     * @Param [player, type, id, level, unminedOnly]
     * @Return void
     * @Description 搜索请求处理
     */
    public void searchNodeHandler(Player player, PsSearchType type, int id, int level, boolean unminedOnly) {
        String key = type.name() + "_" + level + "_" + id + unminedOnly;
        var targetList = searchNode(player, type, id, level, unminedOnly);
        Role role = worldService.getWorld().getRole(player.getRoleId());
        sendGcMapSearch(role, targetList, key);
    }

    /**
     * <AUTHOR>
     * @Date 2024-05-03 14:46:34.945
     * @Param [player, type, id, level, unminedOnly]
     * @Return 目标列表 失败返回空列表
     * @Description 查找地图上的资源或npc 资源田返回规则 1、搜范围内的空的资源田；2、搜范围内的非友方的资源田
     */
    public List<SceneNode> searchNode(Player player, PsSearchType type, int id, int level, boolean unminedOnly) {
        List<SceneNode> targetList = new ArrayList<>();
        if (id < 0) {
            ErrorLogUtil.errorLog("searchNode id invalid", "roleId",player.getId(), "type",type.name(), "id",id, "level",level);
            return targetList;
        }

        if (level < 1) {
            ErrorLogUtil.errorLog("searchNode level invalid", "roleId",player.getId(), "type",type.name(), "id",id, "level",level);
            return targetList;
        }

        // 获取搜索配置
        SettingConfig config = configService.getConfig(SettingConfig.class);
        var searchRange = new int[3];
        switch (type) {
            case NPC:
                searchRange = config.getMapSearchRangeNpc();
                break;
            case WORLD_BOSS:
                searchRange = config.getMapSearchRangeWorldBoss();
                break;
            case NPC_CENTER:
                searchRange = config.getMapSearchRangeWorldBoss();
                break;
            case New_Res:
                searchRange = config.getMapSearchRangeRes();
                break;
            default:
                ErrorLogUtil.errorLog("searchNode type not support", "type",type);
                return targetList;
        }

        // 判断配置是否合法
        if (searchRange.length != 3) {
            ErrorLogUtil.errorLog("searchNode config invalid", "roleId",player.getId(), "type",type.name(), "id",id, "level",level);
            return targetList;
        }
        var maxLevel = searchRange[0];
        var lowRange = searchRange[1];
        var highRange = searchRange[2];
        var autoCreate = false;
        var range = 0;
        if (level > maxLevel) {
            range = highRange;
        } else {
            autoCreate = true;
            range = lowRange;
        }

        switch (type) {
            case NPC:
                return searchNodeNpc(player, id, level, range, autoCreate);
            case WORLD_BOSS:
                return searchNodeWorldBoss(player, id, level, range, autoCreate);
            case NPC_CENTER:
                return searchNodeNpcCenter(player, id, level, range);
            case New_Res:
                return searchNodeRes(player, id, level, range, true, unminedOnly);
            default:
                ErrorLogUtil.errorLog("searchNode type not support", "type",type);
                return targetList;
        }
    }

    public List<SceneNode> searchNodeNpc(Player player, int id, int level, int range, boolean autoCreate) {
        RoleCity city = roleCityManager.getRoleCity(player.getId());
        MapData mapData = worldService.getWorld().getMapData(player.getCurrentServerId());

        // 可刷新新资源田的
        List<MapGrid> canRefreshMapGrids = new ArrayList<>();
        MapGridLoopIterator searchItr = new MapGridLoopIterator(mapData, city.getX(), city.getY(), range);
        var npcConf = configService.getConfig(NpcConfig.class);
        Role role = worldService.getWorld().getRole(player.getRoleId());
        int roleLevel = role.getLevel();
        List<SceneNode> targetList = new ArrayList<>();
        while (searchItr.hasNext()) {
            MapGrid mapGrid = searchItr.next();
            // 不可达跳过
            if (!gridValidCheck(role, mapGrid)) {
                continue;
            }

            // 可生成跳过
            if (gridCreateNewCheck(role, mapGrid, PsSearchType.NPC)) {
                canRefreshMapGrids.add(mapGrid);
                continue;
            }

            // 有行军跳过
            if (gridArmCheck(role.getCurrentServerId(), mapGrid)) {
                continue;
            }

            // 检查地块属性
            SceneNode node = sceneService.getSceneNode(mapData.getServerId(), mapGrid.getPosition());
            if (node == null || node.getNodeType() != SceneNodeType.NPC) {
                continue;
            }
            NpcNode npcNode = (NpcNode) node;
            NpcMeta npcMeta = npcConf.get(npcNode.getNpcMetaId());
            if (npcMeta == null) {
                continue;
            }

            // 别人创建的怪 才会跳过
            if (npcNode.getOwnerId() > 0 && npcNode.getOwnerId() != player.getRoleId()) {
                continue;
            }
            if (npcMeta.getType().getId() == id && npcMeta.getLevel() == level) {
                targetList.add(node);
            }
        }

        // 没有搜索到则生成一个
        if (targetList.isEmpty() && autoCreate && configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            MetaServerType serverType = ServerConfigManager.getInstance().getServerTypeConfig().getMetaServerType(role.getCurrentServerId());
            MapGrid mapGrid = RandomUtils.randomItem(canRefreshMapGrids);
            if (mapGrid != null) {
                List<NpcMeta> npcMetas = npcConf.getFromTypeLevel(serverType, NpcType.findById(id), level);
                NpcMeta npcMeta = RandomUtils.random(npcMetas);
                NpcNode npcNode = npcNodeDao.create("0", TimeUtil.getNow() + NPC_REFRESH_TIME,
                        mapData.getServerId(), mapGrid.getPosition(), npcMeta.getId(), player.getRoleId());
                sceneService.add(npcNode, false);
                targetList.add(npcNode);
                player.sendBatchMsg();
            }
        }

        return targetList;
    }

    public List<SceneNode> searchNodeWorldBoss(Player player, int id, int level, int range, boolean autoCreate) {
        RoleCity city = roleCityManager.getRoleCity(player.getId());
        MapData mapData = worldService.getWorld().getMapData(player.getCurrentServerId());

        // 可刷新新资源田的
        List<MapGrid> canRefreshMapGrids = new ArrayList<>();
        MapGridLoopIterator searchItr = new MapGridLoopIterator(mapData, city.getX(), city.getY(), range);
        Role role = worldService.getWorld().getRole(player.getRoleId());
        List<SceneNode> targetList = new ArrayList<>();
        while (searchItr.hasNext()) {
            MapGrid mapGrid = searchItr.next();
            // 不可达跳过
            if (!gridValidCheck(role, mapGrid)) {
                continue;
            }

            // 可生成跳过
            if (gridCreateNewCheck(role, mapGrid, PsSearchType.WORLD_BOSS)) {
                canRefreshMapGrids.add(mapGrid);
                continue;
            }

            // 有行军跳过
            if (gridArmCheck(role.getCurrentServerId(), mapGrid)) {
                continue;
            }

            // 检查地块属性
            SceneNode node = sceneService.getSceneNode(mapData.getServerId(), mapGrid.getPosition());
            if (node == null || node.getNodeType() != SceneNodeType.WORLD_BOSS) {
                continue;
            }
            WorldBossNode worldBossNode = (WorldBossNode) node;
            WorldBossMeta worldBossMeta = configService.getConfig(WorldBossConfig.class).getById(worldBossNode.getMetaId());
            if (worldBossMeta == null) {
                continue;
            }
            if (worldBossMeta.getType().getId() != id) {
                continue;
            }
            if (worldBossMeta.getLevel() != level) {
                continue;
            }
            targetList.add(node);
        }

        // 没有搜索到则生成一个
        if (targetList.isEmpty() && autoCreate && configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            var metaServerType = ServerConfigManager.getInstance().getServerTypeConfig().getMetaServerType(role.getCurrentServerId());
            var bossMeta = configService.getConfig(WorldBossConfig.class).getByTypeAndLevel(metaServerType, id, level);
            if (bossMeta != null && !canRefreshMapGrids.isEmpty()) {
                MapGrid mapGrid = RandomUtils.random(canRefreshMapGrids);
                var boss = worldBossService.createWorldBoss(bossMeta, role.getCurrentServerId(), mapGrid.getPosition());
                sceneService.add(boss, false);
                targetList.add(boss);
            }
        }

        return targetList;
    }

    public List<SceneNode> searchNodeNpcCenter(Player player, int id, int level, int range) {
        RoleCity city = roleCityManager.getRoleCity(player.getId());
        MapData mapData = worldService.getWorld().getMapData(player.getCurrentServerId());

        MapGridLoopIterator searchItr = new MapGridLoopIterator(mapData, city.getX(), city.getY(), range);
        var npcConf = configService.getConfig(NpcConfig.class);
        Role role = worldService.getWorld().getRole(player.getRoleId());
        List<SceneNode> targetList = new ArrayList<>();
        while (searchItr.hasNext()) {
            MapGrid mapGrid = searchItr.next();
            // 不可达跳过
            if (!gridValidCheck(role, mapGrid)) {
                continue;
            }

            // 有行军跳过
            if (gridArmCheck(role.getCurrentServerId(), mapGrid)) {
                continue;
            }

            // 检查地块属性
            SceneNode node = sceneService.getSceneNode(mapData.getServerId(), mapGrid.getPosition());
            if (node == null || node.getNodeType() != SceneNodeType.NPC_CENTER) {
                continue;
            }
            NpcCenterNode npcCenterNode = (NpcCenterNode) node;
            NpcMeta centerMeta = npcConf.get(npcCenterNode.getNpcMetaId());
            if (centerMeta == null || centerMeta.getLevel() != level) {
                continue;
            }
            targetList.add(node);
        }

        // npc_center 没有新生成逻辑
        if (targetList.isEmpty()) {
            ErrorLogUtil.errorLog("searchNodeNpcCenter result is empty", "roleId",player.getId(),  "id",id, "level",level);
        }

        return targetList;
    }

    public List<SceneNode> searchNodeRes(Player player, int id, int level, int range, boolean autoCreate, boolean unminedOnly) {
        RoleCity city = roleCityManager.getRoleCity(player.getId());
        MapData mapData = worldService.getWorld().getMapData(player.getCurrentServerId());

        // 可刷新新资源田的
        List<MapGrid> canRefreshMapGrids = new ArrayList<>();
        MapGridLoopIterator searchItr = new MapGridLoopIterator(mapData, city.getX(), city.getY(), range);
        var resRefreshConfig = configService.getConfig(NewResRefreshConfig.class);
        var resGrowConfig = configService.getConfig(NewResGrowConfig.class);
        Role role = worldService.getWorld().getRole(player.getRoleId());
        KvkNewResRefreshConfig kvkResRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
        List<SceneNode> targetList = new ArrayList<>();
        while (searchItr.hasNext()) {
            MapGrid mapGrid = searchItr.next();
            // 不可达跳过
            if (!gridValidCheck(role, mapGrid)) {
                continue;
            }

            // 可生成跳过
            if (gridCreateNewCheck(role, mapGrid, PsSearchType.New_Res)) {
                canRefreshMapGrids.add(mapGrid);
                continue;
            }

            // 有行军跳过
            if (gridArmCheck(role.getCurrentServerId(), mapGrid)) {
                continue;
            }

            // 检查地块属性
            SceneNode node = sceneService.getSceneNode(mapData.getServerId(), mapGrid.getPosition());
            if (node == null || node.getNodeType() != SceneNodeType.NEW_RES) {
                continue;
            }
            NewResNode newResNode = (NewResNode) node;
            int resLevel = 0;
            Currency resType = null;
            if (Application.getServerType(newResNode.getCurrentServerId()) == ServerType.KVK_SEASON) {
                var kvkNewResRefreshMeta = kvkResRefreshConfig.get(newResNode.getMetaId());
                if (kvkNewResRefreshMeta == null) {
                    continue;
                }
                resLevel = kvkNewResRefreshMeta.getLevel();
                resType = Currency.findById(kvkNewResRefreshMeta.getType());
            } else {
                NewResRefreshMeta newResRefreshMeta = resRefreshConfig.get(newResNode.getMetaId());
                if (newResRefreshMeta == null) {
                    continue;
                }
                resLevel = newResRefreshMeta.getLevel();
                resType = newResRefreshMeta.getType();
            }
            if (resType == null) {
                continue;
            }

            // 如果已经被占领则跳过
            if (JavaUtils.bool(newResNode.getRoleId())) {
                continue;
            }

            // 如果归属不是自己联盟则跳过
            if (newResNode.getAllianceId() != null && !newResNode.getAllianceId().equals(role.getAllianceId())) {
                continue;
            }

            // 如果只查找未被开采过 则过滤掉被占领过的矿
            if (unminedOnly && newResNode.isBeenUsed()) {
                continue;
            }

            if (resType.getId() == id && resLevel == level) {
                // 如果过期了 ，重置下时间
                if (newResNode.isBeenUsed() && newResNode.getNextRefreshTime() < TimeUtil.getNow()) {
                    newResNode.setNextRefreshTime(TimeUtil.getNow() + resGrowConfig.getNewResRefreshTime(resLevel) * TimeUtils.SECONDS_MILLIS);
                    newResNodeDao.save(newResNode);
                }

                targetList.add(node);
            }
        }

        // 没有搜索到则生成一个
        if (targetList.isEmpty() && autoCreate && configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            var node = refreshNewRes(role, id, level, canRefreshMapGrids);
            if (node != null) {
                targetList.add(node);
            }
        }
        if (!JavaUtils.bool(targetList)) {
            mapSearchResFailTips(role, level);
        }
        return targetList;
    }

    public void sendGcMapSearch(Role role, List<SceneNode> targetList, String key) {
        GcMapSearch msg = new GcMapSearch();

        // 如果searchKey超过1分钟没有使用，则重置
        if (role.getSearchKeyUpdateTime() + 60 * TimeUtils.SECONDS_MILLIS < TimeUtil.getNow()) {
            role.getSearchKey().clear();
        }
        int index = role.getSearchKey().getOrDefault(key, 0);
        // 收集目标点 并 按距离排序
        RoleCity city = roleCityManager.getRoleCity(role.getId());
        targetList.sort(Comparator.comparing(m -> {
            return Math.pow(city.getX() - m.getX(), 2) + Math.pow(city.getY() - m.getY(), 2);
        }));
        if (JavaUtils.bool(targetList)) {
            var resultNode = targetList.get(0);
            if (index >= 0 && index < targetList.size()) {
                resultNode = targetList.get(index);
                index++;
            } else {
                index = 0;
            }
            role.getSearchKey().put(key, index);
            role.setSearchKeyUpdateTime(TimeUtil.getNow());
            msg.setX(resultNode.getX());
            msg.setY(resultNode.getY());
            msg.setNodeId(resultNode.toPsMapNodeId());
        }
        role.send(msg);
    }

    /**
     * 战斗结束后刷新可攻击npc最高等级
     *
     * @param army
     */
    public void onFinishBattle(ArmyInfo army) {
        if (army.getRoleId() == null) {
            return;
        }
        Role armyRole = army.getOwner();
        switch (army.getArmyType()) {
            case ATTACK_MONSTER:
            case AUTO_HUNTING:
            case NEW_RES_PVE:
                if (army.getFightContext().isWin()) {
                    int monsterLevel = army.getFightContext().getMonsterLevel();
                    RoleExtra roleExtra = roleExtraManager.getRoleExtra(armyRole.getPersistKey());
                    if (monsterLevel >= roleExtra.getNpcCanAttackLevel()) {
                        roleExtra.setNpcCanAttackLevel(monsterLevel + 1);
                        if (armyRole.isOnline()) {
                            this.mapSearchInfo(armyRole);
                        }
                        roleExtraManager.saveRoleExtra(roleExtra);

                        srvDpd.getMissionService().onMissionFinish(armyRole, MissionType.KILL_NPC_MAX_LEVEL, armyRole);
                        srvDpd.getWorldExploreEventService().attackNpcMaxLevelSendMessage(monsterLevel, armyRole);
                    }
                }
                break;
            default:
                break;
        }
    }

    /**
     * 搜索地图前置信息
     *
     * @param role
     */
    public void mapSearchInfo(Role role) {
        int openDay = this.srvDpd.getServerInfoService().getOpenServerDay(role.getCurrentServerId());
        int dayMaxLevel = configService.getConfig(NpcLevelConfig.class).getMaxLevelByDay(openDay);
        KvkNpcLevelConfig kvkNpcLevelConfig = configService.getConfig(KvkNpcLevelConfig.class);
        if (Application.getConfigCenter().getServerType(role.getCurrentServerId()) == ServerType.KVK_SEASON) {
            dayMaxLevel = kvkNpcLevelConfig.getMaxLevelByDay(openDay);
        }
        // 可以攻击的怪,最低等级为1
        RoleExtra roleExtra = roleExtraManager.getRoleExtra(role.getId());
        if (roleExtra.getNpcCanAttackLevel() < 1) {
            int initAtkNpcLevel = configService.getConfig(SettingConfig.class).getInitAtkNpcLevel();
            roleExtra.setNpcCanAttackLevel(initAtkNpcLevel);
            roleExtraManager.saveRoleExtra(roleExtra);

            srvDpd.getMissionService().onMissionFinish(role, MissionType.KILL_NPC_MAX_LEVEL, role);
        }
        GcMapSearchInfo msg = new GcMapSearchInfo();
        msg.setNpcLevelLimit(dayMaxLevel);
        msg.setNpcCanAttackLevel(Math.min(dayMaxLevel, roleExtra.getNpcCanAttackLevel()));
        role.send(msg);
    }

    @Override
    public void shutdown() {

    }

    /**
     * 放弃新的资源点
     */
    public void giveUpNewRes(Role role, NewResNode newResNode) {
        // 判断归属
        if (!newResNode.getRoleId().equals(role.getId())) {
            ErrorLogUtil.errorLog("Role giveUpNewRes resNode is not belong", "role",role.getId(), "resNode",newResNode.getRoleId());
            return;
        }
        KvkNewResRefreshConfig kvkResRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
        Currency type = null;
        if (Application.getServerType(newResNode.getCurrentServerId()) == ServerType.KVK_SEASON) {
            var kvkNewResRefreshMeta = kvkResRefreshConfig.get(newResNode.getMetaId());
            if (kvkNewResRefreshMeta == null) {
                ErrorLogUtil.errorLog("Role giveUpNewRes kvkNewResRefreshMeta is null", "roleId",role.getId(), "metaId",newResNode.getMetaId());
                return;
            }
            type = Currency.findById(kvkNewResRefreshMeta.getType());
        } else {
            NewResRefreshMeta resRefreshMeta = configService.getConfig(NewResRefreshConfig.class).get(newResNode.getMetaId());
            if (resRefreshMeta == null) {
                ErrorLogUtil.errorLog("Role giveUpNewRes NewResRefreshMeta is null", "roleId",role.getId(), "metaId",newResNode.getMetaId());
                return;
            }
            type = resRefreshMeta.getType();
        }
        if (type == null) {
            return;
        }

        // 召回采集部队,在采集内部结算采集量
        if (JavaUtils.bool(newResNode.getArmyId())) {
            ArmyInfo armyInfo = armyManager.findById(newResNode.getArmyId());
            if (armyInfo != null) {
                gatherService.exploiting(armyInfo, true, true);
            }
        }

        // 更新索引
        newResNodeDao.changeIndex(newResNode, 0L);

        // 清空归属和采集部队信息
        newResNode.setArmyId(0L);
        newResNode.setOutPutTime(0L);
        newResNodeDao.save(newResNode);

        // BI
        biLogUtil.giveUpNewResNode(role, newResNode.getMetaId(), 0);

        GcGiveUpNewRes msg = new GcGiveUpNewRes();
        msg.setResult(0);
        role.send(msg);

        sceneService.update(newResNode, null);

        logger.info("Role:{} giveUpNewRes resNode:{} meta:{} success", role.getId(), newResNode.getPersistKey(), newResNode.getMetaId());
    }

    /**
     * 放弃所有资源田，迁服用
     */
    public void giveUpAllNewRes(Role role) {
        Collection<NewResNode> newResNodes = newResNodeDao.findByRoleId(role.getId());
        if (!JavaUtils.bool(newResNodes)) {
            return;
        }
        // 需要更新速度的资源类型
        Set<Currency> needUpdateCurrency = new HashSet<>();
        KvkNewResRefreshConfig kvkResRefreshConfig = configService.getConfig(KvkNewResRefreshConfig.class);
        for (NewResNode newResNode : newResNodes) {
            Currency type = null;
            if (Application.getServerType(newResNode.getCurrentServerId()) == ServerType.KVK_SEASON) {
                var kvkNewResRefreshMeta = kvkResRefreshConfig.get(newResNode.getMetaId());
                if (kvkNewResRefreshMeta == null) {
                    ErrorLogUtil.errorLog("Role giveUpNewRes KvkNewResRefreshMeta is null", "roleId",role.getId(), "metaId",newResNode.getMetaId());
                    continue;
                }
                type = Currency.findById(kvkNewResRefreshMeta.getType());
            } else {
                NewResRefreshMeta resRefreshMeta = configService.getConfig(NewResRefreshConfig.class).get(newResNode.getMetaId());
                if (resRefreshMeta == null) {
                    ErrorLogUtil.errorLog("Role giveUpNewRes NewResRefreshMeta is null", "roleId",role.getId(), "metaId",newResNode.getMetaId());
                    continue;
                }
                type = resRefreshMeta.getType();
            }
            if (type == null) {
                continue;
            }
            // 召回采集部队,在采集内部结算采集量
            if (JavaUtils.bool(newResNode.getArmyId())) {
                ArmyInfo armyInfo = armyManager.findById(newResNode.getArmyId());
                if (armyInfo != null) {
                    gatherService.exploiting(armyInfo, true, true);
                }
            }

            // 更新索引
            newResNodeDao.changeIndex(newResNode, 0L);

            // 清空归属和采集部队信息
            newResNode.setArmyId(0L);
            newResNode.setOutPutTime(0L);
            newResNodeDao.save(newResNode);

            // BI
            biLogUtil.giveUpNewResNode(role, newResNode.getMetaId(), 0);

            GcGiveUpNewRes msg = new GcGiveUpNewRes();
            msg.setResult(0);
            role.send(msg);

            if (newResNode.isStation()) {
                needUpdateCurrency.add(type);
            }

            sceneService.update(newResNode, null);

            logger.info("Role:{} giveUpNewRes resNode:{} meta:{} success", role.getId(), newResNode.getPersistKey(), newResNode.getMetaId());
        }

        // 更新产出速度
        needUpdateCurrency.forEach(currency -> resourceOutputManager.updateResourceOutputSpeed(role.getId(), currency));
    }

    /**
     * 清理过期的低语者活动怪物
     *
     * @param forceClear 强制全清
     */
    public void clearActivityWhispererNpc(boolean forceClear) {
        asyncOperationService.execute(new ActivityWhispererClearOperation(srvDpd.getWhispererActivityService(), forceClear));
    }

    public void testGvgMap() {
        logger.warn("###############GVG战场物资点刷新开始测试#####################");

        // 物资点坐标配置
        GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
        int[] pointsConfig = gvgSettingConfig.getRefreshResPoints();
        // 坐标
        Map<Integer, Set<Point>> indexPoints = new HashMap<>();
        // 每一个区域刷新数量
        Map<Integer, Integer> indexCounts = new HashMap<>();
        int index = 1;
        for (int i = 0; i < pointsConfig.length; i = i + 4) {
            int x = pointsConfig[i];
            int y = pointsConfig[i + 1];
            int size = pointsConfig[i + 2];
            int count = pointsConfig[i + 3];
            Set<Point> points = new HashSet<>();
            for (int m = 0; m <= size; m++) {
                for (int n = 0; n <= size; n++) {
                    int px = x + m;
                    int py = y + n;
                    points.add(Point.getInstance(px, py));
                }
            }
            indexPoints.put(index, points);
            indexCounts.put(index, count);
            index++;
        }

        // 当前刷新总数量
        Set<Point> allPoints = new HashSet<>();
        for (Integer key : indexPoints.keySet()) {
            List<Point> pointList = new ArrayList<>(indexPoints.get(key));
            List<Point> retPointList = RandomUtils.randomSelectEvenly(pointList, indexCounts.get(key));
            for (Point point : retPointList) {
                if (allPoints.contains(point)) {
                    ErrorLogUtil.errorLog("========物资点报错,坐标重复======", "pointX",point.getX(), "pointY",point.getY());
                } else {
                    allPoints.add(point);
                }
            }
        }
    }

    /**
     * 初始化gvg物资点占地块
     */
    private void testGvgResCoverArea(Point point, Set<Point> allPoints) {
        GvgBuildingConfig gvgBuildingConfig = configService.getConfig(GvgBuildingConfig.class);
        GvgBuildingMeta meta = gvgBuildingConfig.getBuildingMetaByType(BuildingType.ZiYuan.getId());
        List<Point> resSizePoints = gvgBuildingConfig.getGvgResSizePoints(meta, point);
        if (JavaUtils.bool(resSizePoints)) {
            for (Point position : resSizePoints) {
                if (allPoints.contains(position)) {
                    ErrorLogUtil.errorLog("========物资点地格报错,坐标重复======", "positionX",position.getX(), "positionY",position.getY());
                } else {
                    allPoints.add(position);
                }
            }
        }
    }

    /**
     * 搜索资源田没搜到时的提示
     * @param role
     * @param searchLevel
     */
    private void mapSearchResFailTips(Role role, int searchLevel) {
        if (!configCenter.currentServerTypeIsGAME_or_KVKSEASON()) {
            return;
        }
        int resLevel = getRoleParcelLevel(role);
        if (resLevel < searchLevel) {
            srvDpd.getNoticeService().notice(role, NoticeConstants.MAP_SEARCH_RES_FAIL, false);
        }
    }

}
