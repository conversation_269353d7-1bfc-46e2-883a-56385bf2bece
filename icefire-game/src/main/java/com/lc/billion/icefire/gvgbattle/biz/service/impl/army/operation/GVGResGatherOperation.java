package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.GVGResNodeDao;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.army.ArmyWorkType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.gvgbattle.biz.model.scene.node.GvgResNode;
import com.lc.billion.icefire.game.biz.service.impl.army.ArmyOperation;
import com.lc.billion.icefire.game.biz.service.impl.army.oper.AbstractGatherOperation;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BIMarchResult;
import com.lc.billion.icefire.game.biz.service.impl.login.LoginServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.config.auto.GvgSettingConfig;
import com.lc.billion.icefire.gvgbattle.biz.model.army.GvgGatherContext;
import com.lc.billion.icefire.gvgbattle.biz.service.impl.GVGResGatherService;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 采集GVG资源点
 * 
 * <AUTHOR>
 * @date 2021/1/26
 */
@Service
public class GVGResGatherOperation extends AbstractGatherOperation {

	public static final Logger logger = LoggerFactory.getLogger(GVGResGatherOperation.class);
	@Autowired
	private GVGResNodeDao gvgResNodeDao;
	@Autowired
	private LoginServiceImpl loginService;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private GVGResGatherService gvgResGatherService;

	@Override
	public ArmyType getArmyType() {
		return ArmyType.GVG_GATHER;
	}

	@Override
	public void armyArrive(ArmyInfo army) {
		SceneNode target = armyManager.getArmyTargetNode(army);
		if (!check(army, target)) {
			armyManager.marchRetBILog(army);
			armyManager.returnArmy(army);
			return;
		}
		if (checkFight(army)) {
			army.setArmyType(ArmyType.ATTACK_GVG_GATHER);
			ArmyOperation armyOperation = armyService.getArmyOperation(army);
			armyOperation.armyArrive(army);
			return;
		}


		GvgResNode resNode = (GvgResNode) target;
		resNode.setRoleId(army.getRoleId());
		resNode.setArmyId(army.getPersistKey());
		gvgResNodeDao.save(resNode);
		//
		army.setWorkType(ArmyWorkType.GATHERING);
		// 采集上限、携带量(上限/资源重量)
		long armyCarry = armyManager.getGvgGatherCarry(army.getRoleId(), army);
		GvgSettingConfig gvgSettingConfig = configService.getConfig(GvgSettingConfig.class);
		long gatherAmount = Double.valueOf(armyCarry * 1d / gvgSettingConfig.getGvgCarryToPointRate()).longValue();
		gatherAmount = Math.min(gatherAmount, resNode.getResReversed());
		double gvgGatherSpeed = gvgSettingConfig.getGvgGatherSpeed();

		GvgGatherContext gatherContext = new GvgGatherContext(gatherAmount, gvgGatherSpeed, resNode.getMetaId());
		army.setGvgGatherContext(gatherContext);
		armyDao.save(army);
		//
		// 更新信息给前端
		armyService.updateArmyProgress(army);
		// 更新资源点信息
		sceneService.update(resNode, updater -> updater.setArmyInfo(GvgResNode.toSimpleInfo(army)));
		sceneService.remove(army);

		logger.info("玩家" + army.getRoleId() + "采集开始,预计采集量" + gatherContext.getCarry() + ",预计采集时间" + (int) (gatherContext.getReaminGatherTime() / 60) + ":"
				+ (int) (gatherContext.getReaminGatherTime() % 60) + " | " + army.getPersistKey());
		armyManager.marchRetBILog(army, BIMarchResult.VICTORY);
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		gvgResGatherService.gatherAddPoint(army);
		armyManager.takeBackArmy(army);
	}

	@Override
	protected boolean check(ArmyInfo army, SceneNode armyTargetNode) {
		if (armyTargetNode == null || armyTargetNode.getNodeType() != SceneNodeType.GVG_RES)
			return false;
		GvgResNode node = (GvgResNode) armyTargetNode;
		// 有所有者 && 是我本人
		if (node.hasOwner()){
			// 本人
			if(node.getRoleId().equals(army.getRoleId())) {
				return false;
			}

			// 同盟
			Long ownerId = node.getRoleId();
			if (JavaUtils.bool(ownerId) && JavaUtils.bool(army.getRoleId())) {
				RoleDao roleDao = Application.getBean(RoleDao.class);
				if (roleDao == null) {
					return false;
				}
				Role ownerRole = roleDao.findById(ownerId);
				Role armyRole = roleDao.findById(army.getRoleId());
				if (ownerRole != null) {
					if (Objects.equals(ownerRole.getAllianceId(), armyRole.getAllianceId())) {
						return false;
					}
				}
			}
		}


		// 这时候战斗服开始销毁
		if(!loginService.isLoginSwitch()){
			return false;
		}

		return true;
	}

	@Override
	protected boolean checkFight(ArmyInfo army) {
		SceneNode target = armyManager.getArmyTargetNode(army);
		GvgResNode resNode = (GvgResNode) target;
		Role role = army.getOwner();
		//
		// 有所有者 && 和我不是一个盟
		if (resNode.hasOwner() && !allianceService.isSameAlliance(role.getPersistKey(), resNode.getRoleId())) {
			return true;
		}
		//
		return false;
	}
}
