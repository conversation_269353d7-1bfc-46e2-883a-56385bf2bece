package com.lc.billion.icefire.gvgbattle.biz.config.auto;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.data.util.Pair;

import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;

@Config(name = "tvtSetting")
public class TvtSettingConfig {


    private int tvtGameStartDay;

    private Map<Integer, Integer> tvtGameServerRankingSettings = Maps.newHashMap();

    private Map<Integer, Integer> tvtGameShowPointBonusS = Maps.newHashMap();

    private int tvtGameLeaderboardNum;

    private String[] tvtGameHidePointKs;

    // 初始化tvt活动每日时间点
    // <dayOfWeek,List<time>>
    private Map<Integer, List<LocalTime>> tvtActivityTimeArray = Maps.newHashMap();
    // tvt隐藏积分匹配分数段 从低分到高分
    private Pair<Integer, Integer>[] tvtGameMatchPointStandards;

    private List<GameRefreshPointSetting> tvtGameRefreshPointSettings = Lists.newArrayList();
    //tvt 单场报名最大人数
    private int tvtGameRegistrationLimit;
    //基础匹配组内的人数，完成组内随机匹配，不能跨分数段匹配
    private int tvtGameMatchOneGroupNum;
    //单场战斗服最少人数，双方加一起总和
    private int tvtGameOneMatchNumLimit;
    //单场战斗服最多人数, 双方加一起总和
    private int tvtGameOneMatchNumLimitMax;

    //首次参与活动的时候，折合竞技场分数的系数
    private float tvtGameFirstExchangeRate;
    //未在竞技场参与活动，或竞技场积分为0时。默认计算第一次隐藏分时的竞技场积分
    private int tvtGameErrorHidePointSetting;
    //tvt匹配失败发送的邮件metaId
    private String tvtGameMatchErrorMailSetting;

    private int tvtGameExitTimeSetting;

    public String getTvtGameMatchErrorMailSetting() {
        return tvtGameMatchErrorMailSetting;
    }

    public void setTvtGameMatchErrorMailSetting(String tvtGameMatchErrorMailSetting) {
        this.tvtGameMatchErrorMailSetting = tvtGameMatchErrorMailSetting;
    }

    public int getTvtGameRegistrationLimit() {
        return tvtGameRegistrationLimit;
    }

    public List<LocalTime> getTvtActivityTimeByWeek(int dayOfWeek) {
        return tvtActivityTimeArray.get(dayOfWeek);
    }

    public void init(JsonNode json) {

        tvtGameHidePointKs = MetaUtils.parse(json.get("tvtGameHidePointK").asText(), AbstractMeta.META_SEPARATOR_1);

        // 2000|2300;0.4;100,2300|2600;0.4;200
        String[] pointSettings = MetaUtils.parse(json.get("tvtGameRefreshPointSetting").asText(), AbstractMeta.META_SEPARATOR_1);

        for (String pointSetting : pointSettings) {
            GameRefreshPointSetting gameRefreshPointSetting = new GameRefreshPointSetting();
            String[] settings = MetaUtils.parse(pointSetting, ';');
            String[] scoreRange = MetaUtils.parse(settings[0], '|');
            gameRefreshPointSetting.setScoreUp(Integer.parseInt(scoreRange[0]));
            gameRefreshPointSetting.setScoreDown(Integer.parseInt(scoreRange[1]));
            gameRefreshPointSetting.setCoefficient1(Double.parseDouble(settings[1]));
            gameRefreshPointSetting.setCoefficient2(Integer.parseInt(settings[2]));
            tvtGameRefreshPointSettings.add(gameRefreshPointSetting);
        }

        // tvtGameShowPointBonus
        String[] bonusStrings = MetaUtils.parse(json.get("tvtGameShowPointBonus").asText(), AbstractMeta.META_SEPARATOR_1);
        for (String string : bonusStrings) {
            String[] parses = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_3);
            if (parses.length == 2) {
                if (parses[0].contains("-")) {
                    String[] ids = MetaUtils.parse(parses[0], '-');
                    if (ids.length == 2) {
                        for (int i = Integer.parseInt(ids[0]); i <= Integer.parseInt(ids[1]); i++) {
                            tvtGameShowPointBonusS.put(i, Integer.parseInt(parses[1]));
                        }
                    }
                } else {
                    tvtGameShowPointBonusS.put(Integer.parseInt(parses[0]), Integer.parseInt(parses[1]));
                }
            }
        }

        // tvtGameServerRankingSetting
        int currentIndex = 0;
        String[] strings = MetaUtils.parse(json.get("tvtGameServerRankingSetting").asText(), AbstractMeta.META_SEPARATOR_1);
        for (String string : strings) {
            String[] parses = MetaUtils.parse(string, AbstractMeta.META_SEPARATOR_2);
            for (String parse : parses) {
                if (parse.contains("-")) {
                    String[] ids = MetaUtils.parse(parse, '-');
                    if (ids.length == 2) {
                        for (int i = Integer.parseInt(ids[0]); i <= Integer.parseInt(ids[1]); i++) {
                            tvtGameServerRankingSettings.put(i, currentIndex);
                        }
                    }
                } else {
                    tvtGameServerRankingSettings.put(Integer.parseInt(parse), currentIndex);
                }
            }
            currentIndex++;
        }

        // 初始化 tvt活动时间点
        for (int i = 1; i <= 7; ++i) {
            String timeSettingStr[] = MetaUtils.parse(json.get("tvtGameTimeSetting" + i).asText(), AbstractMeta.META_SEPARATOR_2);
            List<LocalTime> times = new ArrayList<>();
            for (String foo : timeSettingStr) {
                LocalTime localTime = LocalTime.parse(foo, DateTimeFormatter.ISO_LOCAL_TIME);
                times.add(localTime);
            }
            tvtActivityTimeArray.put(i, times);
        }

        // 初始化 tvt隐藏分 匹配分数段
        String[] matchPoints = MetaUtils.parse(json.get("tvtGameMatchPointStandard").asText(), AbstractMeta.META_SEPARATOR_1);
        tvtGameMatchPointStandards = new Pair[matchPoints.length];
        for (int i = 0; i < matchPoints.length; ++i) {
            String[] mp = MetaUtils.parse(matchPoints[i], AbstractMeta.META_SEPARATOR_2);
            tvtGameMatchPointStandards[i] = Pair.of(Integer.parseInt(mp[0]), Integer.parseInt(mp[1]));
        }

        tvtGameRegistrationLimit = json.get("tvtGameRegistrationLimit").asInt();
        tvtGameMatchOneGroupNum = json.get("tvtGameMatchOneGroupNum").asInt();
        tvtGameOneMatchNumLimit = json.get("tvtGameOneMatchNumLimit").asInt();
    }

    public int getTvtGameStartDay() {
        return tvtGameStartDay;
    }

    public void setTvtGameStartDay(int tvtGameStartDay) {
        this.tvtGameStartDay = tvtGameStartDay;
    }

    public Map<Integer, Integer> getTvtGameServerRankingSettings() {
        return tvtGameServerRankingSettings;
    }

    public void setTvtGameServerRankingSettings(Map<Integer, Integer> tvtGameServerRankingSettings) {
        this.tvtGameServerRankingSettings = tvtGameServerRankingSettings;
    }

    public String[] getTvtGameHidePointKs() {
        return tvtGameHidePointKs;
    }

    public void setTvtGameHidePointKs(String[] tvtGameHidePointKs) {
        this.tvtGameHidePointKs = tvtGameHidePointKs;
    }

    public Map<Integer, Integer> getTvtGameShowPointBonusS() {
        return tvtGameShowPointBonusS;
    }

    public void setTvtGameShowPointBonusS(Map<Integer, Integer> tvtGameShowPointBonusS) {
        this.tvtGameShowPointBonusS = tvtGameShowPointBonusS;
    }

    public int getTvtGameLeaderboardNum() {
        return tvtGameLeaderboardNum;
    }

    public void setTvtGameLeaderboardNum(int tvtGameLeaderboardNum) {
        this.tvtGameLeaderboardNum = tvtGameLeaderboardNum;
    }

    public Map<Integer, List<LocalTime>> getTvtActivityTimeArray() {
        return tvtActivityTimeArray;
    }

    public void setTvtActivityTimeArray(Map<Integer, List<LocalTime>> tvtActivityTimeArray) {
        this.tvtActivityTimeArray = tvtActivityTimeArray;
    }

    public List<GameRefreshPointSetting> getTvtGameRefreshPointSettings() {
        return tvtGameRefreshPointSettings;
    }

    public void setTvtGameRefreshPointSettings(List<GameRefreshPointSetting> tvtGameRefreshPointSettings) {
        this.tvtGameRefreshPointSettings = tvtGameRefreshPointSettings;
    }

    public GameRefreshPointSetting getTvtGameRefreshPointSettingByScore(int score) {

        for (GameRefreshPointSetting gameRefreshPointSetting : tvtGameRefreshPointSettings) {
            if (score >= gameRefreshPointSetting.scoreUp && score < gameRefreshPointSetting.scoreDown) {
                return gameRefreshPointSetting;
            }
        }
        return null;
    }

    public class GameRefreshPointSetting {

        private int scoreUp;
        private int scoreDown;
        private double coefficient1;
        private int coefficient2;

        public int getScoreUp() {
            return scoreUp;
        }

        public void setScoreUp(int scoreUp) {
            this.scoreUp = scoreUp;
        }

        public int getScoreDown() {
            return scoreDown;
        }

        public void setScoreDown(int scoreDown) {
            this.scoreDown = scoreDown;
        }

        public double getCoefficient1() {
            return coefficient1;
        }

        public void setCoefficient1(double coefficient1) {
            this.coefficient1 = coefficient1;
        }

        public int getCoefficient2() {
            return coefficient2;
        }

        public void setCoefficient2(int coefficient2) {
            this.coefficient2 = coefficient2;
        }

    }

    public Pair<Integer, Integer>[] getTvtGameMatchPointStandard() {
        return tvtGameMatchPointStandards;
    }

    /**
     * 查找指定分数所在匹配分段，
     *
     * @param score
     * @return 返回其数组下标， 没找到返回-1
     */
    public int getMatchPointLevelByHiddenScore(int score) {
        for (int i = 0; i < this.tvtGameMatchPointStandards.length; ++i) {
            Pair<Integer, Integer> mp = tvtGameMatchPointStandards[i];
            if (mp.getFirst() <= score && score < mp.getSecond()) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 同一匹配分段内 组的大小，
     *
     * @return
     */
    public int getTvtGameMatchOneGroupNum() {
        return tvtGameMatchOneGroupNum;
    }

    /**
     * tvt比赛， 单场开赛最小比赛人数
     *
     * @return
     */
    public int getTvtGameOneMatchNumLimit() {
        return tvtGameOneMatchNumLimit;
    }

    /**
     * tvt比赛，单场开赛最大比赛人数
     * @return
     */
    public int getTvtGameOneMatchNumLimitMax() { return tvtGameOneMatchNumLimitMax; }


    public void setTvtGameRegistrationLimit(int tvtGameRegistrationLimit) {
        this.tvtGameRegistrationLimit = tvtGameRegistrationLimit;
    }

    public void setTvtGameMatchOneGroupNum(int tvtGameMatchOneGroupNum) {
        this.tvtGameMatchOneGroupNum = tvtGameMatchOneGroupNum;
    }

    public void setTvtGameOneMatchNumLimit(int tvtGameOneMatchNumLimit) {
        this.tvtGameOneMatchNumLimit = tvtGameOneMatchNumLimit;
    }

    public void setTvtGameOneMatchNumLimitMax(int tvtGameOneMatchNumLimitMax) {
        this.tvtGameOneMatchNumLimitMax = tvtGameOneMatchNumLimitMax;
    }


    public float getTvtGameFirstExchangeRate() {
        return tvtGameFirstExchangeRate;
    }

    public void setTvtGameFirstExchangeRate(float tvtGameFirstExchangeRate) {
        this.tvtGameFirstExchangeRate = tvtGameFirstExchangeRate;
    }

    public int getTvtGameErrorHidePointSetting() {
        return tvtGameErrorHidePointSetting;
    }

    public void setTvtGameErrorHidePointSetting(int tvtGameErrorHidePointSetting) {
        this.tvtGameErrorHidePointSetting = tvtGameErrorHidePointSetting;
    }

    public void setTvtGameExitTimeSetting(int tvtGameExitTimeSetting) {
        this.tvtGameExitTimeSetting = tvtGameExitTimeSetting;
    }

    public int getTvtGameExitTimeSetting() {
        return tvtGameExitTimeSetting;
    }
}
