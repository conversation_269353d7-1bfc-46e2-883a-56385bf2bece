package com.lc.billion.icefire.game.biz.service.impl.ugc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.support.HttpUtil;
import com.lc.billion.icefire.game.GameConfig;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDeviceDao;
import com.lc.billion.icefire.game.biz.manager.RoleVipManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleDevice;
import com.lc.billion.icefire.game.biz.model.vip.RoleVip;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.ugc.wechat.WechatWordCheck;
import com.lc.billion.icefire.game.biz.util.TuyooWordCheck;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class
WordCheckService {
    final static Logger logger = LoggerFactory.getLogger(WordCheckService.class);
    @Autowired
    private WechatWordCheck wechatWordCheck;
    @Autowired
    private RoleDeviceDao roleDeviceDao;
    @Autowired
    private RoleVipManager vipManager;

    private TuyooWordCheck tuyooWordCheck = new TuyooWordCheck();


    public boolean checkIfWordIsSpam(Role role, String content, boolean useVip) {
        if (wechatWordCheck.isFunctionOpen()) {
            RoleDevice roleDevice = roleDeviceDao.findById(role.getRoleId());
            if (roleDevice != null && JavaUtils.bool(roleDevice.getWxOpenId())) {
                return wechatWordCheck.checkIfWordIsSpam(roleDevice.getWxOpenId(), content);
            }
        }
        RoleVip roleVip = vipManager.getRoleVip(role);
        int vipLevel = 0;
        if(roleVip != null) vipLevel = roleVip.getVipLevel();
        return tuyooWordCheck.checkIfWordIsSpam(role.getRoleId(), content, vipLevel, useVip);
    }
    public String checkAndCleanWord(Role role, String content, boolean useVip) {
        RoleVip roleVip = vipManager.getRoleVip(role);
        int vipLevel = 0;
        if(roleVip != null) vipLevel = roleVip.getVipLevel();
        return tuyooWordCheck.checkAndReplaceWord(role.getRoleId(), content, vipLevel, useVip);
    }
    public void startService() {
        wechatWordCheck.init();
    }
    /**
     *
     * @param wxOpenId
     * @param mediaUrl
     */
    public String checkHeadImageByChat(String wxOpenId, Long roleId, String mediaUrl) {
        Map<String, Object> paramMap = new HashMap<>();
        GameConfig gameConfig = ServerConfigManager.getInstance().getGameConfig();
        String url = gameConfig.getNewChatURL() + "/image/check";
        paramMap.put("appId", gameConfig.getNewChatAppId());
        paramMap.put("uid", roleId);
        paramMap.put("url", mediaUrl);
        try {
            String responseData = HttpUtil.postJSON(url, JSON.toJSONString(paramMap));
            JSONObject response = JSONObject.parseObject(responseData);
            String errorCode = response.getString("retCode");
            String errmsg = response.getString("data");
            if ("succ".equals(errorCode)) {
                logger.info("checkHeadImageByChat openId={}, mediaUrl={} traceId={} errmsg={} response={}", wxOpenId, mediaUrl, wxOpenId, errmsg, responseData);
            } else {
                logger.info("checkHeadImageByChat openId={}, mediaUrl={} errorCode={} errmsg={} traceId={}", wxOpenId, mediaUrl, errorCode, errmsg, wxOpenId);
            }
            return String.valueOf(roleId);
        } catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("校验出错,系统异常" ,e,"roleId" , roleId,"mediaUrl", mediaUrl);
        }
        return null;
    }
}
