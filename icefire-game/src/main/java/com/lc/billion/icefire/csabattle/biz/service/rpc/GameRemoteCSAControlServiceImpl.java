package com.lc.billion.icefire.csabattle.biz.service.rpc;

import com.alibaba.fastjson.JSON;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAActivityGroupContext;
import com.lc.billion.icefire.csacontrol.biz.model.activity.CSAScoreType;
import com.lc.billion.icefire.csacontrol.biz.service.common.CSAControlBroadcastToGameService;
import com.lc.billion.icefire.csacontrol.biz.service.rpc.CSAControlRPCToGameProxyService;
import com.lc.billion.icefire.csacontrol.biz.util.CSAUtils;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.csa.control.CSAControlBroadcastActivityInfoRpcThreadOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.CSAActivityHistoryDao;
import com.lc.billion.icefire.game.biz.manager.csa.CSAActivityHistoryManager;
import com.lc.billion.icefire.game.biz.manager.csa.CSAServerBattleInfoManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.csa.CSAServerBattleInfo;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.rpc.service.crossalliance.IGameRemoteCSAControlService;
import com.lc.billion.icefire.rpc.vo.csa.CSAActivityVo;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.TimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * game服到CSA中控
 * 
 * <AUTHOR>
 *
 */
@Service
public class GameRemoteCSAControlServiceImpl implements IGameRemoteCSAControlService {

	private static final Logger logger = LoggerFactory.getLogger(GameRemoteCSAControlServiceImpl.class);

	@Autowired
	ActivityDao activityDao;
	@Autowired
	private CSAControlRPCToGameProxyService csaControlRPCToGameProxyService;
	@Autowired
	protected AsyncOperationServiceImpl asyncOperationService;
	@Autowired
	private CSAControlBroadcastToGameService csaControlBroadcastToGameService;
	@Autowired
	private ConfigServiceImpl configService;
	@Autowired
	private CSAServerBattleInfoManager csaServerBattleInfoManager;
	@Autowired
	private CSAActivityHistoryManager csaActivityHistoryManager;
	@Autowired
	private CSAActivityHistoryDao csaActivityHistoryDao;

	/**
	 * 当前暂无判断，全部返回true
	 *
	 * @param serverId
	 * @return
	 */
	@Override
	public boolean findCSAServerQualified(int serverId) {
		return true;
	}

	@Override
	public CSAActivityVo findCSAActivityVo(int serverId, double serverCombat) {

		if (getCurrServerType() != ServerType.CSA_CONTROL) {
			throw new AlertException("当前代码只能运行在CSA控制服上", "StackTrace",Arrays.toString(Thread.currentThread().getStackTrace()));
		}

		Activity activity = CSAUtils.getCSAActivity(activityDao, serverId);
		if (activity == null) {
			return null;
		}

		CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
		if (csaActivityGroupContext == null) {
			return null;
		}

		if (csaActivityGroupContext.getServerCombat() != 0 || serverCombat == 0) {
			return new CSAActivityVo(activity);
		}

		csaActivityGroupContext.setServerCombat(serverCombat);
		activityDao.save(activity);

		CSAActivityVo csaActivityVo = new CSAActivityVo(activity);
		int againstServerId = csaActivityGroupContext.getAgainstServerId();

		if (CSAUtils.getCsaActivityGroupContext(activity, againstServerId) != null) {
			// 广播活动信息 To对手服
			CSAControlBroadcastActivityInfoRpcThreadOperation againstCsaControlBroadcastActivityInfoRpcThreadOperation = new CSAControlBroadcastActivityInfoRpcThreadOperation(
					againstServerId, false, activity, csaActivityVo, activityDao, csaControlRPCToGameProxyService, configService,
					csaServerBattleInfoManager, csaActivityHistoryManager, csaActivityHistoryDao, csaControlBroadcastToGameService);
			asyncOperationService.execute(againstCsaControlBroadcastActivityInfoRpcThreadOperation);
		}

		return csaActivityVo;
	}

	@Override
	public void uploadStageScoreToCSAControl(int serverId, int serverScore, CSAScoreType scoreType) {

		if (getCurrServerType() != ServerType.CSA_CONTROL) {
			throw new AlertException("当前代码只能运行在CSA控制服上","StackTrace",Arrays.toString(Thread.currentThread().getStackTrace()));
		}

		Activity activity = CSAUtils.getCSAActivity(activityDao, serverId);
		if (activity == null) {
			return;
		}

		CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
		if (csaActivityGroupContext == null) {
			return;
		}

		int againstServerId = csaActivityGroupContext.getAgainstServerId();
		CSAActivityGroupContext againstCsaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, againstServerId);
		if (againstCsaActivityGroupContext == null) {
			return;
		}

		// 设置对应分数
		scoreType.dealUploadScore(csaActivityGroupContext, serverScore);

		activityDao.save(activity);
		// 通知服务器活动信息变更：分数
		csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, false, true);

		logger.info("Upload StageScore. serverId={}, serverScore={}", serverId, serverScore);
	}

	@Override
	public void uploadEventComplete(int serverId) {
		if (getCurrServerType() != ServerType.CSA_CONTROL) {
			throw new AlertException("当前代码只能运行在CSA控制服上","StackTrace",Arrays.toString(Thread.currentThread().getStackTrace()));
		}

		Activity activity = CSAUtils.getCSAActivity(activityDao, serverId);
		if (activity == null) {
			return;
		}

		CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
		if (csaActivityGroupContext == null) {
			return;
		}


		csaActivityGroupContext.setCurrStageEventCompleteTime(csaActivityGroupContext.getCurrStageEventCompleteTime() + 1);
		activityDao.save(activity);

		// 通知服务器活动信息变更：事件完成
		csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, false, true);
	}

	@Override
	public void adjustCurrentStageEndTime(int serverId, int waitEndTime) {

		if (getCurrServerType() != ServerType.CSA_CONTROL) {
			throw new AlertException("当前代码只能运行在CSA控制服上","StackTrace",Arrays.toString(Thread.currentThread().getStackTrace()));
		}

		Activity activity = CSAUtils.getCSAActivity(activityDao, serverId);
		if (activity == null) {
			return;
		}

		CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
		if (csaActivityGroupContext == null) {
			return;
		}

		if (waitEndTime < 10) {
			waitEndTime = 10;
		}

		int againstServerId = csaActivityGroupContext.getAgainstServerId();
		CSAActivityGroupContext againstServerActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, againstServerId);
		if (againstServerActivityGroupContext == null) {
			return;
		}

		long now = TimeUtil.getNow();
		long currStageEndTime = now + waitEndTime * TimeUtils.SECONDS_MILLIS;
		csaActivityGroupContext.setNextCsaActivityStatusTime(currStageEndTime);
		againstServerActivityGroupContext.setNextCsaActivityStatusTime(currStageEndTime);

		// 保存数据
		activityDao.save(activity);

		// 通知服务器活动信息变更：时间
		csaControlBroadcastToGameService.broadcastCSAActivity(activity, serverId, false, true);

		logger.info("Adjust Current Stage EndTime. serverId={}, currStageEndTime={}", serverId, TimeUtil.parseTime2UtcStr(currStageEndTime));
	}

	/**
	 * 检测当前运行服务器类型
	 */
	private ServerType getCurrServerType() {
		return Application.getServerType();
	}


	/**
	 * 上传主事人选择时间到中控服
	 */
	@Override
	public void uploadSelectedTimeToCSAControl(int serverId, List<Integer> selectTimeList) {
		if (getCurrServerType() != ServerType.CSA_CONTROL) {
			throw new AlertException("当前代码只能运行在CSA控制服上","StackTrace",Arrays.toString(Thread.currentThread().getStackTrace()));
		}

		Activity activity = CSAUtils.getCSAActivity(activityDao, serverId);
		if (activity == null) {
			return;
		}

		CSAActivityGroupContext csaActivityGroupContext = CSAUtils.getCsaActivityGroupContext(activity, serverId);
		if (csaActivityGroupContext == null) {
			return;
		}

		// 设置选择时间
		csaActivityGroupContext.getSelectedBattleTimeList().clear();
		csaActivityGroupContext.getSelectedBattleTimeList().addAll(selectTimeList);
		activityDao.save(activity);

		logger.info("Upload selectTimeList. serverId={}, selectTimeList={}", serverId, JSON.toJSONString(selectTimeList));
	}

	@Override
	public CSAServerBattleInfo getCSAServerBattleInfoFromCSAControl(int serverId) {
		return csaServerBattleInfoManager.getCSAServerBattleInfoByServerId(serverId);
	}
}
