package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.exception.AlertException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Config(name = "SeasonSafeZone", metaClass = SeasonSafeZoneConfig.SeasonSafeZoneMeta.class)
public class SeasonSafeZoneConfig {
    /**
     * serverNum
     */
    private Map<Integer, SeasonSafeZoneMeta> serverNumMap = new HashMap<>();

    public void init(List<SeasonSafeZoneMeta> metaList) {
        StringBuffer sb = new StringBuffer();
        int index = 1;
        if (!Application.getConfigCenter().currentServerTypeIsGAME_or_KVKSEASON()) {
            return;
        }
        for (var meta : metaList) {
            var old = serverNumMap.get(meta.getServerNum());
            if (old != null) {
                sb.append(index + "重复配置 ID" + meta.getId() + "与ID" + old.getId() + "配置重复 serverNum=" + old.getServerNum());
                index++;
            }
            serverNumMap.put(meta.getServerNum(), meta);
        }
        if (sb.length() > 0) {
            throw new AlertException("赛季表Season有错误","season",sb);
        }
    }

    public SeasonSafeZoneMeta getSeasonMetaBySeason(int serverNum) {
        return serverNumMap.get(serverNum);
    }

    /**
     * 按照服务器和索引获取安全区
     *
     * @param serverNum
     * @param index
     * @return
     */
    public List<Integer> getSafeZoneList(int serverNum, int index) {
        return getSeasonMetaBySeason(serverNum).getRegionIdList(index);
    }

    public static class SeasonSafeZoneMeta extends AbstractMeta {

        @Getter
        private int serverNum;

        @JsonIgnore
        private List<List<Integer>> zoneList = new ArrayList<>();

        @Getter
        private List<Integer> allSafeZoneList = new ArrayList<>();

        /**
         * 按照索引获取安全区列表
         *
         * @param index
         * @return
         */
        public List<Integer> getRegionIdList(int index) {
            return zoneList.get(index);
        }

        public void init(JsonNode json) {
            String zoneListConfig = json.path("ZoneList").asText();
            for (var ids : MetaUtils.parseStringList(zoneListConfig, AbstractMeta.META_SEPARATOR_3)) {
                var zones = MetaUtils.parseIntegerList(ids, AbstractMeta.META_SEPARATOR_2);
                zoneList.add(zones);
                allSafeZoneList.addAll(zones);
            }
            if (zoneList.size() != serverNum) {
                throw new AlertException("解析SeasonSafeZone失败,服务器数量和安全区数量不一致",
                        "serverNum",serverNum,"安全区数量",zoneList.size());
            }
        }

    }
}
