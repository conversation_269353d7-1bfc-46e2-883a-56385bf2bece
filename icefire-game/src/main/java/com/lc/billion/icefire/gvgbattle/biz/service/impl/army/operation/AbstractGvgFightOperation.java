package com.lc.billion.icefire.gvgbattle.biz.service.impl.army.operation;

import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.service.impl.army.oper.AbstractArmyFightOperation;

/**
 * 
 * GVG战斗处理
 * 
 * <AUTHOR>
 * @date 2021/2/4
 */
public abstract class AbstractGvgFightOperation extends AbstractArmyFightOperation {
	@Override
	public void returnArrived(ArmyInfo army) {
		logger.info("[GVG]returnArrived, army: {}, role: {}", army.getPersistKey(), army.getRoleId());
		armyManager.takeBackArmy(army);
	}
}
