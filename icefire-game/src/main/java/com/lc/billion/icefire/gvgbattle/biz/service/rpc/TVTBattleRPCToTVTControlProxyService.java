package com.lc.billion.icefire.gvgbattle.biz.service.rpc;

import com.lc.billion.icefire.rpc.service.tvt.ITvtBattleRemoteTvtControlService;
import org.springframework.stereotype.Service;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;

/**
 * TVT战斗服-->TVT中控的rpc连接
 * */
@Service
public class TVTBattleRPCToTVTControlProxyService extends AbstractRPCProxyService {
	private RpcProxyBean<ITvtBattleRemoteTvtControlService> tvtBattleRemoteTVTControlService;

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.TVT_BATTLE };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.TVT_CONTROL };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		RpcProxyBuilder<ITvtBattleRemoteTvtControlService> rpcProxyBuilder = RpcProxyBuilder.create(ITvtBattleRemoteTvtControlService.class).connect(getSerializer(),
				gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
		RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
		ITvtBattleRemoteTvtControlService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());

		tvtBattleRemoteTVTControlService = new RpcProxyBean<ITvtBattleRemoteTvtControlService>(service, rpcClient);
		logger.info("rpc TVT_BATTLE to TVT_CONTROL {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
				gameServerConfig.getRpcPort());
		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		if (tvtBattleRemoteTVTControlService != null) {
			RpcClient rpcClient = tvtBattleRemoteTVTControlService.getRpcClient();
			rpcClient.setStop(true);
		}

		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		if (tvtBattleRemoteTVTControlService != null) {
			RpcClient rpcClient = tvtBattleRemoteTVTControlService.getRpcClient();
			rpcClient.setStop(true);
		}
		tvtBattleRemoteTVTControlService = null;
	}

	@Override
	protected boolean containsRPCClient(int serverId) {
		return tvtBattleRemoteTVTControlService != null;
	}

	@Override
	protected boolean checkInstance() {
		return false;
	}

	@Override
	protected boolean createWait() {
		return true;
	}

	public ITvtBattleRemoteTvtControlService getTVTBattleRemoteTVTControlService() {
		if (tvtBattleRemoteTVTControlService != null) {
			return tvtBattleRemoteTVTControlService.getProxy();
		}
		return null;
	}
}
