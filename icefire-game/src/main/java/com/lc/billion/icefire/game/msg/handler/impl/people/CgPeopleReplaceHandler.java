package com.lc.billion.icefire.game.msg.handler.impl.people;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.people.PeopleServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgPeopleReplace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @ClassName CgPeopleReplaceHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/8/12 20:31
 * @Version 1.0
 */
@Controller
public class CgPeopleReplaceHandler extends CgAbstractMessageHandler<CgPeopleReplace> {
    @Autowired
    private PeopleServiceImpl peopleService;

    @Override
    protected void handle(Role role, CgPeopleReplace message) {
        peopleService.peopleReplace(role, message.getId(), message.getTargetId());
    }
}
