package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.RolesEntityDao;
import com.lc.billion.icefire.game.biz.model.milestone.Milestone;
import com.lc.billion.icefire.game.biz.model.milestone.PersonalMilestone;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.longtech.ls.config.ServerType;
import com.simfun.sgf.utils.JavaUtils;

/**
 * <AUTHOR>
 * @date 2021/9/2
 */
@Repository
public class PersonalMilestoneDao extends RolesEntityDao<PersonalMilestone> {

	// roleId, <MilestoneEntityId, PersonalMilestone>
	private ConcurrentMap<Long, ConcurrentMap<Long, PersonalMilestone>> rolePersonalMilestones = new ConcurrentHashMap<>();
	// MilestoneEntityId, <roleId, PersonalMilestone>
	private ConcurrentMap<Long, ConcurrentMap<Long, PersonalMilestone>> milestone2personalInfoMap = new ConcurrentHashMap();

	//
	//

	protected PersonalMilestoneDao() {
		super(PersonalMilestone.class, true);
	}

	@Override
	protected void putMemoryIndexes(PersonalMilestone entity) {
		rolePersonalMilestones.compute(entity.getRoleId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).put(entity.getMilestoneEntityId(), entity);
		milestone2personalInfoMap.compute(entity.getMilestoneEntityId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).put(entity.getRoleId(), entity);
	}

	@Override
	protected void removeMemoryIndexes(PersonalMilestone entity) {
		rolePersonalMilestones.compute(entity.getRoleId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).remove(entity.getMilestoneEntityId());
		milestone2personalInfoMap.compute(entity.getMilestoneEntityId(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).remove(entity.getRoleId());
	}

	@Override
	protected MongoCursor<PersonalMilestone> doFindAll(int db, List<Long> roleIds) {
		return dbFindByRoleIds(db, roleIds);
	}

	@Override
	protected MongoCursor<PersonalMilestone> doFindByPlayerId(int db, Long roleId) {
		return dbFindByRoleId(db, roleId);
	}

	@Override
	protected Collection<Long> clearMemoryIndexes(Long playerId) {
		List<Long> ret = new ArrayList<>();
		ConcurrentMap<Long, PersonalMilestone> map = rolePersonalMilestones.remove(playerId);
		if (map != null) {
			map.values().forEach(o -> {
				ret.add(o.getPersistKey());
				ConcurrentMap<Long, PersonalMilestone> map2 = milestone2personalInfoMap.get(o.getMilestoneEntityId());
				if (map2 != null)
					map2.remove(playerId);
			});
		}
		return ret;
	}

	@Override
	public Collection<PersonalMilestone> findByRoleId(Long roleId) {
		ConcurrentMap<Long, PersonalMilestone> concurrentMap = rolePersonalMilestones.get(roleId);
		if (JavaUtils.bool(concurrentMap)) {
			return concurrentMap.values();
		}
		return Collections.emptyList();
	}

	public PersonalMilestone create(Role role, Milestone milestone) {
		PersonalMilestone personalMilestone = new PersonalMilestone();
		personalMilestone.setMilestoneEntityId(milestone.getPersistKey());
		personalMilestone.setRoleId(role.getPersistKey());
		personalMilestone.setKVKSeasonData(ServerConfigManager.getInstance().getServerTypeConfig().getServerType(milestone.getDB()) == ServerType.KVK_SEASON);
		return createEntity(role, personalMilestone);
	}

	public PersonalMilestone getByMileStone(Role role, Milestone milestone) {
		return milestone2personalInfoMap.compute(milestone.getPersistKey(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v).get(role.getRoleId());
	}

	public boolean isComplete(Role role, Milestone milestone) {
		PersonalMilestone personalMilestone = milestone2personalInfoMap.compute(milestone.getPersistKey(), (k, v) -> v == null ? new ConcurrentHashMap<>() : v)
				.get(role.getRoleId());
		if (personalMilestone == null)
			return false;
		return personalMilestone.isCompleted();
	}

	public Map<Long, PersonalMilestone> getPersonaMilestoneRecord(Milestone milestone) {
		return milestone2personalInfoMap.get(milestone.getPersistKey()) == null ? new HashMap<>() : milestone2personalInfoMap.get(milestone.getPersistKey());
	}

}
