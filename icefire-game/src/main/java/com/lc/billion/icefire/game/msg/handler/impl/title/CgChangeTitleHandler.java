package com.lc.billion.icefire.game.msg.handler.impl.title;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.title.RoleTitleConstant;
import com.lc.billion.icefire.game.biz.service.impl.title.TitleService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgChangeTitle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * 请求称号列表
 *
 * <AUTHOR>
 * @since 2021-2-24
 */
@Controller
public class CgChangeTitleHandler extends CgAbstractMessageHandler<CgChangeTitle> {
	private static final Logger logger = LoggerFactory.getLogger(CgChangeTitleHandler.class);

	@Autowired
	private TitleService titleService;

	@Override
	public void handle(Role role, CgChangeTitle message) {
		switch (message.getType()){
			case RoleTitleConstant.CHANGE_TYPE_ACTIVE:
				titleService.activate(role, message.getTitleId());
				break;
			case RoleTitleConstant.CHANGE_TYPE_REMOVE:
				titleService.removeActivatedTitle(role);
				break;
			default:
				ErrorLogUtil.errorLog("CgChangeTitleHandler receive msg type error","msgType", message.getType());
				break;
		}
	}
}
