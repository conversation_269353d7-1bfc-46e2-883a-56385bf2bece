package com.lc.billion.icefire.gvgbattle.biz.dao.mongo.root;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.RootDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.gvgbattle.biz.model.gvg.RoleGVGBattle;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 *
 */
@Repository
public class RoleGVGBattleDao extends RootDao<RoleGVGBattle> {

	public RoleGVGBattleDao() {
		super(RoleGVGBattle.class, false);
	}

	@Override
	protected MongoCursor<RoleGVGBattle> doFindAll(int db ) {
		return dbFindAllForWorldEntity(db);
	}

	@Override
	protected void putMemoryIndexes(RoleGVGBattle entity) {

	}

	@Override
	protected void removeMemoryIndexes(RoleGVGBattle entity) {

	}

	public RoleGVGBattle create(Role role, Alliance alliance) {
		RoleGVGBattle entity = newEntityInstance();
		entity.setPersistKey(role.getId());
		entity.setBattlePoint(0);
		entity.setMoveCount(0);
		entity.setBuyMoveCount(0);
		entity.setBuyStaminaCount(0);
		entity.setAllianceId(role.getAllianceId());
		entity.setAllianceAliasName(alliance == null ? "" : alliance.getAliasName());
		entity.setAllianceName(alliance == null ? "" : alliance.getName());
		entity.setHead(role.getHead());
		entity.setSex(role.getSex());
		entity.setHeadFrame(role.getHeadFrame());
		entity.setName(role.getName());
		entity.setServerId(role.getoServerId());
		return createEntity(Application.getServerId(), entity);
	}

	public void deleteAll() {
		delete(findAll());
	}
}
