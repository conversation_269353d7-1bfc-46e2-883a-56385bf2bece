require.config({
	shim : {
        'bootstrap' : ['jquery'],
        'bootbox'   : ['jquery'],
        'json'		: ['jquery'],
        'validator' : ['jquery'],
        'common'    : ['jquery']
    },
    
    
	paths: {
		'jquery'	: '../lib/jquery.min',
		'json'		: '../lib/jquery.json.min',
		'bootstrap'	: '../lib/bootstrap.min',
  		'validator' : '../lib/validator.min',
  		'bootbox'   : '../lib/bootbox.min',
  		'select'	: '../lib/bootstrap-select.min',
  		
  		'common'    : 'common'
    }
});

define(['jquery', 'bootbox', 'common', 'bootstrap', 'validator', 'select', 'json'], function($, bootbox, common) {
	var gm = {};
	
	$(function() {
	
		$('body').on('keyup', '#systemCommand', function() {
			if(event.keyCode == 13){  
				gm.executeCmd();
			}
		});
		
	});
	
	gm = {
		
		executeCmd: function() {
			var serverId = $('#serverSelect').val();
			var command = $('#systemCommand').val();
			
			if( command == '' ) {
				common.alert("GM 命令错误！ 不会？尝试输入：help");
				return;
			}
			
			$.ajax({
				url:  '/console/system/command/execute',
				type: 'POST',
				data: {
					serverId : serverId,
					command  : command
				}
			}).done(function(data) {
            	if (!common.isPermitted(data)) {
            		return;
            	}
				var json = jQuery.parseJSON( data );
				
				var result = $('#systemCommandResult');
				
				/**display cmd help*/
				if( json.cmd != null ) {
					result.append('---------------------------------------------------------------------------\n');
					result.append('Help\n');
					result.append('---------------------------------------------------------------------------\n');
					$.each(json.cmd, function(key, index) {
						result.append(key).append('    ').append(json.cmd[key]).append('\n');		
					}); 
				}
				
				result.append('---------------------------------------------------------------------------\n');
				result.append('Command: ' + command +'\n');
				result.append('RetCode	: ' + json.ret +'\n');
				if( json.msg != null ) {
					result.append('Message	: ' + json.msg).append('\n');
				}
				result.append(new Date()).append('\n');
				
				var scrollTop = result[0].scrollHeight;  
				result.scrollTop(scrollTop);  
			});
		},
		
		bindServerID: function() {
			var selectServer = $('#serverSelect-role').val();
			$('#serverid-role').val(selectServer);
		},
		
		searchRoleInfoById: function() {
			var me = this;
			var userId = $('#userInfoId').val();
			me.cleanTablesData();
			
			if (userId == '') {
				common.alert("请输入有效的  玩家ID 进行查询！");
				return;
			}
			
			$.ajax({
				url:  '/console/role/searchinfo/uid',
				type: 'POST',
				data: {
					userId : userId
				},
				beforeSend: function(xhr) {
					common.mask();
				},
				complete: function(result) {
					common.unmask();
				}
			}).done(function(data) {
            	if (!common.isPermitted(data)) {
            		return;
            	}
				var dataJson = jQuery.parseJSON (data);
				if (dataJson.ret != -1) {
					var result = jQuery.parseJSON (dataJson.result);
					var res = jQuery.parseJSON(result.res);
					if(res.ret == 0) {
						var resdata = jQuery.parseJSON(res.data);
						me.bindAccInfoData(resdata, result.serverId, result.serverName );
					} else {
						if( res.msg ) {
							common.alert(res.msg);
						} else {
							common.alert("服务器内部错误！");
						}
					}
				} else {
					common.alert(dataJson.msg);
				}
				
			});

		},
		
		searchRoleInfoByName: function() {
			var me = this;
			var name = $('#name-role').val();
			var serverId = $('#serverSelect-role').val();
			var serverName = $('#serverSelect-role').find('option:selected').text();
			me.cleanTablesData();
			
			if (name == '') {
				common.alert("请输入有效的  玩家名称 进行查询！");
				return;
			}
			
			$.ajax({
				url  :	'/console/role/searchinfo/name',
				type :	'POST',
				data :	{
					serverId : serverId,
					name :	name
				},
				beforeSend: function(xhr) {
					common.mask();
				},
				complete: function(result) {
					common.unmask();
				}
			}).done(function(data) {
            	if (!common.isPermitted(data)) {
            		return;
            	}
				var json = jQuery.parseJSON (data);
				if (json.ret != -1) {
					var result = jQuery.parseJSON (json.result);
					if(result.ret == 0) {
						var resdata = jQuery.parseJSON(result.data);
						me.bindAccInfoData(resdata, serverId, serverName);
					} else {
						if( result.msg ) {
							common.alert(result.msg);
						} else {
							common.alert("服务器内部错误！");
						}
					}
					
				} else {
					common.alert(json.msg);
				}
				
			});
			
		},
		
		bindAccInfoData: function(data, serverId, name) {
			var me = this;
			var allianceData = jQuery.parseJSON(data.alliance);
			var techData = jQuery.parseJSON(data.tech);
			
			$('#role-level').text(data.level);
			$('#role-id').text(data.roleId);
			$('#role-name').text(data.name);
			$('#role-diamond').text(data.currencyMap.DIAMOND);
			$('#role-ne-time').text(data.lastLoginTime);
			$('#role-ne-ip').text(data.lastLogoutTime);
			$('#role-reg-time').text(data.createTime);
			$('#role-server-id').text(serverId);
			$('#role-server-name').text(name);
			$('#role-alliance').text(allianceData.name);
			$('#role-alliance-id').text(allianceData.id);
			if (data.chatBan == true) {
				if (data.chatExpireTime == 0) {
					$('#role-chat-ban').text("永久禁言");
				} else {
					$('#role-chat-ban').text(data.chatExpireTime + "解禁");
				}
			} else {
				$('#role-chat-ban').text("否");
			}
			if (data.accountBan == true) {
				$('#role-ban').text("是");
			} else {
				$('#role-ban').text("否");
			}
			if(data.isGM == true ) {
				$('#role-apply-gm').text("是");
			}else{
				$('#role-apply-gm').text("否");
			}
			$('#tabHeroExp').text("英雄全局经验:" + data.heroGlobalExp);
			
			//资源
			$('#food').text(data.currencyMap.FOOD);
			$('#wood').text(data.currencyMap.WOOD);
			$('#iron').text(data.currencyMap.IRON);
			$('#silver').text(data.currencyMap.SILVER);
			$('#gold').text(data.currencyMap.GOLD);
			$('#diamond').text(data.currencyMap.DIAMOND);
			$('#honour').text(data.currencyMap.HONOUR);
			
			me.bindHeroTableData(data.heroList);
			me.bindSoldierTableData(data.soldier);
			me.bingItemTableData(data.itemList);
			
			me.bindAllianceData(allianceData);
			me.bindMissionTableData(data.mission);
			me.bindTechTableData(techData);
		},
		
		bindTechTableData: function(data) {
			
			if (data != null) {
				for (var tech in data) {
					var row = $('<tr></tr>');
					row.append('<td>' + tech + '</td>');
					row.append('<td>' + data[tech] + '</td>');
					$('#tabTech').append(row);
				}
			}
			
		},
		
		bindMissionTableData: function(data) {
			var me = this;
			$.each(data, function(index, missionData) {
				var row = $('<tr></tr>');
				row.append('<td>' + missionData.missionId + '</td>');
				row.append('<td>' + missionData.progress + '</td>');
				row.append(me.renderMissionStatus(missionData.status));
				$('#tabMission').append(row);
			});
		},
		
		renderMissionStatus: function(status) {
			switch(status) {
			case 0:
				return '<td>未完成</td>';
			case 1:
				return '<td>完成</td>';
			default:
				return '<td>' + status + '</td>';
			}
			
		},
		
		bindAllianceData: function(data) {
			$('#alliance-id').text(data.id);
			$('#alliance-name').text(data.name);
			$('#alliance-alias').text(data.aliasName);
			$('#alliance-banner').text(data.banner);
			$('#alliance-maxMem').text(data.maxMember);
			$('#alliance-leaderid').text(data.leaderId);
			$('#alliance-capacity').text(data.fightingCapacity);
			$('#alliance-notice').text(data.notice);
			$('#alliance-declaration').text(data.declaration);
			$('#alliance-honour').text(data.honour);
			$('#alliance-mem').text(data.members);
			$('#alliance-createTime').text(data.createAllTime);
		},
		
		bingItemTableData: function(data) {
			for (var flag in data) {
				$.each(data[flag], function(index, itemData) {
					var row = $('<tr></tr>');
					row.append('<td>' + itemData.id + '</td>');
					row.append('<td>' + itemData.metaId + '</td>');
					row.append('<td>' + itemData.bagType + '</td>');
					row.append('<td>' + itemData.count + '</td>');
					row.append('<td>' + itemData.bagIndex  + '</td>');
					$('#tabItems').append(row);
				});
			}
			
		},
		
		bindSoldierTableData: function(data) {
						
			if (data != null) {
				for (var soldier in data) {
					if (data[soldier] != 0) {
						var row = $('<tr></tr>');
						row.append('<td>' + soldier + '</td>');
						row.append('<td>' + data[soldier] + '</td>');
						$('#tabSoldier').append(row);
					}
				}
			}
			
		},
		
		bindHeroTableData: function(data) {
			var me = this;
			for (var hero in data) {
				var row = $('<tr></tr>');
				row.append('<td>' + data[hero].id    + '</td>');
				row.append('<td>' + data[hero].name  + '</td>');
				row.append('<td>' + data[hero].level + '</td>');
				row.append('<td>' + data[hero].stone  + '</td>');
				row.append('<td>' + data[hero].exp   + '</td>');
				row.append(me.renderHeroStatus(data[hero].status));
				var property = $('<td></td>');
				property.append('<input type="hidden" id="' + data[hero].id + '" value=' + $.toJSON(data[hero].attrs) + '>');
				property.append('<a class="hero-prop" href="javascript:void(0)" data-id="'+data[hero].id+'" title="属性"><span class="glyphicon glyphicon-list-alt"></span></a>');
				row.append(property);
				row.append('<td>' + data[hero].additionPoint + '</td>');
				$('#tabHero').append(row);
			}
			
		},
		
		renderHeroStatus: function(status) {
			switch(status) {
			case 1:
				return '<td>空闲</td>';
			case 2:
				return '<td>出征</td>';
			case 3:
				return '<td>守家</td>';
			default:
				return '<td>' + status + '</td>';
			}
		},
		
		showHeroProp: function(btn) {
			this.cleanHeroAttrsData();
			var heroId = $(btn).attr('data-id');
			var json = $('#'+heroId).val();
			
			var data = jQuery.parseJSON(json);
			this.bindHeroAttrsData(data);
			
			$('#showHeroModal').modal('show');
			
		},
		
		bindHeroAttrsData: function(data) {
			
			$.each(data, function(index, attr) {
				var row = $('<tr></tr>');
				row.append('<td>' + attr.attributeType + '</td>');
				row.append('<td>' + attr.metaId + '</td>');
				row.append('<td>' + attr.baseValue + '</td>');
				row.append('<td>' + attr.additionPoint + '</td>');
				if (attr.isNew == true) {
					row.append('<td>是</td>');
				} else {
					row.append('<td>否</td>')
				}
				$('#tabHeroAttrs').append(row);
			});
		},
		
		cleanHeroAttrsData: function() {
			$('#tabHeroAttrs tr:gt(0)').remove();
		},
		
		cleanTablesData: function() {
			$('#tabSoldier tr:gt(0)').remove();
			$('#tabHero tr:gt(0)').remove();
			$('#tabTech tr:gt(0)').remove();
			$('#tabMission tr:gt(0)').remove();
			$('#tabItems tr:gt(0)').remove();
			$('td[id^="alliance-"]').text(0);
			$('td[id^="role-"]').text(0);
			$('#food').text(0);
			$('#wood').text(0);
			$('#iron').text(0);
			$('#silver').text(0);
			$('#gold').text(0);
			$('#diamond').text(0);
			$('#honour').text(0);
		},
		
		sendDynacCode: function() {
			var code = $('#dynaCode').val();
			$('#codeRunResult').text("");
			
			if( code == '') {
				common.alert("请先输入有效代码");
				return;
			}
			bootbox.confirm('您确定要发送此段代码吗？', function(btn) {
				if (btn) {
					$.ajax({
						url : '/console/dynamicode/send',
						type: 'POST',
						data: {
							serverId:$('#serverSelect-code').val(),
							code:code
						},
						beforeSend: function(xhr) {
							common.mask();
						},
						complete: function(result) {
							common.unmask();
						}
					}).done(function(result) {
		            	if (!common.isPermitted(result)) {
		            		return;
		            	}
						var json = jQuery.parseJSON(result);
						var runResult = $('#codeRunResult');
						
						if (json.value != null) {
							runResult.append('-------------------------------------------------------------------------------------------------------------------------------\n');
							runResult.append("value   :  \n" + json.value).append("\n");
						}
						
						runResult.append('-------------------------------------------------------------------------------------------------------------------------------\n');
						if (json.msg != null) {
							runResult.append("Message  :  \n" + json.msg).append("\n");
						}
						runResult.append(new Date()).append('\n');
						runResult.append("\n");
					});
					
				}
				
			})
		},
		
	};
	
	return gm;
});