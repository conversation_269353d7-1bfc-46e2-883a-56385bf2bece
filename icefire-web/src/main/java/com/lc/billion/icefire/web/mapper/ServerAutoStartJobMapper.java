package com.lc.billion.icefire.web.mapper;

import com.lc.billion.icefire.web.bus.gm.model.ServerAutoStartJob;

import java.util.List;
import java.util.Map;

public interface ServerAutoStartJobMapper {
    boolean insert(ServerAutoStartJob record);

    boolean delete(Long id);

    boolean updateStartTime(ServerAutoStartJob record);

    boolean updateStatus(ServerAutoStartJob record);

    ServerAutoStartJob selectById(Long id);

    List<ServerAutoStartJob> selectPageByParam(Map<String, Object> param );

    int selectCountByParam(Map<String, Object> param );

    List<ServerAutoStartJob> select24HourList(Map<String, Object> param);

    List<ServerAutoStartJob> selectWaitJobByServerId(Map<String, Object> param);

    ServerAutoStartJob selectWaitJobByTimeRange(Map<String, Object> param);
}
