package com.lc.billion.icefire.web.bus.flyway.migrations.web;

import com.lc.billion.icefire.web.bus.flyway.AbstractBaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class V202203180908__<PERSON><PERSON> extends AbstractBaseJavaMigration {
    private static final String INSERT_ACL_SQL = "INSERT INTO `sys_acl` VALUES (?, ?, ?, ?, ?);";
    private static final String[][] INSERT_ACL_SQL_DATA = {
            {"253","console_server_updateUuidWhite", "/console/server/getUuidWhiteList", "perms[console_server_updateUuidWhite]", "获取uuid白名单"},
    };

    private static final String INSERT_ACL_ROLE_SQL = "INSERT INTO `sys_acl_role` VALUES (?, ?);";
    private static final String[][] INSERT_ACL_ROLE_SQL_DATA = {
            { "253", "1" },
    };

    @Override
    protected void update(Context context) throws Exception {
        Connection connection = context.getConnection();
        PreparedStatement insert_acl = connection.prepareStatement(INSERT_ACL_SQL);
        for (String[] acl_data : INSERT_ACL_SQL_DATA) {
            for (int i = 1; i <= acl_data.length; i++) {
                insert_acl.setObject(i, acl_data[i - 1]);
            }
            insert_acl.addBatch();
        }
        insert_acl.executeBatch();
        close(insert_acl);

        PreparedStatement insert_acl_role = connection.prepareStatement(INSERT_ACL_ROLE_SQL);
        for (String[] acl_role_data : INSERT_ACL_ROLE_SQL_DATA) {
            for (int i = 1; i <= acl_role_data.length; i++) {
                insert_acl_role.setObject(i, acl_role_data[i - 1]);
            }
            insert_acl_role.addBatch();
        }
        insert_acl_role.executeBatch();
        close(insert_acl_role);
    }
}
