//package com.lc.billion.icefire.web.bus.server.entity.policy;
//
//import java.util.function.Supplier;
//
//public enum RecommendPolicyType {
//	/**
//	 * 国家策略
//	 */
//	COUNTRY(1, CountryRecommendPolicy::new),
//	/**
//	 * 平台策略
//	 */
//	PLATFORM(2, PlatformRecommendPolicy::new),;
//
//	private int id;
//	private Supplier<RecommendPolicy> supplier;
//
//	private RecommendPolicyType(int id, Supplier<RecommendPolicy> supplier) {
//		this.id = id;
//		this.supplier = supplier;
//	}
//
//	public int getId() {
//		return id;
//	}
//
//	public RecommendPolicy createInstance() {
//		return supplier.get();
//	}
//
//	public static RecommendPolicyType getType(int id) {
//		for (RecommendPolicyType type : values()) {
//			if (type.getId() == id) {
//				return type;
//			}
//		}
//		return null;
//	}
//}
