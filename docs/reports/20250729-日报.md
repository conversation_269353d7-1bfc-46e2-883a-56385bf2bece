1. Apache HttpClient监控系统集成
- 创建ApacheHttpClientInstrumentation类，实现对Apache HttpComponents 4.x的监控装饰
- 实现HttpRequestContext数据结构，封装HTTP请求的监控上下文信息
- 添加请求/响应拦截器，自动记录请求耗时、状态码、方法和URI等指标
- 实现URI模式化处理，将动态路径参数替换为占位符以减少监控标签基数
- 支持可选的监控回调机制，通过外部注入监控处理逻辑

2. HttpClient监控工具类封装
- 创建HttpClientMonitoring工具类，提供简洁的监控集成API
- 封装ApacheHttpClientInstrumentation的复杂性，对外提供统一接口
- 实现监控可用性检查机制，确保监控功能的健壮性
- 支持为不同HttpClient实例指定不同的客户端名称用于监控分类

3. 现有HttpUtil类监控升级
- 在HttpUtil.init()方法中集成HttpClientMonitoring
- 为共享的HttpClient实例添加"HttpUtil"标识的监控支持
- 保持原有API兼容性，监控功能作为可选增强特性
- 确保监控集成不影响现有HTTP请求的功能和性能

4. 监控初始化器组件开发
- 创建HttpClientMonitoringInitializer Spring组件
- 实现ContextRefreshedEvent监听器，在应用启动完成后自动初始化监控
- 建立sgf-monitoring模块与icefire-core模块的监控桥梁
- 通过反射机制安全地注入监控回调，避免模块间的强依赖

5. 监控配置完善
- 在MonitoringConfiguration中添加HttpClientMonitoringInitializer Bean配置
- 确保监控初始化器在Spring容器中正确注册和管理
- 完善监控组件的依赖关系，保证初始化顺序的正确性 