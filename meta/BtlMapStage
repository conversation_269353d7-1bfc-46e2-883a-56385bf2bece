[{"id": "1", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1001101,1,1|-2~2,22~25,1001102,2,1|-2~2,22~25,1001103,1,1|-2.5~1.5,36~42,1001101,8,1|0~0,40~40,1001107,1,1|-2.5~1.5,43~48,1001102,8,1|-2.5~1.5,50~60,1001103,8,1|0~0,58~58,1001109,1,1|1.5~4,70~75,1001101,11,1|-4~-1.5,70~75,1001102,12,1|-1.5~1.5,70~75,1001103,10,1|1~3,75~75,1001106,1,1", "Building": "0,15,1001201|2.3,15,1001202|0,32,1001203|-2.3,40,1001204|2.3,50,1001205|-2.3,63,1001206|0,63,1001207|2.3,63,1001208"}, {"id": "2", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "10,200", "MapOffSetX": "0", "Monster": "-4~-2.5,25~31,1002104,5,1|-4~-2.5,32~38,1002102,6,1|-4~-2.5,39~45,1002101,5,1|-4~-2.5,45~50,1002107,2,1|-4~-1,62~66,1002103,8,1|-2.5~0,62~66,1002102,8,1|-4~-2.5,66~68,1002109,1,1|-2.5~0,66~68,1002108,1,1|-2.5~0,69~72,1002103,12,1|-3.5~-1,75~80,1002101,10,1|-2.5~0,75~80,1002102,10,1|-4~-2,75~80,1002103,10,1|-2~-1.5,81~83,1002105,1,1", "Building": "2.7,15,1002201|2.7,25,1002202|2.7,30,1002203|2.7,35,1002204|2.7,40,1002205|2.7,45,1002206|2.7,55,1002207|2.7,60,1002208|2.7,65,1002209|2.7,70,1002210|-2.7,30,1002211|-4,55,1002212|-0.5,70,1002213|-4,80,1002214|-0.5,80,1002215"}, {"id": "3", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1003101,1,1|-1.5~1.5,29~40,1003102,12,1|0~0,42~42,1003108,1,1|-1.5~1.5,48~55,1003103,13,1|0~0,51~51,1003109,1,1|-1.5~1.5,58~61,1003103,9,1|0~3,63~66,1003104,10,1|-1.5~1.5,72~75,1003101,10,1|-2.5~-2.5,73~73,1003107,1,1|1.5~3.5,75~78,1003102,12,1|2~2,78~78,1003108,1,1|-4~-1.5,80~84,1003103,10,1|0~0,85~85,1003106,1,1", "Building": "-2.3,15,1003201|0,15,1003202|2.3,15,1003203|0,28,1003204|-2.3,42,1003205|2.3,42,1003206|-2.3,56,1003207|0,56,1003208|2.3,56,1003209|-2.3,70,1003210|0,70,1003211"}, {"id": "4", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "0~0,20~20,1004101,1,1|-1.5~1.5,29~40,1004102,12,1|0~0,42~42,1004108,1,1|-1.5~1.5,48~55,1004103,13,1|0~0,51~51,1004109,1,1|-1.5~1.5,58~61,1004103,9,1|0~3,63~66,1004104,10,1|-1.5~1.5,72~75,1004101,10,1|-2.5~-2.5,73~73,1004107,1,1|1.5~3.5,75~78,1004102,12,1|2~2,78~78,1004108,1,1|-4~-1.5,80~84,1004103,10,1|0~0,85~85,1004106,1,1", "Building": "-2.3,15,1004201|0,15,1004202|2.3,15,1004203|0,28,1004204|-2.3,42,1004205|2.3,42,1004206|-2.3,56,1004207|0,56,1004208|2.3,56,1004209|-2.3,70,1004210|0,70,1004211"}, {"id": "5", "FieldType": "2", "PassCondition": "2", "Speed": "3", "MapSize": "8,120", "MapOffSetX": "0", "Monster": "0~0,12~12,1005101,1,0|0~0,15~15,1005101,1,0|-3~3,19~19,1005101,3,0|-1~1,26~30,1005102,12,0|-3~-1.5,33~36,1005103,12,0|-1~1,40~43,1005104,12,0|1.5~3,46~50,1005105,8,0|-1~1,56~59,1005102,12,0|-1~1,63~65,1005103,12,0", "Building": "-2.3,10,1005201|2.3,10,1005202|-2.3,25,1005203|2.3,25,1005204|-2.3,48,1005205|2.3,48,1005206|-2.3,60,1005207"}, {"id": "6", "FieldType": "3", "PassCondition": "1", "Speed": "1", "MapSize": "10,12", "MapOffSetX": "62", "Monster": "-8~8,-2~10,1001101,100,0|-8~8,-2~10,1001101,1,0", "Building": ""}, {"id": "7", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": ""}, {"id": "8", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": ""}, {"id": "9", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": ""}, {"id": "10", "FieldType": "1", "PassCondition": "1", "Speed": "0", "MapSize": "8,200", "MapOffSetX": "0", "Monster": "-2~2,22~25,1007101,1,1|-2~2,22~25,1007102,2,1|-2~2,22~25,1007103,1,1|-2.5~1.5,36~42,1007101,8,1|0~0,40~40,1007107,1,1|-2.5~1.5,43~48,1007102,8,1|-2.5~1.5,50~60,1007103,8,1|0~0,58~58,1007109,1,1|1.5~4,70~75,1007101,11,1|-4~-1.5,70~75,1007102,12,1|-1.5~1.5,70~75,1007103,10,1|1~3,75~75,1007106,1,1", "Building": "0,15,1001203"}]