[{"id": "2101001", "skill_id": "21010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2101001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2101001", "LCompareInfor": "BtlSkill_CompareInfor_2101001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101001", "skillEffectIdStr": "2101001", "skillEffectIdStrNew": "2101001"}, {"id": "2101101", "skill_id": "21011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2101101", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101101", "LCompareInfor": "BtlSkill_CompareInfor_2101101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011012;21011013;21011011", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011012;21011013;21011011"}, {"id": "2101102", "skill_id": "21011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2101102", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101102", "LCompareInfor": "BtlSkill_CompareInfor_2101102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011012;21011013;21011021", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011012;21011013;21011021"}, {"id": "2101103", "skill_id": "21011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2101103", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101103", "LCompareInfor": "BtlSkill_CompareInfor_2101103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011032;21011033;21011031", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011032;21011033;21011031"}, {"id": "2101104", "skill_id": "21011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2101104", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101104", "LCompareInfor": "BtlSkill_CompareInfor_2101104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011032;21011033;21011041", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011032;21011033;21011041"}, {"id": "2101105", "skill_id": "21011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2101105", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101105", "LCompareInfor": "BtlSkill_CompareInfor_2101105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011052;21011053;21011051", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011052;21011053;21011051"}, {"id": "2101106", "skill_id": "21011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2101106", "skillIconId": "skill_battle_96", "LSkillDes": "BtlSkill_SkillDes_2101106", "LCompareInfor": "BtlSkill_CompareInfor_2101106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2101101", "skillEffectIdStr": "21011014;21011024;21011034;21011044;21011054;21011052;21011053;21011061", "skillEffectIdStrNew": "21011014;21011024;21011034;21011044;21011054;21011052;21011053;21011061"}, {"id": "2101201", "skill_id": "21012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2101201", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101201", "LCompareInfor": "BtlSkill_CompareInfor_2101201", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101201", "skillEffectIdStrNew": "2101201"}, {"id": "2101202", "skill_id": "21012", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2101202", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101202", "LCompareInfor": "BtlSkill_CompareInfor_2101202", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101202", "skillEffectIdStrNew": "2101202"}, {"id": "2101203", "skill_id": "21012", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2101203", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101203", "LCompareInfor": "BtlSkill_CompareInfor_2101203", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101203", "skillEffectIdStrNew": "2101203"}, {"id": "2101204", "skill_id": "21012", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2101204", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101204", "LCompareInfor": "BtlSkill_CompareInfor_2101204", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101204", "skillEffectIdStrNew": "2101204"}, {"id": "2101205", "skill_id": "21012", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2101205", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101205", "LCompareInfor": "BtlSkill_CompareInfor_2101205", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101205", "skillEffectIdStrNew": "2101205"}, {"id": "2101206", "skill_id": "21012", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2101206", "skillIconId": "skill_battle_82", "LSkillDes": "BtlSkill_SkillDes_2101206", "LCompareInfor": "BtlSkill_CompareInfor_2101206", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101206", "skillEffectIdStrNew": "2101206"}, {"id": "2101901", "skill_id": "21019", "skill_level": "1", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "800", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019011", "skillEffectIdStrNew": "21019011"}, {"id": "2101902", "skill_id": "21019", "skill_level": "2", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019021", "skillEffectIdStrNew": "21019021"}, {"id": "2101903", "skill_id": "21019", "skill_level": "3", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019031", "skillEffectIdStrNew": "21019031"}, {"id": "2101904", "skill_id": "21019", "skill_level": "4", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019041", "skillEffectIdStrNew": "21019041"}, {"id": "2101905", "skill_id": "21019", "skill_level": "5", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019051", "skillEffectIdStrNew": "21019051"}, {"id": "2101906", "skill_id": "21019", "skill_level": "6", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "2101201", "skillEffectIdStr": "21019061", "skillEffectIdStrNew": "21019061"}, {"id": "2101301", "skill_id": "21013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2101301", "skillIconId": "skill_battle_32", "LSkillDes": "BtlSkill_SkillDes_2101301", "LCompareInfor": "BtlSkill_CompareInfor_2101301", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101301", "skillEffectIdStrNew": "2101301"}, {"id": "2101302", "skill_id": "21013", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2101302", "skillIconId": "skill_battle_32", "LSkillDes": "BtlSkill_SkillDes_2101302", "LCompareInfor": "BtlSkill_CompareInfor_2101302", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101302", "skillEffectIdStrNew": "2101302"}, {"id": "2101303", "skill_id": "21013", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2101303", "skillIconId": "skill_battle_32", "LSkillDes": "BtlSkill_SkillDes_2101303", "LCompareInfor": "BtlSkill_CompareInfor_2101303", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101303", "skillEffectIdStrNew": "2101303"}, {"id": "2101304", "skill_id": "21013", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2101304", "skillIconId": "skill_battle_32", "LSkillDes": "BtlSkill_SkillDes_2101304", "LCompareInfor": "BtlSkill_CompareInfor_2101304", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101304", "skillEffectIdStrNew": "2101304"}, {"id": "2101305", "skill_id": "21013", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2101305", "skillIconId": "skill_battle_32", "LSkillDes": "BtlSkill_SkillDes_2101305", "LCompareInfor": "BtlSkill_CompareInfor_2101305", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2101305", "skillEffectIdStrNew": "2101305"}, {"id": "2102001", "skill_id": "21020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2102001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2102001", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2102001", "skillEffectIdStr": "2102001", "skillEffectIdStrNew": "2102001"}, {"id": "2102101", "skill_id": "21021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2102101", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102101", "LCompareInfor": "BtlSkill_CompareInfor_2102101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102101;21021011;21021012", "skillEffectIdStrNew": "2102101;21021011;21021012"}, {"id": "2102102", "skill_id": "21021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2102102", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102102", "LCompareInfor": "BtlSkill_CompareInfor_2102102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102102;21021011;21021022", "skillEffectIdStrNew": "2102102;21021011;21021022"}, {"id": "2102103", "skill_id": "21021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2102103", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102103", "LCompareInfor": "BtlSkill_CompareInfor_2102103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102103;21021011;21021032", "skillEffectIdStrNew": "2102103;21021011;21021032"}, {"id": "2102104", "skill_id": "21021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2102104", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102104", "LCompareInfor": "BtlSkill_CompareInfor_2102104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102104;21021011;21021042", "skillEffectIdStrNew": "2102104;21021011;21021042"}, {"id": "2102105", "skill_id": "21021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2102105", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102105", "LCompareInfor": "BtlSkill_CompareInfor_2102105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102105;21021011;21021052", "skillEffectIdStrNew": "2102105;21021011;21021052"}, {"id": "2102106", "skill_id": "21021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2102106", "skillIconId": "skill_battle_21026", "LSkillDes": "BtlSkill_SkillDes_2102106", "LCompareInfor": "BtlSkill_CompareInfor_2102106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2102101", "skillEffectIdStr": "2102106;21021011;21021062", "skillEffectIdStrNew": "2102106;21021011;21021062"}, {"id": "2102201", "skill_id": "21022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2102201", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102201", "LCompareInfor": "BtlSkill_CompareInfor_2102201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102201", "skillEffectIdStrNew": "2102201"}, {"id": "2102202", "skill_id": "21022", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2102202", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102202", "LCompareInfor": "BtlSkill_CompareInfor_2102202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102202", "skillEffectIdStrNew": "2102202"}, {"id": "2102203", "skill_id": "21022", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2102203", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102203", "LCompareInfor": "BtlSkill_CompareInfor_2102203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102203", "skillEffectIdStrNew": "2102203"}, {"id": "2102204", "skill_id": "21022", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2102204", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102204", "LCompareInfor": "BtlSkill_CompareInfor_2102204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102204", "skillEffectIdStrNew": "2102204"}, {"id": "2102205", "skill_id": "21022", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2102205", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102205", "LCompareInfor": "BtlSkill_CompareInfor_2102205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102205", "skillEffectIdStrNew": "2102205"}, {"id": "2102206", "skill_id": "21022", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2102206", "skillIconId": "skill_battle_21027", "LSkillDes": "BtlSkill_SkillDes_2102206", "LCompareInfor": "BtlSkill_CompareInfor_2102206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102206", "skillEffectIdStrNew": "2102206"}, {"id": "2102301", "skill_id": "21023", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2102301", "skillIconId": "skill_battle_21028", "LSkillDes": "BtlSkill_SkillDes_2102301", "LCompareInfor": "BtlSkill_CompareInfor_2102301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102301", "skillEffectIdStrNew": "2102301"}, {"id": "2102302", "skill_id": "21023", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2102302", "skillIconId": "skill_battle_21028", "LSkillDes": "BtlSkill_SkillDes_2102302", "LCompareInfor": "BtlSkill_CompareInfor_2102302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102302", "skillEffectIdStrNew": "2102302"}, {"id": "2102303", "skill_id": "21023", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2102303", "skillIconId": "skill_battle_21028", "LSkillDes": "BtlSkill_SkillDes_2102303", "LCompareInfor": "BtlSkill_CompareInfor_2102303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102303", "skillEffectIdStrNew": "2102303"}, {"id": "2102304", "skill_id": "21023", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2102304", "skillIconId": "skill_battle_21028", "LSkillDes": "BtlSkill_SkillDes_2102304", "LCompareInfor": "BtlSkill_CompareInfor_2102304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102304", "skillEffectIdStrNew": "2102304"}, {"id": "2102305", "skill_id": "21023", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2102305", "skillIconId": "skill_battle_21028", "LSkillDes": "BtlSkill_SkillDes_2102305", "LCompareInfor": "BtlSkill_CompareInfor_2102305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2102305", "skillEffectIdStrNew": "2102305"}, {"id": "2103001", "skill_id": "21030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2103001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2103001", "LCompareInfor": "BtlSkill_CompareInfor_2103001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103001", "skillEffectIdStr": "2103001", "skillEffectIdStrNew": "2103001"}, {"id": "2103101", "skill_id": "21031", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2103101", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103101", "LCompareInfor": "BtlSkill_CompareInfor_2103101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103101;21031001;21031002;21031003", "skillEffectIdStrNew": "2103101;21031001;21031002;21031003"}, {"id": "2103102", "skill_id": "21031", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2103102", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103102", "LCompareInfor": "BtlSkill_CompareInfor_2103102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103102;21031001;21031002;21031003", "skillEffectIdStrNew": "2103102;21031001;21031002;21031003"}, {"id": "2103103", "skill_id": "21031", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2103103", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103103", "LCompareInfor": "BtlSkill_CompareInfor_2103103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103103;21031001;21031002;21031003", "skillEffectIdStrNew": "2103103;21031001;21031002;21031003"}, {"id": "2103104", "skill_id": "21031", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2103104", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103104", "LCompareInfor": "BtlSkill_CompareInfor_2103104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103104;21031001;21031002;21031003", "skillEffectIdStrNew": "2103104;21031001;21031002;21031003"}, {"id": "2103105", "skill_id": "21031", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2103105", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103105", "LCompareInfor": "BtlSkill_CompareInfor_2103105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103105;21031001;21031002;21031003", "skillEffectIdStrNew": "2103105;21031001;21031002;21031003"}, {"id": "2103106", "skill_id": "21031", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2103106", "skillIconId": "skill_battle_21036", "LSkillDes": "BtlSkill_SkillDes_2103106", "LCompareInfor": "BtlSkill_CompareInfor_2103106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2103101", "skillEffectIdStr": "2103106;21031001;21031002;21031003", "skillEffectIdStrNew": "2103106;21031001;21031002;21031003"}, {"id": "2103191", "skill_id": "21031", "skill_level": "91", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2103191", "skillEffectIdStr": "21031911;21031912;21031913;21031914;21031915;21031916", "skillEffectIdStrNew": "21031911;21031912;21031913;21031914;21031915;21031916"}, {"id": "2103192", "skill_id": "21031", "skill_level": "92", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2103192", "skillEffectIdStr": "21031921;21031922;21031923;21031924;21031925;21031926", "skillEffectIdStrNew": "21031921;21031922;21031923;21031924;21031925;21031926"}, {"id": "2103193", "skill_id": "21031", "skill_level": "93", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2103193", "skillEffectIdStr": "21031931;21031932;21031933;21031934;21031935;21031936", "skillEffectIdStrNew": "21031931;21031932;21031933;21031934;21031935;21031936"}, {"id": "2103201", "skill_id": "21032", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2103201", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103201", "LCompareInfor": "BtlSkill_CompareInfor_2103201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103201", "skillEffectIdStrNew": "2103201"}, {"id": "2103202", "skill_id": "21032", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2103202", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103202", "LCompareInfor": "BtlSkill_CompareInfor_2103202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103202", "skillEffectIdStrNew": "2103202"}, {"id": "2103203", "skill_id": "21032", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2103203", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103203", "LCompareInfor": "BtlSkill_CompareInfor_2103203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103203", "skillEffectIdStrNew": "2103203"}, {"id": "2103204", "skill_id": "21032", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2103204", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103204", "LCompareInfor": "BtlSkill_CompareInfor_2103204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103204", "skillEffectIdStrNew": "2103204"}, {"id": "2103205", "skill_id": "21032", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2103205", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103205", "LCompareInfor": "BtlSkill_CompareInfor_2103205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103205", "skillEffectIdStrNew": "2103205"}, {"id": "2103206", "skill_id": "21032", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2103206", "skillIconId": "skill_battle_21037", "LSkillDes": "BtlSkill_SkillDes_2103206", "LCompareInfor": "BtlSkill_CompareInfor_2103206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103206", "skillEffectIdStrNew": "2103206"}, {"id": "2103301", "skill_id": "21033", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2103301", "skillIconId": "skill_battle_21038", "LSkillDes": "BtlSkill_SkillDes_2103301", "LCompareInfor": "BtlSkill_CompareInfor_2103301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103301", "skillEffectIdStrNew": "2103301"}, {"id": "2103302", "skill_id": "21033", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2103302", "skillIconId": "skill_battle_21038", "LSkillDes": "BtlSkill_SkillDes_2103302", "LCompareInfor": "BtlSkill_CompareInfor_2103302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103302", "skillEffectIdStrNew": "2103302"}, {"id": "2103303", "skill_id": "21033", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2103303", "skillIconId": "skill_battle_21038", "LSkillDes": "BtlSkill_SkillDes_2103303", "LCompareInfor": "BtlSkill_CompareInfor_2103303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103303", "skillEffectIdStrNew": "2103303"}, {"id": "2103304", "skill_id": "21033", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2103304", "skillIconId": "skill_battle_21038", "LSkillDes": "BtlSkill_SkillDes_2103304", "LCompareInfor": "BtlSkill_CompareInfor_2103304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103304", "skillEffectIdStrNew": "2103304"}, {"id": "2103305", "skill_id": "21033", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2103305", "skillIconId": "skill_battle_21038", "LSkillDes": "BtlSkill_SkillDes_2103305", "LCompareInfor": "BtlSkill_CompareInfor_2103305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2103305", "skillEffectIdStrNew": "2103305"}, {"id": "2104001", "skill_id": "21040", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2104001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2104001", "skillEffectIdStr": "2104001", "skillEffectIdStrNew": "2104001"}, {"id": "2104101", "skill_id": "21041", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2104101", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104101", "LCompareInfor": "BtlSkill_CompareInfor_2104101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104101;2104100;21041011", "skillEffectIdStrNew": "2104101;2104100;21041011"}, {"id": "2104102", "skill_id": "21041", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2104102", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104102", "LCompareInfor": "BtlSkill_CompareInfor_2104102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104102;2104100;21041021", "skillEffectIdStrNew": "2104102;2104100;21041021"}, {"id": "2104103", "skill_id": "21041", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2104103", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104103", "LCompareInfor": "BtlSkill_CompareInfor_2104103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104103;2104100;21041031", "skillEffectIdStrNew": "2104103;2104100;21041031"}, {"id": "2104104", "skill_id": "21041", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2104104", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104104", "LCompareInfor": "BtlSkill_CompareInfor_2104104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104104;2104100;21041041", "skillEffectIdStrNew": "2104104;2104100;21041041"}, {"id": "2104105", "skill_id": "21041", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2104105", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104105", "LCompareInfor": "BtlSkill_CompareInfor_2104105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104105;2104100;21041051", "skillEffectIdStrNew": "2104105;2104100;21041051"}, {"id": "2104106", "skill_id": "21041", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2104106", "skillIconId": "skill_battle_21046", "LSkillDes": "BtlSkill_SkillDes_2104106", "LCompareInfor": "BtlSkill_CompareInfor_2104106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2104101", "skillEffectIdStr": "2104106;2104100;21041061", "skillEffectIdStrNew": "2104106;2104100;21041061"}, {"id": "2104201", "skill_id": "21042", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2104201", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104201", "LCompareInfor": "BtlSkill_CompareInfor_2104201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104201", "skillEffectIdStrNew": "2104201"}, {"id": "2104202", "skill_id": "21042", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2104202", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104202", "LCompareInfor": "BtlSkill_CompareInfor_2104202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104202", "skillEffectIdStrNew": "2104202"}, {"id": "2104203", "skill_id": "21042", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2104203", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104203", "LCompareInfor": "BtlSkill_CompareInfor_2104203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104203", "skillEffectIdStrNew": "2104203"}, {"id": "2104204", "skill_id": "21042", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2104204", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104204", "LCompareInfor": "BtlSkill_CompareInfor_2104204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104204", "skillEffectIdStrNew": "2104204"}, {"id": "2104205", "skill_id": "21042", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2104205", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104205", "LCompareInfor": "BtlSkill_CompareInfor_2104205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104205", "skillEffectIdStrNew": "2104205"}, {"id": "2104206", "skill_id": "21042", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2104206", "skillIconId": "skill_battle_21047", "LSkillDes": "BtlSkill_SkillDes_2104206", "LCompareInfor": "BtlSkill_CompareInfor_2104206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104206", "skillEffectIdStrNew": "2104206"}, {"id": "2104301", "skill_id": "21043", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2104301", "skillIconId": "skill_battle_21048", "LSkillDes": "BtlSkill_SkillDes_2104301", "LCompareInfor": "BtlSkill_CompareInfor_2104301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104301", "skillEffectIdStrNew": "2104301"}, {"id": "2104302", "skill_id": "21043", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2104302", "skillIconId": "skill_battle_21048", "LSkillDes": "BtlSkill_SkillDes_2104302", "LCompareInfor": "BtlSkill_CompareInfor_2104302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104302", "skillEffectIdStrNew": "2104302"}, {"id": "2104303", "skill_id": "21043", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2104303", "skillIconId": "skill_battle_21048", "LSkillDes": "BtlSkill_SkillDes_2104303", "LCompareInfor": "BtlSkill_CompareInfor_2104303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104303", "skillEffectIdStrNew": "2104303"}, {"id": "2104304", "skill_id": "21043", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2104304", "skillIconId": "skill_battle_21048", "LSkillDes": "BtlSkill_SkillDes_2104304", "LCompareInfor": "BtlSkill_CompareInfor_2104304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104304", "skillEffectIdStrNew": "2104304"}, {"id": "2104305", "skill_id": "21043", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2104305", "skillIconId": "skill_battle_21048", "LSkillDes": "BtlSkill_SkillDes_2104305", "LCompareInfor": "BtlSkill_CompareInfor_2104305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2104305", "skillEffectIdStrNew": "2104305"}, {"id": "2105001", "skill_id": "21050", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2105001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2105001", "skillEffectIdStr": "2105001", "skillEffectIdStrNew": "2105001"}, {"id": "2105101", "skill_id": "21051", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2105101", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105101", "LCompareInfor": "BtlSkill_CompareInfor_2105101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051011;21051012;21052011;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051011;21051012;21052011;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105102", "skill_id": "21051", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2105102", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105102", "LCompareInfor": "BtlSkill_CompareInfor_2105102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051021;21051012;21052021;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051021;21051012;21052021;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105103", "skill_id": "21051", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2105103", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105103", "LCompareInfor": "BtlSkill_CompareInfor_2105103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051031;21051012;21052031;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051031;21051012;21052031;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105104", "skill_id": "21051", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2105104", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105104", "LCompareInfor": "BtlSkill_CompareInfor_2105104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051041;21051012;21052041;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051041;21051012;21052041;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105105", "skill_id": "21051", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2105105", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105105", "LCompareInfor": "BtlSkill_CompareInfor_2105105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051051;21051012;21052051;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051051;21051012;21052051;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105106", "skill_id": "21051", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2105106", "skillIconId": "skill_battle_21056", "LSkillDes": "BtlSkill_SkillDes_2105106", "LCompareInfor": "BtlSkill_CompareInfor_2105106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "2.4", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2105101", "skillEffectIdStr": "21051061;21051012;21052061;21053011;21053021;21053031;21053041;21053051", "skillEffectIdStrNew": "21051061;21051012;21052061;21053011;21053021;21053031;21053041;21053051"}, {"id": "2105201", "skill_id": "21052", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2105201", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105201", "LCompareInfor": "BtlSkill_CompareInfor_2105201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105201", "skillEffectIdStrNew": "2105201"}, {"id": "2105202", "skill_id": "21052", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2105202", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105202", "LCompareInfor": "BtlSkill_CompareInfor_2105202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105202", "skillEffectIdStrNew": "2105202"}, {"id": "2105203", "skill_id": "21052", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2105203", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105203", "LCompareInfor": "BtlSkill_CompareInfor_2105203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105203", "skillEffectIdStrNew": "2105203"}, {"id": "2105204", "skill_id": "21052", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2105204", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105204", "LCompareInfor": "BtlSkill_CompareInfor_2105204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105204", "skillEffectIdStrNew": "2105204"}, {"id": "2105205", "skill_id": "21052", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2105205", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105205", "LCompareInfor": "BtlSkill_CompareInfor_2105205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105205", "skillEffectIdStrNew": "2105205"}, {"id": "2105206", "skill_id": "21052", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2105206", "skillIconId": "skill_battle_21057", "LSkillDes": "BtlSkill_SkillDes_2105206", "LCompareInfor": "BtlSkill_CompareInfor_2105206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105206", "skillEffectIdStrNew": "2105206"}, {"id": "2105301", "skill_id": "21053", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2105301", "skillIconId": "skill_battle_21058", "LSkillDes": "BtlSkill_SkillDes_2105301", "LCompareInfor": "BtlSkill_CompareInfor_2105301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105301", "skillEffectIdStrNew": "2105301"}, {"id": "2105302", "skill_id": "21053", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2105302", "skillIconId": "skill_battle_21058", "LSkillDes": "BtlSkill_SkillDes_2105302", "LCompareInfor": "BtlSkill_CompareInfor_2105302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105302", "skillEffectIdStrNew": "2105302"}, {"id": "2105303", "skill_id": "21053", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2105303", "skillIconId": "skill_battle_21058", "LSkillDes": "BtlSkill_SkillDes_2105303", "LCompareInfor": "BtlSkill_CompareInfor_2105303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105303", "skillEffectIdStrNew": "2105303"}, {"id": "2105304", "skill_id": "21053", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2105304", "skillIconId": "skill_battle_21058", "LSkillDes": "BtlSkill_SkillDes_2105304", "LCompareInfor": "BtlSkill_CompareInfor_2105304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105304", "skillEffectIdStrNew": "2105304"}, {"id": "2105305", "skill_id": "21053", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2105305", "skillIconId": "skill_battle_21058", "LSkillDes": "BtlSkill_SkillDes_2105305", "LCompareInfor": "BtlSkill_CompareInfor_2105305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2105305", "skillEffectIdStrNew": "2105305"}, {"id": "2106001", "skill_id": "21060", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2106001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106001", "skillEffectIdStr": "2106001", "skillEffectIdStrNew": "2106001"}, {"id": "2106101", "skill_id": "21061", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2106101", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106101", "LCompareInfor": "BtlSkill_CompareInfor_2106101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061011;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061011;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106102", "skill_id": "21061", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2106102", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106102", "LCompareInfor": "BtlSkill_CompareInfor_2106102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061021;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061021;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106103", "skill_id": "21061", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2106103", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106103", "LCompareInfor": "BtlSkill_CompareInfor_2106103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061031;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061031;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106104", "skill_id": "21061", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2106104", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106104", "LCompareInfor": "BtlSkill_CompareInfor_2106104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061041;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061041;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106105", "skill_id": "21061", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2106105", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106105", "LCompareInfor": "BtlSkill_CompareInfor_2106105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061051;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061051;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106106", "skill_id": "21061", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2106106", "skillIconId": "skill_battle_21066", "LSkillDes": "BtlSkill_SkillDes_2106106", "LCompareInfor": "BtlSkill_CompareInfor_2106106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2106101", "skillEffectIdStr": "21061061;21061012;21063011;21063021;21063031;21063041;21063051", "skillEffectIdStrNew": "21061061;21061012;21063011;21063021;21063031;21063041;21063051"}, {"id": "2106201", "skill_id": "21062", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2106201", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106201", "LCompareInfor": "BtlSkill_CompareInfor_2106201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106201", "skillEffectIdStrNew": "2106201"}, {"id": "2106202", "skill_id": "21062", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2106202", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106202", "LCompareInfor": "BtlSkill_CompareInfor_2106202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106202", "skillEffectIdStrNew": "2106202"}, {"id": "2106203", "skill_id": "21062", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2106203", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106203", "LCompareInfor": "BtlSkill_CompareInfor_2106203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106203", "skillEffectIdStrNew": "2106203"}, {"id": "2106204", "skill_id": "21062", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2106204", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106204", "LCompareInfor": "BtlSkill_CompareInfor_2106204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106204", "skillEffectIdStrNew": "2106204"}, {"id": "2106205", "skill_id": "21062", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2106205", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106205", "LCompareInfor": "BtlSkill_CompareInfor_2106205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106205", "skillEffectIdStrNew": "2106205"}, {"id": "2106206", "skill_id": "21062", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2106206", "skillIconId": "skill_battle_21067", "LSkillDes": "BtlSkill_SkillDes_2106206", "LCompareInfor": "BtlSkill_CompareInfor_2106206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106206", "skillEffectIdStrNew": "2106206"}, {"id": "2106301", "skill_id": "21063", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2106301", "skillIconId": "skill_battle_21068", "LSkillDes": "BtlSkill_SkillDes_2106301", "LCompareInfor": "BtlSkill_CompareInfor_2106301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106301", "skillEffectIdStrNew": "2106301"}, {"id": "2106302", "skill_id": "21063", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2106302", "skillIconId": "skill_battle_21068", "LSkillDes": "BtlSkill_SkillDes_2106302", "LCompareInfor": "BtlSkill_CompareInfor_2106302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106302", "skillEffectIdStrNew": "2106302"}, {"id": "2106303", "skill_id": "21063", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2106303", "skillIconId": "skill_battle_21068", "LSkillDes": "BtlSkill_SkillDes_2106303", "LCompareInfor": "BtlSkill_CompareInfor_2106303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106303", "skillEffectIdStrNew": "2106303"}, {"id": "2106304", "skill_id": "21063", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2106304", "skillIconId": "skill_battle_21068", "LSkillDes": "BtlSkill_SkillDes_2106304", "LCompareInfor": "BtlSkill_CompareInfor_2106304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106304", "skillEffectIdStrNew": "2106304"}, {"id": "2106305", "skill_id": "21063", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2106305", "skillIconId": "skill_battle_21068", "LSkillDes": "BtlSkill_SkillDes_2106305", "LCompareInfor": "BtlSkill_CompareInfor_2106305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2106305", "skillEffectIdStrNew": "2106305"}, {"id": "2201001", "skill_id": "22010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2201001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2201001", "LCompareInfor": "BtlSkill_CompareInfor_2201001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2201001", "skillEffectIdStr": "2201001", "skillEffectIdStrNew": "2201001"}, {"id": "2201101", "skill_id": "22011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2201101", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201101", "LCompareInfor": "BtlSkill_CompareInfor_2201101", "power": "600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011011;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011011;22011012"}, {"id": "2201102", "skill_id": "22011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2201102", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201102", "LCompareInfor": "BtlSkill_CompareInfor_2201102", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011021;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011021;22011012"}, {"id": "2201103", "skill_id": "22011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2201103", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201103", "LCompareInfor": "BtlSkill_CompareInfor_2201103", "power": "1000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011031;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011031;22011012"}, {"id": "2201104", "skill_id": "22011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2201104", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201104", "LCompareInfor": "BtlSkill_CompareInfor_2201104", "power": "1200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011041;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011041;22011012"}, {"id": "2201105", "skill_id": "22011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2201105", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201105", "LCompareInfor": "BtlSkill_CompareInfor_2201105", "power": "1400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011051;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011051;22011012"}, {"id": "2201106", "skill_id": "22011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2201106", "skillIconId": "skill_battle_10", "LSkillDes": "BtlSkill_SkillDes_2201106", "LCompareInfor": "BtlSkill_CompareInfor_2201106", "power": "1600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2201101", "skillEffectIdStr": "22011013;22011023;22011033;22011043;22011053;22011061;22011012", "skillEffectIdStrNew": "22011013;22011023;22011033;22011043;22011053;22011061;22011012"}, {"id": "2201201", "skill_id": "22012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2201201", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201201", "LCompareInfor": "BtlSkill_CompareInfor_2201201", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201201", "skillEffectIdStrNew": "2201201"}, {"id": "2201202", "skill_id": "22012", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2201202", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201202", "LCompareInfor": "BtlSkill_CompareInfor_2201202", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201202", "skillEffectIdStrNew": "2201202"}, {"id": "2201203", "skill_id": "22012", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2201203", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201203", "LCompareInfor": "BtlSkill_CompareInfor_2201203", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201203", "skillEffectIdStrNew": "2201203"}, {"id": "2201204", "skill_id": "22012", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2201204", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201204", "LCompareInfor": "BtlSkill_CompareInfor_2201204", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201204", "skillEffectIdStrNew": "2201204"}, {"id": "2201205", "skill_id": "22012", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2201205", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201205", "LCompareInfor": "BtlSkill_CompareInfor_2201205", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201205", "skillEffectIdStrNew": "2201205"}, {"id": "2201206", "skill_id": "22012", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2201206", "skillIconId": "skill_battle_54", "LSkillDes": "BtlSkill_SkillDes_2201206", "LCompareInfor": "BtlSkill_CompareInfor_2201206", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201206", "skillEffectIdStrNew": "2201206"}, {"id": "2201301", "skill_id": "22013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2201301", "skillIconId": "skill_battle_38", "LSkillDes": "BtlSkill_SkillDes_2201301", "LCompareInfor": "BtlSkill_CompareInfor_2201301", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201301", "skillEffectIdStrNew": "2201301"}, {"id": "2201302", "skill_id": "22013", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2201302", "skillIconId": "skill_battle_38", "LSkillDes": "BtlSkill_SkillDes_2201302", "LCompareInfor": "BtlSkill_CompareInfor_2201302", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201302", "skillEffectIdStrNew": "2201302"}, {"id": "2201303", "skill_id": "22013", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2201303", "skillIconId": "skill_battle_38", "LSkillDes": "BtlSkill_SkillDes_2201303", "LCompareInfor": "BtlSkill_CompareInfor_2201303", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201303", "skillEffectIdStrNew": "2201303"}, {"id": "2201304", "skill_id": "22013", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2201304", "skillIconId": "skill_battle_38", "LSkillDes": "BtlSkill_SkillDes_2201304", "LCompareInfor": "BtlSkill_CompareInfor_2201304", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201304", "skillEffectIdStrNew": "2201304"}, {"id": "2201305", "skill_id": "22013", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2201305", "skillIconId": "skill_battle_38", "LSkillDes": "BtlSkill_SkillDes_2201305", "LCompareInfor": "BtlSkill_CompareInfor_2201305", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2201305", "skillEffectIdStrNew": "2201305"}, {"id": "2202001", "skill_id": "22020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2202001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2202001", "LCompareInfor": "BtlSkill_CompareInfor_2202001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2202001", "skillEffectIdStr": "2202001", "skillEffectIdStrNew": "2202001"}, {"id": "2202101", "skill_id": "22021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2202101", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202101", "LCompareInfor": "BtlSkill_CompareInfor_2202101", "power": "600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021011", "skillEffectIdStrNew": "22021011"}, {"id": "2202102", "skill_id": "22021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2202102", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202102", "LCompareInfor": "BtlSkill_CompareInfor_2202102", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021021", "skillEffectIdStrNew": "22021021"}, {"id": "2202103", "skill_id": "22021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2202103", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202103", "LCompareInfor": "BtlSkill_CompareInfor_2202103", "power": "1000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021031", "skillEffectIdStrNew": "22021031"}, {"id": "2202104", "skill_id": "22021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2202104", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202104", "LCompareInfor": "BtlSkill_CompareInfor_2202104", "power": "1200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021041", "skillEffectIdStrNew": "22021041"}, {"id": "2202105", "skill_id": "22021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2202105", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202105", "LCompareInfor": "BtlSkill_CompareInfor_2202105", "power": "1400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021051", "skillEffectIdStrNew": "22021051"}, {"id": "2202106", "skill_id": "22021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2202106", "skillIconId": "skill_battle_58", "LSkillDes": "BtlSkill_SkillDes_2202106", "LCompareInfor": "BtlSkill_CompareInfor_2202106", "power": "1600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2202101", "skillEffectIdStr": "22021061", "skillEffectIdStrNew": "22021061"}, {"id": "2202201", "skill_id": "22022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2202201", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202201", "LCompareInfor": "BtlSkill_CompareInfor_2202201", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202201", "skillEffectIdStrNew": "2202201"}, {"id": "2202202", "skill_id": "22022", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2202202", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202202", "LCompareInfor": "BtlSkill_CompareInfor_2202202", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202202", "skillEffectIdStrNew": "2202202"}, {"id": "2202203", "skill_id": "22022", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2202203", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202203", "LCompareInfor": "BtlSkill_CompareInfor_2202203", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202203", "skillEffectIdStrNew": "2202203"}, {"id": "2202204", "skill_id": "22022", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2202204", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202204", "LCompareInfor": "BtlSkill_CompareInfor_2202204", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202204", "skillEffectIdStrNew": "2202204"}, {"id": "2202205", "skill_id": "22022", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2202205", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202205", "LCompareInfor": "BtlSkill_CompareInfor_2202205", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202205", "skillEffectIdStrNew": "2202205"}, {"id": "2202206", "skill_id": "22022", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2202206", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_2202206", "LCompareInfor": "BtlSkill_CompareInfor_2202206", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202206", "skillEffectIdStrNew": "2202206"}, {"id": "2202301", "skill_id": "22023", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2202301", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_2202301", "LCompareInfor": "BtlSkill_CompareInfor_2202301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202301", "skillEffectIdStrNew": "2202301"}, {"id": "2202302", "skill_id": "22023", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2202302", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_2202302", "LCompareInfor": "BtlSkill_CompareInfor_2202302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202302", "skillEffectIdStrNew": "2202302"}, {"id": "2202303", "skill_id": "22023", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2202303", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_2202303", "LCompareInfor": "BtlSkill_CompareInfor_2202303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202303", "skillEffectIdStrNew": "2202303"}, {"id": "2202304", "skill_id": "22023", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2202304", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_2202304", "LCompareInfor": "BtlSkill_CompareInfor_2202304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202304", "skillEffectIdStrNew": "2202304"}, {"id": "2202305", "skill_id": "22023", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2202305", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_2202305", "LCompareInfor": "BtlSkill_CompareInfor_2202305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2202305", "skillEffectIdStrNew": "2202305"}, {"id": "2203001", "skill_id": "22030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2203001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2203001", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203001", "skillEffectIdStr": "2203001", "skillEffectIdStrNew": "2203001"}, {"id": "2203101", "skill_id": "22031", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2203101", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203101", "LCompareInfor": "BtlSkill_CompareInfor_2203101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203101;2203290", "skillEffectIdStrNew": "2203101;2203290"}, {"id": "2203102", "skill_id": "22031", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2203102", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203102", "LCompareInfor": "BtlSkill_CompareInfor_2203102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203102;2203290", "skillEffectIdStrNew": "2203102;2203290"}, {"id": "2203103", "skill_id": "22031", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2203103", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203103", "LCompareInfor": "BtlSkill_CompareInfor_2203103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203103;2203290", "skillEffectIdStrNew": "2203103;2203290"}, {"id": "2203104", "skill_id": "22031", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2203104", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203104", "LCompareInfor": "BtlSkill_CompareInfor_2203104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203104;2203290", "skillEffectIdStrNew": "2203104;2203290"}, {"id": "2203105", "skill_id": "22031", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2203105", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203105", "LCompareInfor": "BtlSkill_CompareInfor_2203105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203105;2203290", "skillEffectIdStrNew": "2203105;2203290"}, {"id": "2203106", "skill_id": "22031", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2203106", "skillIconId": "skill_battle_22036", "LSkillDes": "BtlSkill_SkillDes_2203106", "LCompareInfor": "BtlSkill_CompareInfor_2203106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.2", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2203101", "skillEffectIdStr": "2203106;2203290", "skillEffectIdStrNew": "2203106;2203290"}, {"id": "2203201", "skill_id": "22032", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2203201", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203201", "LCompareInfor": "BtlSkill_CompareInfor_2203201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203201", "skillEffectIdStrNew": "2203201"}, {"id": "2203202", "skill_id": "22032", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2203202", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203202", "LCompareInfor": "BtlSkill_CompareInfor_2203202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203202", "skillEffectIdStrNew": "2203202"}, {"id": "2203203", "skill_id": "22032", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2203203", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203203", "LCompareInfor": "BtlSkill_CompareInfor_2203203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203203", "skillEffectIdStrNew": "2203203"}, {"id": "2203204", "skill_id": "22032", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2203204", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203204", "LCompareInfor": "BtlSkill_CompareInfor_2203204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203204", "skillEffectIdStrNew": "2203204"}, {"id": "2203205", "skill_id": "22032", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2203205", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203205", "LCompareInfor": "BtlSkill_CompareInfor_2203205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203205", "skillEffectIdStrNew": "2203205"}, {"id": "2203206", "skill_id": "22032", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2203206", "skillIconId": "skill_battle_22037", "LSkillDes": "BtlSkill_SkillDes_2203206", "LCompareInfor": "BtlSkill_CompareInfor_2203206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203206", "skillEffectIdStrNew": "2203206"}, {"id": "2203291", "skill_id": "22032", "skill_level": "91", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "22032911;22032912;22032913;22032914;22032915;22032916", "skillEffectIdStrNew": "22032911;22032912;22032913;22032914;22032915;22032916"}, {"id": "2203301", "skill_id": "22033", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2203301", "skillIconId": "skill_battle_22038", "LSkillDes": "BtlSkill_SkillDes_2203301", "LCompareInfor": "BtlSkill_CompareInfor_2203301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203301", "skillEffectIdStrNew": "2203301"}, {"id": "2203302", "skill_id": "22033", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2203302", "skillIconId": "skill_battle_22038", "LSkillDes": "BtlSkill_SkillDes_2203302", "LCompareInfor": "BtlSkill_CompareInfor_2203302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203302", "skillEffectIdStrNew": "2203302"}, {"id": "2203303", "skill_id": "22033", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2203303", "skillIconId": "skill_battle_22038", "LSkillDes": "BtlSkill_SkillDes_2203303", "LCompareInfor": "BtlSkill_CompareInfor_2203303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203303", "skillEffectIdStrNew": "2203303"}, {"id": "2203304", "skill_id": "22033", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2203304", "skillIconId": "skill_battle_22038", "LSkillDes": "BtlSkill_SkillDes_2203304", "LCompareInfor": "BtlSkill_CompareInfor_2203304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203304", "skillEffectIdStrNew": "2203304"}, {"id": "2203305", "skill_id": "22033", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2203305", "skillIconId": "skill_battle_22038", "LSkillDes": "BtlSkill_SkillDes_2203305", "LCompareInfor": "BtlSkill_CompareInfor_2203305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2203305", "skillEffectIdStrNew": "2203305"}, {"id": "2204001", "skill_id": "22040", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2204001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2204001", "skillEffectIdStr": "2204001", "skillEffectIdStrNew": "2204001"}, {"id": "2204002", "skill_id": "22040", "skill_level": "1", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2204002", "skillEffectIdStr": "2204002;22040021;22040022;22040023;22040024;22040025", "skillEffectIdStrNew": "2204002;22040021;22040022;22040023;22040024;22040025"}, {"id": "2204101", "skill_id": "22041", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2204101", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204101", "LCompareInfor": "BtlSkill_CompareInfor_2204101", "power": "800", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041011;22041012;22041013", "skillEffectIdStrNew": "22041011;22041012;22041013"}, {"id": "2204102", "skill_id": "22041", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2204102", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204102", "LCompareInfor": "BtlSkill_CompareInfor_2204102", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041021;22041022;22041023", "skillEffectIdStrNew": "22041021;22041022;22041023"}, {"id": "2204103", "skill_id": "22041", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2204103", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204103", "LCompareInfor": "BtlSkill_CompareInfor_2204103", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041031;22041032;22041033", "skillEffectIdStrNew": "22041031;22041032;22041033"}, {"id": "2204104", "skill_id": "22041", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2204104", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204104", "LCompareInfor": "BtlSkill_CompareInfor_2204104", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041041;22041042;22041043", "skillEffectIdStrNew": "22041041;22041042;22041043"}, {"id": "2204105", "skill_id": "22041", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2204105", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204105", "LCompareInfor": "BtlSkill_CompareInfor_2204105", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041051;22041052;22041053", "skillEffectIdStrNew": "22041051;22041052;22041053"}, {"id": "2204106", "skill_id": "22041", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2204106", "skillIconId": "skill_battle_22046", "LSkillDes": "BtlSkill_SkillDes_2204106", "LCompareInfor": "BtlSkill_CompareInfor_2204106", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2204101", "skillEffectIdStr": "22041061;22041062;22041063", "skillEffectIdStrNew": "22041061;22041062;22041063"}, {"id": "2204201", "skill_id": "22042", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2204201", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204201", "LCompareInfor": "BtlSkill_CompareInfor_2204201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204201", "skillEffectIdStrNew": "2204201"}, {"id": "2204202", "skill_id": "22042", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2204202", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204202", "LCompareInfor": "BtlSkill_CompareInfor_2204202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204202", "skillEffectIdStrNew": "2204202"}, {"id": "2204203", "skill_id": "22042", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2204203", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204203", "LCompareInfor": "BtlSkill_CompareInfor_2204203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204203", "skillEffectIdStrNew": "2204203"}, {"id": "2204204", "skill_id": "22042", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2204204", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204204", "LCompareInfor": "BtlSkill_CompareInfor_2204204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204204", "skillEffectIdStrNew": "2204204"}, {"id": "2204205", "skill_id": "22042", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2204205", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204205", "LCompareInfor": "BtlSkill_CompareInfor_2204205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204205", "skillEffectIdStrNew": "2204205"}, {"id": "2204206", "skill_id": "22042", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2204206", "skillIconId": "skill_battle_22047", "LSkillDes": "BtlSkill_SkillDes_2204206", "LCompareInfor": "BtlSkill_CompareInfor_2204206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204206", "skillEffectIdStrNew": "2204206"}, {"id": "2204301", "skill_id": "22043", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2204301", "skillIconId": "skill_battle_22048", "LSkillDes": "BtlSkill_SkillDes_2204301", "LCompareInfor": "BtlSkill_CompareInfor_2204301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204300;2204301", "skillEffectIdStrNew": "2204300;2204301"}, {"id": "2204302", "skill_id": "22043", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2204302", "skillIconId": "skill_battle_22048", "LSkillDes": "BtlSkill_SkillDes_2204302", "LCompareInfor": "BtlSkill_CompareInfor_2204302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204300;2204302", "skillEffectIdStrNew": "2204300;2204302"}, {"id": "2204303", "skill_id": "22043", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2204303", "skillIconId": "skill_battle_22048", "LSkillDes": "BtlSkill_SkillDes_2204303", "LCompareInfor": "BtlSkill_CompareInfor_2204303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204300;2204303", "skillEffectIdStrNew": "2204300;2204303"}, {"id": "2204304", "skill_id": "22043", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2204304", "skillIconId": "skill_battle_22048", "LSkillDes": "BtlSkill_SkillDes_2204304", "LCompareInfor": "BtlSkill_CompareInfor_2204304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204300;2204304", "skillEffectIdStrNew": "2204300;2204304"}, {"id": "2204305", "skill_id": "22043", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2204305", "skillIconId": "skill_battle_22048", "LSkillDes": "BtlSkill_SkillDes_2204305", "LCompareInfor": "BtlSkill_CompareInfor_2204305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2204300;2204305", "skillEffectIdStrNew": "2204300;2204305"}, {"id": "2205001", "skill_id": "22050", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2205001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2205001", "skillEffectIdStr": "2205001", "skillEffectIdStrNew": "2205001"}, {"id": "2205101", "skill_id": "22051", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2205101", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205101", "LCompareInfor": "BtlSkill_CompareInfor_2205101", "power": "800", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205101;22051011", "skillEffectIdStrNew": "2205100;2205101;22051011"}, {"id": "2205102", "skill_id": "22051", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2205102", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205102", "LCompareInfor": "BtlSkill_CompareInfor_2205102", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205102;22051021", "skillEffectIdStrNew": "2205100;2205102;22051021"}, {"id": "2205103", "skill_id": "22051", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2205103", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205103", "LCompareInfor": "BtlSkill_CompareInfor_2205103", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205103;22051031", "skillEffectIdStrNew": "2205100;2205103;22051031"}, {"id": "2205104", "skill_id": "22051", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2205104", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205104", "LCompareInfor": "BtlSkill_CompareInfor_2205104", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205104;22051041", "skillEffectIdStrNew": "2205100;2205104;22051041"}, {"id": "2205105", "skill_id": "22051", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2205105", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205105", "LCompareInfor": "BtlSkill_CompareInfor_2205105", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205105;22051051", "skillEffectIdStrNew": "2205100;2205105;22051051"}, {"id": "2205106", "skill_id": "22051", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2205106", "skillIconId": "skill_battle_22056", "LSkillDes": "BtlSkill_SkillDes_2205106", "LCompareInfor": "BtlSkill_CompareInfor_2205106", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2205101", "skillEffectIdStr": "2205100;2205106;22051061", "skillEffectIdStrNew": "2205100;2205106;22051061"}, {"id": "2205201", "skill_id": "22052", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2205201", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205201", "LCompareInfor": "BtlSkill_CompareInfor_2205201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205201", "skillEffectIdStrNew": "2205201"}, {"id": "2205202", "skill_id": "22052", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2205202", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205202", "LCompareInfor": "BtlSkill_CompareInfor_2205202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205202", "skillEffectIdStrNew": "2205202"}, {"id": "2205203", "skill_id": "22052", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2205203", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205203", "LCompareInfor": "BtlSkill_CompareInfor_2205203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205203", "skillEffectIdStrNew": "2205203"}, {"id": "2205204", "skill_id": "22052", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2205204", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205204", "LCompareInfor": "BtlSkill_CompareInfor_2205204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205204", "skillEffectIdStrNew": "2205204"}, {"id": "2205205", "skill_id": "22052", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2205205", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205205", "LCompareInfor": "BtlSkill_CompareInfor_2205205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205205", "skillEffectIdStrNew": "2205205"}, {"id": "2205206", "skill_id": "22052", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2205206", "skillIconId": "skill_battle_22057", "LSkillDes": "BtlSkill_SkillDes_2205206", "LCompareInfor": "BtlSkill_CompareInfor_2205206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205206", "skillEffectIdStrNew": "2205206"}, {"id": "2205301", "skill_id": "22053", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2205301", "skillIconId": "skill_battle_22058", "LSkillDes": "BtlSkill_SkillDes_2205301", "LCompareInfor": "BtlSkill_CompareInfor_2205301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;2", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205301;2205300;2205390", "skillEffectIdStrNew": "2205301;2205300;2205390"}, {"id": "2205302", "skill_id": "22053", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2205302", "skillIconId": "skill_battle_22058", "LSkillDes": "BtlSkill_SkillDes_2205302", "LCompareInfor": "BtlSkill_CompareInfor_2205302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;2", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205302;2205300;2205390", "skillEffectIdStrNew": "2205302;2205300;2205390"}, {"id": "2205303", "skill_id": "22053", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2205303", "skillIconId": "skill_battle_22058", "LSkillDes": "BtlSkill_SkillDes_2205303", "LCompareInfor": "BtlSkill_CompareInfor_2205303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;2", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205303;2205300;2205390", "skillEffectIdStrNew": "2205303;2205300;2205390"}, {"id": "2205304", "skill_id": "22053", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2205304", "skillIconId": "skill_battle_22058", "LSkillDes": "BtlSkill_SkillDes_2205304", "LCompareInfor": "BtlSkill_CompareInfor_2205304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;2", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205304;2205300;2205390", "skillEffectIdStrNew": "2205304;2205300;2205390"}, {"id": "2205305", "skill_id": "22053", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2205305", "skillIconId": "skill_battle_22058", "LSkillDes": "BtlSkill_SkillDes_2205305", "LCompareInfor": "BtlSkill_CompareInfor_2205305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;2", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2205305;2205300;2205390", "skillEffectIdStrNew": "2205305;2205300;2205390"}, {"id": "2205390", "skill_id": "22053", "skill_level": "90", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "10", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2205001", "skillEffectIdStr": "2205001;22053901", "skillEffectIdStrNew": "2205001;22053901"}, {"id": "2206001", "skill_id": "22060", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2206001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2206001", "skillEffectIdStr": "2206001", "skillEffectIdStrNew": "2206001"}, {"id": "2206101", "skill_id": "22061", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2206101", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206101", "LCompareInfor": "BtlSkill_CompareInfor_2206101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061011;22061012;22061013", "skillEffectIdStrNew": "22061011;22061012;22061013"}, {"id": "2206102", "skill_id": "22061", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2206102", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206102", "LCompareInfor": "BtlSkill_CompareInfor_2206102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061021;22061012;22061023", "skillEffectIdStrNew": "22061021;22061012;22061023"}, {"id": "2206103", "skill_id": "22061", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2206103", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206103", "LCompareInfor": "BtlSkill_CompareInfor_2206103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061031;22061012;22061033", "skillEffectIdStrNew": "22061031;22061012;22061033"}, {"id": "2206104", "skill_id": "22061", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2206104", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206104", "LCompareInfor": "BtlSkill_CompareInfor_2206104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061041;22061012;22061043", "skillEffectIdStrNew": "22061041;22061012;22061043"}, {"id": "2206105", "skill_id": "22061", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2206105", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206105", "LCompareInfor": "BtlSkill_CompareInfor_2206105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061051;22061012;22061053", "skillEffectIdStrNew": "22061051;22061012;22061053"}, {"id": "2206106", "skill_id": "22061", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2206106", "skillIconId": "skill_battle_22066", "LSkillDes": "BtlSkill_SkillDes_2206106", "LCompareInfor": "BtlSkill_CompareInfor_2206106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.9", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "2206101", "skillEffectIdStr": "22061061;22061012;22061063", "skillEffectIdStrNew": "22061061;22061012;22061063"}, {"id": "2206201", "skill_id": "22062", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2206201", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206201", "LCompareInfor": "BtlSkill_CompareInfor_2206201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206201", "skillEffectIdStrNew": "2206201"}, {"id": "2206202", "skill_id": "22062", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2206202", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206202", "LCompareInfor": "BtlSkill_CompareInfor_2206202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206202", "skillEffectIdStrNew": "2206202"}, {"id": "2206203", "skill_id": "22062", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2206203", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206203", "LCompareInfor": "BtlSkill_CompareInfor_2206203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206203", "skillEffectIdStrNew": "2206203"}, {"id": "2206204", "skill_id": "22062", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2206204", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206204", "LCompareInfor": "BtlSkill_CompareInfor_2206204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206204", "skillEffectIdStrNew": "2206204"}, {"id": "2206205", "skill_id": "22062", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2206205", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206205", "LCompareInfor": "BtlSkill_CompareInfor_2206205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206205", "skillEffectIdStrNew": "2206205"}, {"id": "2206206", "skill_id": "22062", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2206206", "skillIconId": "skill_battle_22067", "LSkillDes": "BtlSkill_SkillDes_2206206", "LCompareInfor": "BtlSkill_CompareInfor_2206206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206206", "skillEffectIdStrNew": "2206206"}, {"id": "2206301", "skill_id": "22063", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2206301", "skillIconId": "skill_battle_22068", "LSkillDes": "BtlSkill_SkillDes_2206301", "LCompareInfor": "BtlSkill_CompareInfor_2206301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206301", "skillEffectIdStrNew": "2206301"}, {"id": "2206302", "skill_id": "22063", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2206302", "skillIconId": "skill_battle_22068", "LSkillDes": "BtlSkill_SkillDes_2206302", "LCompareInfor": "BtlSkill_CompareInfor_2206302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206302", "skillEffectIdStrNew": "2206302"}, {"id": "2206303", "skill_id": "22063", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2206303", "skillIconId": "skill_battle_22068", "LSkillDes": "BtlSkill_SkillDes_2206303", "LCompareInfor": "BtlSkill_CompareInfor_2206303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206303", "skillEffectIdStrNew": "2206303"}, {"id": "2206304", "skill_id": "22063", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2206304", "skillIconId": "skill_battle_22068", "LSkillDes": "BtlSkill_SkillDes_2206304", "LCompareInfor": "BtlSkill_CompareInfor_2206304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206304", "skillEffectIdStrNew": "2206304"}, {"id": "2206305", "skill_id": "22063", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2206305", "skillIconId": "skill_battle_22068", "LSkillDes": "BtlSkill_SkillDes_2206305", "LCompareInfor": "BtlSkill_CompareInfor_2206305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2206305", "skillEffectIdStrNew": "2206305"}, {"id": "2301001", "skill_id": "23010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2301001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2301001", "LCompareInfor": "BtlSkill_CompareInfor_2301001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2301001", "skillEffectIdStr": "2301001", "skillEffectIdStrNew": "2301001"}, {"id": "2301101", "skill_id": "23011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2301101", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301101", "LCompareInfor": "BtlSkill_CompareInfor_2301101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301101", "skillEffectIdStrNew": "2301101"}, {"id": "2301102", "skill_id": "23011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2301102", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301102", "LCompareInfor": "BtlSkill_CompareInfor_2301102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301102", "skillEffectIdStrNew": "2301102"}, {"id": "2301103", "skill_id": "23011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2301103", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301103", "LCompareInfor": "BtlSkill_CompareInfor_2301103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301103", "skillEffectIdStrNew": "2301103"}, {"id": "2301104", "skill_id": "23011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2301104", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301104", "LCompareInfor": "BtlSkill_CompareInfor_2301104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301104", "skillEffectIdStrNew": "2301104"}, {"id": "2301105", "skill_id": "23011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2301105", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301105", "LCompareInfor": "BtlSkill_CompareInfor_2301105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301105", "skillEffectIdStrNew": "2301105"}, {"id": "2301106", "skill_id": "23011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2301106", "skillIconId": "skill_battle_23016", "LSkillDes": "BtlSkill_SkillDes_2301106", "LCompareInfor": "BtlSkill_CompareInfor_2301106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2301101", "skillEffectIdStr": "2301106", "skillEffectIdStrNew": "2301106"}, {"id": "2301201", "skill_id": "23012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2301201", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301201", "LCompareInfor": "BtlSkill_CompareInfor_2301201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301201", "skillEffectIdStrNew": "2301201"}, {"id": "2301202", "skill_id": "23012", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2301202", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301202", "LCompareInfor": "BtlSkill_CompareInfor_2301202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301202", "skillEffectIdStrNew": "2301202"}, {"id": "2301203", "skill_id": "23012", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2301203", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301203", "LCompareInfor": "BtlSkill_CompareInfor_2301203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301203", "skillEffectIdStrNew": "2301203"}, {"id": "2301204", "skill_id": "23012", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2301204", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301204", "LCompareInfor": "BtlSkill_CompareInfor_2301204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301204", "skillEffectIdStrNew": "2301204"}, {"id": "2301205", "skill_id": "23012", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2301205", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301205", "LCompareInfor": "BtlSkill_CompareInfor_2301205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301205", "skillEffectIdStrNew": "2301205"}, {"id": "2301206", "skill_id": "23012", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2301206", "skillIconId": "skill_battle_23017", "LSkillDes": "BtlSkill_SkillDes_2301206", "LCompareInfor": "BtlSkill_CompareInfor_2301206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301206", "skillEffectIdStrNew": "2301206"}, {"id": "2301301", "skill_id": "23013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2301301", "skillIconId": "skill_battle_23018", "LSkillDes": "BtlSkill_SkillDes_2301301", "LCompareInfor": "BtlSkill_CompareInfor_2301301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301301", "skillEffectIdStrNew": "2301301"}, {"id": "2301302", "skill_id": "23013", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2301302", "skillIconId": "skill_battle_23018", "LSkillDes": "BtlSkill_SkillDes_2301302", "LCompareInfor": "BtlSkill_CompareInfor_2301302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301302", "skillEffectIdStrNew": "2301302"}, {"id": "2301303", "skill_id": "23013", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2301303", "skillIconId": "skill_battle_23018", "LSkillDes": "BtlSkill_SkillDes_2301303", "LCompareInfor": "BtlSkill_CompareInfor_2301303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301303", "skillEffectIdStrNew": "2301303"}, {"id": "2301304", "skill_id": "23013", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2301304", "skillIconId": "skill_battle_23018", "LSkillDes": "BtlSkill_SkillDes_2301304", "LCompareInfor": "BtlSkill_CompareInfor_2301304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301304", "skillEffectIdStrNew": "2301304"}, {"id": "2301305", "skill_id": "23013", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2301305", "skillIconId": "skill_battle_23018", "LSkillDes": "BtlSkill_SkillDes_2301305", "LCompareInfor": "BtlSkill_CompareInfor_2301305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2301305", "skillEffectIdStrNew": "2301305"}, {"id": "2302001", "skill_id": "23020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2302001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2302001", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2302001", "skillEffectIdStr": "2302001", "skillEffectIdStrNew": "2302001"}, {"id": "2302101", "skill_id": "23021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2302101", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302101", "LCompareInfor": "BtlSkill_CompareInfor_2302101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302101;23021011;23021012", "skillEffectIdStrNew": "2302101;23021011;23021012"}, {"id": "2302102", "skill_id": "23021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2302102", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302102", "LCompareInfor": "BtlSkill_CompareInfor_2302102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302102;23021011;23021022", "skillEffectIdStrNew": "2302102;23021011;23021022"}, {"id": "2302103", "skill_id": "23021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2302103", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302103", "LCompareInfor": "BtlSkill_CompareInfor_2302103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302103;23021011;23021032", "skillEffectIdStrNew": "2302103;23021011;23021032"}, {"id": "2302104", "skill_id": "23021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2302104", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302104", "LCompareInfor": "BtlSkill_CompareInfor_2302104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302104;23021011;23021042", "skillEffectIdStrNew": "2302104;23021011;23021042"}, {"id": "2302105", "skill_id": "23021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2302105", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302105", "LCompareInfor": "BtlSkill_CompareInfor_2302105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302105;23021051;23021052", "skillEffectIdStrNew": "2302105;23021051;23021052"}, {"id": "2302106", "skill_id": "23021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2302106", "skillIconId": "skill_battle_23026", "LSkillDes": "BtlSkill_SkillDes_2302106", "LCompareInfor": "BtlSkill_CompareInfor_2302106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.1", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2302101", "skillEffectIdStr": "2302106;23021051;23021062", "skillEffectIdStrNew": "2302106;23021051;23021062"}, {"id": "2302201", "skill_id": "23022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2302201", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302201", "LCompareInfor": "BtlSkill_CompareInfor_2302201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302201", "skillEffectIdStrNew": "2302201"}, {"id": "2302202", "skill_id": "23022", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2302202", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302202", "LCompareInfor": "BtlSkill_CompareInfor_2302202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302202", "skillEffectIdStrNew": "2302202"}, {"id": "2302203", "skill_id": "23022", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2302203", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302203", "LCompareInfor": "BtlSkill_CompareInfor_2302203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302203", "skillEffectIdStrNew": "2302203"}, {"id": "2302204", "skill_id": "23022", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2302204", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302204", "LCompareInfor": "BtlSkill_CompareInfor_2302204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302204", "skillEffectIdStrNew": "2302204"}, {"id": "2302205", "skill_id": "23022", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2302205", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302205", "LCompareInfor": "BtlSkill_CompareInfor_2302205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302205", "skillEffectIdStrNew": "2302205"}, {"id": "2302206", "skill_id": "23022", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2302206", "skillIconId": "skill_battle_23027", "LSkillDes": "BtlSkill_SkillDes_2302206", "LCompareInfor": "BtlSkill_CompareInfor_2302206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302206", "skillEffectIdStrNew": "2302206"}, {"id": "2302301", "skill_id": "23023", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2302301", "skillIconId": "skill_battle_23028", "LSkillDes": "BtlSkill_SkillDes_2302301", "LCompareInfor": "BtlSkill_CompareInfor_2302301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302301", "skillEffectIdStrNew": "2302301"}, {"id": "2302302", "skill_id": "23023", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2302302", "skillIconId": "skill_battle_23028", "LSkillDes": "BtlSkill_SkillDes_2302302", "LCompareInfor": "BtlSkill_CompareInfor_2302302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302302", "skillEffectIdStrNew": "2302302"}, {"id": "2302303", "skill_id": "23023", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2302303", "skillIconId": "skill_battle_23028", "LSkillDes": "BtlSkill_SkillDes_2302303", "LCompareInfor": "BtlSkill_CompareInfor_2302303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302303", "skillEffectIdStrNew": "2302303"}, {"id": "2302304", "skill_id": "23023", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2302304", "skillIconId": "skill_battle_23028", "LSkillDes": "BtlSkill_SkillDes_2302304", "LCompareInfor": "BtlSkill_CompareInfor_2302304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302304", "skillEffectIdStrNew": "2302304"}, {"id": "2302305", "skill_id": "23023", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2302305", "skillIconId": "skill_battle_23028", "LSkillDes": "BtlSkill_SkillDes_2302305", "LCompareInfor": "BtlSkill_CompareInfor_2302305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2302305", "skillEffectIdStrNew": "2302305"}, {"id": "2303001", "skill_id": "23030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2303001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2303001", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303001", "skillEffectIdStr": "2303001", "skillEffectIdStrNew": "2303001"}, {"id": "2303101", "skill_id": "23031", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2303101", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303101", "LCompareInfor": "BtlSkill_CompareInfor_2303101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303101;2303290", "skillEffectIdStrNew": "2303101;2303290"}, {"id": "2303102", "skill_id": "23031", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2303102", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303102", "LCompareInfor": "BtlSkill_CompareInfor_2303102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303102;2303290", "skillEffectIdStrNew": "2303102;2303290"}, {"id": "2303103", "skill_id": "23031", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2303103", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303103", "LCompareInfor": "BtlSkill_CompareInfor_2303103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303103;2303290", "skillEffectIdStrNew": "2303103;2303290"}, {"id": "2303104", "skill_id": "23031", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2303104", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303104", "LCompareInfor": "BtlSkill_CompareInfor_2303104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303104;2303290", "skillEffectIdStrNew": "2303104;2303290"}, {"id": "2303105", "skill_id": "23031", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2303105", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303105", "LCompareInfor": "BtlSkill_CompareInfor_2303105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303105;2303290", "skillEffectIdStrNew": "2303105;2303290"}, {"id": "2303106", "skill_id": "23031", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2303106", "skillIconId": "skill_battle_23036", "LSkillDes": "BtlSkill_SkillDes_2303106", "LCompareInfor": "BtlSkill_CompareInfor_2303106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2303101", "skillEffectIdStr": "2303106;2303290", "skillEffectIdStrNew": "2303106;2303290"}, {"id": "2303201", "skill_id": "23032", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2303201", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303201", "LCompareInfor": "BtlSkill_CompareInfor_2303201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303201", "skillEffectIdStrNew": "2303201"}, {"id": "2303202", "skill_id": "23032", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2303202", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303202", "LCompareInfor": "BtlSkill_CompareInfor_2303202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303202", "skillEffectIdStrNew": "2303202"}, {"id": "2303203", "skill_id": "23032", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2303203", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303203", "LCompareInfor": "BtlSkill_CompareInfor_2303203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303203", "skillEffectIdStrNew": "2303203"}, {"id": "2303204", "skill_id": "23032", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2303204", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303204", "LCompareInfor": "BtlSkill_CompareInfor_2303204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303204", "skillEffectIdStrNew": "2303204"}, {"id": "2303205", "skill_id": "23032", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2303205", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303205", "LCompareInfor": "BtlSkill_CompareInfor_2303205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303205", "skillEffectIdStrNew": "2303205"}, {"id": "2303206", "skill_id": "23032", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2303206", "skillIconId": "skill_battle_23037", "LSkillDes": "BtlSkill_SkillDes_2303206", "LCompareInfor": "BtlSkill_CompareInfor_2303206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303206", "skillEffectIdStrNew": "2303206"}, {"id": "2303290", "skill_id": "23032", "skill_level": "90", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2303290", "skillEffectIdStr": "23032901;23032902;23032903;23032904;23032905;23032906", "skillEffectIdStrNew": "23032901;23032902;23032903;23032904;23032905;23032906"}, {"id": "2303301", "skill_id": "23033", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2303301", "skillIconId": "skill_battle_23038", "LSkillDes": "BtlSkill_SkillDes_2303301", "LCompareInfor": "BtlSkill_CompareInfor_2303301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303301", "skillEffectIdStrNew": "2303301"}, {"id": "2303302", "skill_id": "23033", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2303302", "skillIconId": "skill_battle_23038", "LSkillDes": "BtlSkill_SkillDes_2303302", "LCompareInfor": "BtlSkill_CompareInfor_2303302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303302", "skillEffectIdStrNew": "2303302"}, {"id": "2303303", "skill_id": "23033", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2303303", "skillIconId": "skill_battle_23038", "LSkillDes": "BtlSkill_SkillDes_2303303", "LCompareInfor": "BtlSkill_CompareInfor_2303303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303303", "skillEffectIdStrNew": "2303303"}, {"id": "2303304", "skill_id": "23033", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2303304", "skillIconId": "skill_battle_23038", "LSkillDes": "BtlSkill_SkillDes_2303304", "LCompareInfor": "BtlSkill_CompareInfor_2303304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303304", "skillEffectIdStrNew": "2303304"}, {"id": "2303305", "skill_id": "23033", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2303305", "skillIconId": "skill_battle_23038", "LSkillDes": "BtlSkill_SkillDes_2303305", "LCompareInfor": "BtlSkill_CompareInfor_2303305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2303305", "skillEffectIdStrNew": "2303305"}, {"id": "2304001", "skill_id": "23040", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2304001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304001", "skillEffectIdStr": "2304002;2304003", "skillEffectIdStrNew": "2304002;2304003"}, {"id": "2304002", "skill_id": "23040", "skill_level": "1", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "8.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304002", "skillEffectIdStr": "2304001", "skillEffectIdStrNew": "2304001"}, {"id": "2304003", "skill_id": "23040", "skill_level": "1", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "8.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304003", "skillEffectIdStr": "2304001", "skillEffectIdStrNew": "2304001"}, {"id": "2304101", "skill_id": "23041", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2304101", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304101", "LCompareInfor": "BtlSkill_CompareInfor_2304101", "power": "600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304101", "skillEffectIdStrNew": "2304101"}, {"id": "2304102", "skill_id": "23041", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2304102", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304102", "LCompareInfor": "BtlSkill_CompareInfor_2304102", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304102", "skillEffectIdStrNew": "2304102"}, {"id": "2304103", "skill_id": "23041", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2304103", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304103", "LCompareInfor": "BtlSkill_CompareInfor_2304103", "power": "1000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304103", "skillEffectIdStrNew": "2304103"}, {"id": "2304104", "skill_id": "23041", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2304104", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304104", "LCompareInfor": "BtlSkill_CompareInfor_2304104", "power": "1200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304104", "skillEffectIdStrNew": "2304104"}, {"id": "2304105", "skill_id": "23041", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2304105", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304105", "LCompareInfor": "BtlSkill_CompareInfor_2304105", "power": "1400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304105", "skillEffectIdStrNew": "2304105"}, {"id": "2304106", "skill_id": "23041", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2304106", "skillIconId": "skill_battle_23046", "LSkillDes": "BtlSkill_SkillDes_2304106", "LCompareInfor": "BtlSkill_CompareInfor_2304106", "power": "1600", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4.8", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2304101", "skillEffectIdStr": "2304106", "skillEffectIdStrNew": "2304106"}, {"id": "2304191", "skill_id": "23041", "skill_level": "91", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304191;23041911", "skillEffectIdStrNew": "2304191;23041911"}, {"id": "2304192", "skill_id": "23041", "skill_level": "92", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304192;23041921", "skillEffectIdStrNew": "2304192;23041921"}, {"id": "2304193", "skill_id": "23041", "skill_level": "93", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304193;23041931", "skillEffectIdStrNew": "2304193;23041931"}, {"id": "2304194", "skill_id": "23041", "skill_level": "94", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304194;23041941", "skillEffectIdStrNew": "2304194;23041941"}, {"id": "2304195", "skill_id": "23041", "skill_level": "95", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304195;23041951", "skillEffectIdStrNew": "2304195;23041951"}, {"id": "2304196", "skill_id": "23041", "skill_level": "96", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "1", "clientEffectId": "2304191", "skillEffectIdStr": "2304196;23041961", "skillEffectIdStrNew": "2304196;23041961"}, {"id": "2304201", "skill_id": "23042", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2304201", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304201", "LCompareInfor": "BtlSkill_CompareInfor_2304201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304201", "skillEffectIdStrNew": "2304201"}, {"id": "2304202", "skill_id": "23042", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2304202", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304202", "LCompareInfor": "BtlSkill_CompareInfor_2304202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304202", "skillEffectIdStrNew": "2304202"}, {"id": "2304203", "skill_id": "23042", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2304203", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304203", "LCompareInfor": "BtlSkill_CompareInfor_2304203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304203", "skillEffectIdStrNew": "2304203"}, {"id": "2304204", "skill_id": "23042", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2304204", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304204", "LCompareInfor": "BtlSkill_CompareInfor_2304204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304204", "skillEffectIdStrNew": "2304204"}, {"id": "2304205", "skill_id": "23042", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2304205", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304205", "LCompareInfor": "BtlSkill_CompareInfor_2304205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304205", "skillEffectIdStrNew": "2304205"}, {"id": "2304206", "skill_id": "23042", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2304206", "skillIconId": "skill_battle_23047", "LSkillDes": "BtlSkill_SkillDes_2304206", "LCompareInfor": "BtlSkill_CompareInfor_2304206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304206", "skillEffectIdStrNew": "2304206"}, {"id": "2304301", "skill_id": "23043", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2304301", "skillIconId": "skill_battle_23048", "LSkillDes": "BtlSkill_SkillDes_2304301", "LCompareInfor": "BtlSkill_CompareInfor_2304301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304301", "skillEffectIdStrNew": "2304301"}, {"id": "2304302", "skill_id": "23043", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2304302", "skillIconId": "skill_battle_23048", "LSkillDes": "BtlSkill_SkillDes_2304302", "LCompareInfor": "BtlSkill_CompareInfor_2304302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304302", "skillEffectIdStrNew": "2304302"}, {"id": "2304303", "skill_id": "23043", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2304303", "skillIconId": "skill_battle_23048", "LSkillDes": "BtlSkill_SkillDes_2304303", "LCompareInfor": "BtlSkill_CompareInfor_2304303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304303", "skillEffectIdStrNew": "2304303"}, {"id": "2304304", "skill_id": "23043", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2304304", "skillIconId": "skill_battle_23048", "LSkillDes": "BtlSkill_SkillDes_2304304", "LCompareInfor": "BtlSkill_CompareInfor_2304304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304304", "skillEffectIdStrNew": "2304304"}, {"id": "2304305", "skill_id": "23043", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2304305", "skillIconId": "skill_battle_23048", "LSkillDes": "BtlSkill_SkillDes_2304305", "LCompareInfor": "BtlSkill_CompareInfor_2304305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2304305", "skillEffectIdStrNew": "2304305"}, {"id": "2305001", "skill_id": "23050", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2305001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2305001", "skillEffectIdStr": "2305001", "skillEffectIdStrNew": "2305001"}, {"id": "2305101", "skill_id": "23051", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2305101", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305101", "LCompareInfor": "BtlSkill_CompareInfor_2305101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051011;23051012;23051013;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051011;23051012;23051013;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305102", "skill_id": "23051", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2305102", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305102", "LCompareInfor": "BtlSkill_CompareInfor_2305102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051021;23051012;23051023;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051021;23051012;23051023;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305103", "skill_id": "23051", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2305103", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305103", "LCompareInfor": "BtlSkill_CompareInfor_2305103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051031;23051012;23051033;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051031;23051012;23051033;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305104", "skill_id": "23051", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2305104", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305104", "LCompareInfor": "BtlSkill_CompareInfor_2305104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051041;23051012;23051043;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051041;23051012;23051043;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305105", "skill_id": "23051", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2305105", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305105", "LCompareInfor": "BtlSkill_CompareInfor_2305105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051051;23051012;23051053;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051051;23051012;23051053;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305106", "skill_id": "23051", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2305106", "skillIconId": "skill_battle_23056", "LSkillDes": "BtlSkill_SkillDes_2305106", "LCompareInfor": "BtlSkill_CompareInfor_2305106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "2305101", "skillEffectIdStr": "23051061;23051012;23051063;23053011;23053021;23053031;23053041;23053051", "skillEffectIdStrNew": "23051061;23051012;23051063;23053011;23053021;23053031;23053041;23053051"}, {"id": "2305201", "skill_id": "23052", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2305201", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305201", "LCompareInfor": "BtlSkill_CompareInfor_2305201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305201", "skillEffectIdStrNew": "2305201"}, {"id": "2305202", "skill_id": "23052", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2305202", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305202", "LCompareInfor": "BtlSkill_CompareInfor_2305202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305202", "skillEffectIdStrNew": "2305202"}, {"id": "2305203", "skill_id": "23052", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2305203", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305203", "LCompareInfor": "BtlSkill_CompareInfor_2305203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305203", "skillEffectIdStrNew": "2305203"}, {"id": "2305204", "skill_id": "23052", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2305204", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305204", "LCompareInfor": "BtlSkill_CompareInfor_2305204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305204", "skillEffectIdStrNew": "2305204"}, {"id": "2305205", "skill_id": "23052", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2305205", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305205", "LCompareInfor": "BtlSkill_CompareInfor_2305205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305205", "skillEffectIdStrNew": "2305205"}, {"id": "2305206", "skill_id": "23052", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2305206", "skillIconId": "skill_battle_23057", "LSkillDes": "BtlSkill_SkillDes_2305206", "LCompareInfor": "BtlSkill_CompareInfor_2305206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305206", "skillEffectIdStrNew": "2305206"}, {"id": "2305301", "skill_id": "23053", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2305301", "skillIconId": "skill_battle_23058", "LSkillDes": "BtlSkill_SkillDes_2305301", "LCompareInfor": "BtlSkill_CompareInfor_2305301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305301", "skillEffectIdStrNew": "2305301"}, {"id": "2305302", "skill_id": "23053", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2305302", "skillIconId": "skill_battle_23058", "LSkillDes": "BtlSkill_SkillDes_2305302", "LCompareInfor": "BtlSkill_CompareInfor_2305302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305302", "skillEffectIdStrNew": "2305302"}, {"id": "2305303", "skill_id": "23053", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2305303", "skillIconId": "skill_battle_23058", "LSkillDes": "BtlSkill_SkillDes_2305303", "LCompareInfor": "BtlSkill_CompareInfor_2305303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305303", "skillEffectIdStrNew": "2305303"}, {"id": "2305304", "skill_id": "23053", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2305304", "skillIconId": "skill_battle_23058", "LSkillDes": "BtlSkill_SkillDes_2305304", "LCompareInfor": "BtlSkill_CompareInfor_2305304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305304", "skillEffectIdStrNew": "2305304"}, {"id": "2305305", "skill_id": "23053", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2305305", "skillIconId": "skill_battle_23058", "LSkillDes": "BtlSkill_SkillDes_2305305", "LCompareInfor": "BtlSkill_CompareInfor_2305305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2305305", "skillEffectIdStrNew": "2305305"}, {"id": "2306001", "skill_id": "23060", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2306001", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306101", "skill_id": "23061", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2306101", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306101", "LCompareInfor": "BtlSkill_CompareInfor_2306101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306102", "skill_id": "23061", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2306102", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306102", "LCompareInfor": "BtlSkill_CompareInfor_2306102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306103", "skill_id": "23061", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2306103", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306103", "LCompareInfor": "BtlSkill_CompareInfor_2306103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306104", "skill_id": "23061", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2306104", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306104", "LCompareInfor": "BtlSkill_CompareInfor_2306104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306105", "skill_id": "23061", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2306105", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306105", "LCompareInfor": "BtlSkill_CompareInfor_2306105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306106", "skill_id": "23061", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2306106", "skillIconId": "skill_battle_23066", "LSkillDes": "BtlSkill_SkillDes_2306106", "LCompareInfor": "BtlSkill_CompareInfor_2306106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;180", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306201", "skill_id": "23062", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2306201", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306201", "LCompareInfor": "BtlSkill_CompareInfor_2306201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306202", "skill_id": "23062", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2306202", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306202", "LCompareInfor": "BtlSkill_CompareInfor_2306202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306203", "skill_id": "23062", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2306203", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306203", "LCompareInfor": "BtlSkill_CompareInfor_2306203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306204", "skill_id": "23062", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2306204", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306204", "LCompareInfor": "BtlSkill_CompareInfor_2306204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306205", "skill_id": "23062", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2306205", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306205", "LCompareInfor": "BtlSkill_CompareInfor_2306205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306206", "skill_id": "23062", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2306206", "skillIconId": "skill_battle_23067", "LSkillDes": "BtlSkill_SkillDes_2306206", "LCompareInfor": "BtlSkill_CompareInfor_2306206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306301", "skill_id": "23063", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2306301", "skillIconId": "skill_battle_23068", "LSkillDes": "BtlSkill_SkillDes_2306301", "LCompareInfor": "BtlSkill_CompareInfor_2306301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306302", "skill_id": "23063", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2306302", "skillIconId": "skill_battle_23068", "LSkillDes": "BtlSkill_SkillDes_2306302", "LCompareInfor": "BtlSkill_CompareInfor_2306302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306303", "skill_id": "23063", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2306303", "skillIconId": "skill_battle_23068", "LSkillDes": "BtlSkill_SkillDes_2306303", "LCompareInfor": "BtlSkill_CompareInfor_2306303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306304", "skill_id": "23063", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2306304", "skillIconId": "skill_battle_23068", "LSkillDes": "BtlSkill_SkillDes_2306304", "LCompareInfor": "BtlSkill_CompareInfor_2306304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2306305", "skill_id": "23063", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2306305", "skillIconId": "skill_battle_23068", "LSkillDes": "BtlSkill_SkillDes_2306305", "LCompareInfor": "BtlSkill_CompareInfor_2306305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "999", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "2401001", "skill_id": "24010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2401001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2401001", "LCompareInfor": "BtlSkill_CompareInfor_2401001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "2401001", "skillEffectIdStr": "2401001", "skillEffectIdStrNew": "2401001"}, {"id": "2401101", "skill_id": "24011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2401101", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401101", "LCompareInfor": "BtlSkill_CompareInfor_2401101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011011;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011011;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401102", "skill_id": "24011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2401102", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401102", "LCompareInfor": "BtlSkill_CompareInfor_2401102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011021;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011021;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401103", "skill_id": "24011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2401103", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401103", "LCompareInfor": "BtlSkill_CompareInfor_2401103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011031;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011031;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401104", "skill_id": "24011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2401104", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401104", "LCompareInfor": "BtlSkill_CompareInfor_2401104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011041;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011041;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401105", "skill_id": "24011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2401105", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401105", "LCompareInfor": "BtlSkill_CompareInfor_2401105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011051;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011051;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401106", "skill_id": "24011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2401106", "skillIconId": "skill_battle_42", "LSkillDes": "BtlSkill_SkillDes_2401106", "LCompareInfor": "BtlSkill_CompareInfor_2401106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3", "CD": "8.1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "2401101", "skillEffectIdStr": "24011061;24011012;24011013;24011023;24011033;24011043;24011053", "skillEffectIdStrNew": "24011061;24011012;24011013;24011023;24011033;24011043;24011053"}, {"id": "2401201", "skill_id": "24012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2401201", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401201", "LCompareInfor": "BtlSkill_CompareInfor_2401201", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401201", "skillEffectIdStrNew": "2401201"}, {"id": "2401202", "skill_id": "24012", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2401202", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401202", "LCompareInfor": "BtlSkill_CompareInfor_2401202", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401202", "skillEffectIdStrNew": "2401202"}, {"id": "2401203", "skill_id": "24012", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2401203", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401203", "LCompareInfor": "BtlSkill_CompareInfor_2401203", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401203", "skillEffectIdStrNew": "2401203"}, {"id": "2401204", "skill_id": "24012", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2401204", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401204", "LCompareInfor": "BtlSkill_CompareInfor_2401204", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401204", "skillEffectIdStrNew": "2401204"}, {"id": "2401205", "skill_id": "24012", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2401205", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401205", "LCompareInfor": "BtlSkill_CompareInfor_2401205", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401205", "skillEffectIdStrNew": "2401205"}, {"id": "2401206", "skill_id": "24012", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2401206", "skillIconId": "skill_battle_120", "LSkillDes": "BtlSkill_SkillDes_2401206", "LCompareInfor": "BtlSkill_CompareInfor_2401206", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401206", "skillEffectIdStrNew": "2401206"}, {"id": "2401901", "skill_id": "24019", "skill_level": "1", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "800", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401901", "skillEffectIdStrNew": "2401901"}, {"id": "2401902", "skill_id": "24019", "skill_level": "2", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401902", "skillEffectIdStrNew": "2401902"}, {"id": "2401903", "skill_id": "24019", "skill_level": "3", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401903", "skillEffectIdStrNew": "2401903"}, {"id": "2401904", "skill_id": "24019", "skill_level": "4", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401904", "skillEffectIdStrNew": "2401904"}, {"id": "2401905", "skill_id": "24019", "skill_level": "5", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401905", "skillEffectIdStrNew": "2401905"}, {"id": "2401906", "skill_id": "24019", "skill_level": "6", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401906", "skillEffectIdStrNew": "2401906"}, {"id": "2401301", "skill_id": "24013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2401301", "skillIconId": "skill_battle_102", "LSkillDes": "BtlSkill_SkillDes_2401301", "LCompareInfor": "BtlSkill_CompareInfor_2401301", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401301", "skillEffectIdStrNew": "2401301"}, {"id": "2401302", "skill_id": "24013", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2401302", "skillIconId": "skill_battle_102", "LSkillDes": "BtlSkill_SkillDes_2401302", "LCompareInfor": "BtlSkill_CompareInfor_2401302", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401302", "skillEffectIdStrNew": "2401302"}, {"id": "2401303", "skill_id": "24013", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2401303", "skillIconId": "skill_battle_102", "LSkillDes": "BtlSkill_SkillDes_2401303", "LCompareInfor": "BtlSkill_CompareInfor_2401303", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401303", "skillEffectIdStrNew": "2401303"}, {"id": "2401304", "skill_id": "24013", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2401304", "skillIconId": "skill_battle_102", "LSkillDes": "BtlSkill_SkillDes_2401304", "LCompareInfor": "BtlSkill_CompareInfor_2401304", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401304", "skillEffectIdStrNew": "2401304"}, {"id": "2401305", "skill_id": "24013", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2401305", "skillIconId": "skill_battle_102", "LSkillDes": "BtlSkill_SkillDes_2401305", "LCompareInfor": "BtlSkill_CompareInfor_2401305", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2401305", "skillEffectIdStrNew": "2401305"}, {"id": "2402001", "skill_id": "24020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2402001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2402001", "LCompareInfor": "BtlSkill_CompareInfor_2402001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402001", "skillEffectIdStr": "2402001", "skillEffectIdStrNew": "2402001"}, {"id": "2402101", "skill_id": "24021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2402101", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402101", "LCompareInfor": "BtlSkill_CompareInfor_2402101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021012", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021012"}, {"id": "2402102", "skill_id": "24021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2402102", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402102", "LCompareInfor": "BtlSkill_CompareInfor_2402102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021022", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021022"}, {"id": "2402103", "skill_id": "24021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2402103", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402103", "LCompareInfor": "BtlSkill_CompareInfor_2402103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021032", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021032"}, {"id": "2402104", "skill_id": "24021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2402104", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402104", "LCompareInfor": "BtlSkill_CompareInfor_2402104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021042", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021042"}, {"id": "2402105", "skill_id": "24021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2402105", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402105", "LCompareInfor": "BtlSkill_CompareInfor_2402105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021052", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021052"}, {"id": "2402106", "skill_id": "24021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2402106", "skillIconId": "skill_battle_48", "LSkillDes": "BtlSkill_SkillDes_2402106", "LCompareInfor": "BtlSkill_CompareInfor_2402106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2402101", "skillEffectIdStr": "24021011;24021013;24021023;24021033;24021043;24021053;24021062", "skillEffectIdStrNew": "24021011;24021013;24021023;24021033;24021043;24021053;24021062"}, {"id": "2402901", "skill_id": "24029", "skill_level": "1", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "800", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029011", "skillEffectIdStrNew": "24029011"}, {"id": "2402902", "skill_id": "24029", "skill_level": "2", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029021", "skillEffectIdStrNew": "24029021"}, {"id": "2402903", "skill_id": "24029", "skill_level": "3", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029031", "skillEffectIdStrNew": "24029031"}, {"id": "2402904", "skill_id": "24029", "skill_level": "4", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029041", "skillEffectIdStrNew": "24029041"}, {"id": "2402905", "skill_id": "24029", "skill_level": "5", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029051", "skillEffectIdStrNew": "24029051"}, {"id": "2402906", "skill_id": "24029", "skill_level": "6", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1.65;360", "damageType": "2", "clientEffectId": "2402901", "skillEffectIdStr": "24029061", "skillEffectIdStrNew": "24029061"}, {"id": "2402201", "skill_id": "24022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2402201", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402201", "LCompareInfor": "BtlSkill_CompareInfor_2402201", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402201", "skillEffectIdStrNew": "2402201"}, {"id": "2402202", "skill_id": "24022", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2402202", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402202", "LCompareInfor": "BtlSkill_CompareInfor_2402202", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402202", "skillEffectIdStrNew": "2402202"}, {"id": "2402203", "skill_id": "24022", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2402203", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402203", "LCompareInfor": "BtlSkill_CompareInfor_2402203", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402203", "skillEffectIdStrNew": "2402203"}, {"id": "2402204", "skill_id": "24022", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2402204", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402204", "LCompareInfor": "BtlSkill_CompareInfor_2402204", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402204", "skillEffectIdStrNew": "2402204"}, {"id": "2402205", "skill_id": "24022", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2402205", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402205", "LCompareInfor": "BtlSkill_CompareInfor_2402205", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402205", "skillEffectIdStrNew": "2402205"}, {"id": "2402206", "skill_id": "24022", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2402206", "skillIconId": "skill_battle_105", "LSkillDes": "BtlSkill_SkillDes_2402206", "LCompareInfor": "BtlSkill_CompareInfor_2402206", "power": "8000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402206", "skillEffectIdStrNew": "2402206"}, {"id": "2402301", "skill_id": "24023", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2402301", "skillIconId": "skill_battle_50", "LSkillDes": "BtlSkill_SkillDes_2402301", "LCompareInfor": "BtlSkill_CompareInfor_2402301", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402301", "skillEffectIdStrNew": "2402301"}, {"id": "2402302", "skill_id": "24023", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2402302", "skillIconId": "skill_battle_50", "LSkillDes": "BtlSkill_SkillDes_2402302", "LCompareInfor": "BtlSkill_CompareInfor_2402302", "power": "2200", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402302", "skillEffectIdStrNew": "2402302"}, {"id": "2402303", "skill_id": "24023", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2402303", "skillIconId": "skill_battle_50", "LSkillDes": "BtlSkill_SkillDes_2402303", "LCompareInfor": "BtlSkill_CompareInfor_2402303", "power": "3000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402303", "skillEffectIdStrNew": "2402303"}, {"id": "2402304", "skill_id": "24023", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2402304", "skillIconId": "skill_battle_50", "LSkillDes": "BtlSkill_SkillDes_2402304", "LCompareInfor": "BtlSkill_CompareInfor_2402304", "power": "4400", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402304", "skillEffectIdStrNew": "2402304"}, {"id": "2402305", "skill_id": "24023", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2402305", "skillIconId": "skill_battle_50", "LSkillDes": "BtlSkill_SkillDes_2402305", "LCompareInfor": "BtlSkill_CompareInfor_2402305", "power": "6000", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "3", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "1", "skillEffectIdStr": "2402305", "skillEffectIdStrNew": "2402305"}, {"id": "2403001", "skill_id": "24030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2403001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_2403001", "LCompareInfor": "", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "9", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403001", "skillEffectIdStr": "2403001", "skillEffectIdStrNew": "2403001"}, {"id": "2403101", "skill_id": "24031", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2403101", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403101", "LCompareInfor": "BtlSkill_CompareInfor_2403101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031019", "skillEffectIdStrNew": "24031019"}, {"id": "2403102", "skill_id": "24031", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2403102", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403102", "LCompareInfor": "BtlSkill_CompareInfor_2403102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031029", "skillEffectIdStrNew": "24031029"}, {"id": "2403103", "skill_id": "24031", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2403103", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403103", "LCompareInfor": "BtlSkill_CompareInfor_2403103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031039", "skillEffectIdStrNew": "24031039"}, {"id": "2403104", "skill_id": "24031", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2403104", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403104", "LCompareInfor": "BtlSkill_CompareInfor_2403104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031049", "skillEffectIdStrNew": "24031049"}, {"id": "2403105", "skill_id": "24031", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2403105", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403105", "LCompareInfor": "BtlSkill_CompareInfor_2403105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031059", "skillEffectIdStrNew": "24031059"}, {"id": "2403106", "skill_id": "24031", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2403106", "skillIconId": "skill_battle_24036", "LSkillDes": "BtlSkill_SkillDes_2403106", "LCompareInfor": "BtlSkill_CompareInfor_2403106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "4", "CD": "8.3", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "2403101", "skillEffectIdStr": "24031069", "skillEffectIdStrNew": "24031069"}, {"id": "24031019", "skill_id": "240319", "skill_level": "1", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403101;24031011;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403101;24031011;24033011;24033021;24033031;24033041;24033051"}, {"id": "24031029", "skill_id": "240319", "skill_level": "2", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403102;24031021;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403102;24031021;24033011;24033021;24033031;24033041;24033051"}, {"id": "24031039", "skill_id": "240319", "skill_level": "3", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403103;24031031;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403103;24031031;24033011;24033021;24033031;24033041;24033051"}, {"id": "24031049", "skill_id": "240319", "skill_level": "4", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403104;24031041;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403104;24031041;24033011;24033021;24033031;24033041;24033051"}, {"id": "24031059", "skill_id": "240319", "skill_level": "5", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;2", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403105;24031051;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403105;24031051;24033011;24033021;24033031;24033041;24033051"}, {"id": "24031069", "skill_id": "240319", "skill_level": "6", "LSkillName": "", "skillIconId": "", "LSkillDes": "", "LCompareInfor": "", "power": "", "LTypeDes": "", "priority": "10", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "2", "selectParams": "15;2", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "24031019", "skillEffectIdStr": "2403106;24031061;24033011;24033021;24033031;24033041;24033051", "skillEffectIdStrNew": "2403106;24031061;24033011;24033021;24033031;24033041;24033051"}, {"id": "2403201", "skill_id": "24032", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2403201", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403201", "LCompareInfor": "BtlSkill_CompareInfor_2403201", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403201", "skillEffectIdStrNew": "2403201"}, {"id": "2403202", "skill_id": "24032", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2403202", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403202", "LCompareInfor": "BtlSkill_CompareInfor_2403202", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403202", "skillEffectIdStrNew": "2403202"}, {"id": "2403203", "skill_id": "24032", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2403203", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403203", "LCompareInfor": "BtlSkill_CompareInfor_2403203", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403203", "skillEffectIdStrNew": "2403203"}, {"id": "2403204", "skill_id": "24032", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2403204", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403204", "LCompareInfor": "BtlSkill_CompareInfor_2403204", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403204", "skillEffectIdStrNew": "2403204"}, {"id": "2403205", "skill_id": "24032", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2403205", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403205", "LCompareInfor": "BtlSkill_CompareInfor_2403205", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403205", "skillEffectIdStrNew": "2403205"}, {"id": "2403206", "skill_id": "24032", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_2403206", "skillIconId": "skill_battle_24037", "LSkillDes": "BtlSkill_SkillDes_2403206", "LCompareInfor": "BtlSkill_CompareInfor_2403206", "power": "8000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403206", "skillEffectIdStrNew": "2403206"}, {"id": "2403301", "skill_id": "24033", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_2403301", "skillIconId": "skill_battle_24038", "LSkillDes": "BtlSkill_SkillDes_2403301", "LCompareInfor": "BtlSkill_CompareInfor_2403301", "power": "800", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403301", "skillEffectIdStrNew": "2403301"}, {"id": "2403302", "skill_id": "24033", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_2403302", "skillIconId": "skill_battle_24038", "LSkillDes": "BtlSkill_SkillDes_2403302", "LCompareInfor": "BtlSkill_CompareInfor_2403302", "power": "2200", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403302", "skillEffectIdStrNew": "2403302"}, {"id": "2403303", "skill_id": "24033", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_2403303", "skillIconId": "skill_battle_24038", "LSkillDes": "BtlSkill_SkillDes_2403303", "LCompareInfor": "BtlSkill_CompareInfor_2403303", "power": "3000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403303", "skillEffectIdStrNew": "2403303"}, {"id": "2403304", "skill_id": "24033", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_2403304", "skillIconId": "skill_battle_24038", "LSkillDes": "BtlSkill_SkillDes_2403304", "LCompareInfor": "BtlSkill_CompareInfor_2403304", "power": "4400", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403304", "skillEffectIdStrNew": "2403304"}, {"id": "2403305", "skill_id": "24033", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_2403305", "skillIconId": "skill_battle_24038", "LSkillDes": "BtlSkill_SkillDes_2403305", "LCompareInfor": "BtlSkill_CompareInfor_2403305", "power": "6000", "LTypeDes": "", "priority": "3", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "2403305", "skillEffectIdStrNew": "2403305"}, {"id": "3101001", "skill_id": "31010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3101001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3101001", "LCompareInfor": "BtlSkill_CompareInfor_3101001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101001", "skillEffectIdStr": "3101001", "skillEffectIdStrNew": "3101001"}, {"id": "3101101", "skill_id": "31011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3101101", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101101", "LCompareInfor": "BtlSkill_CompareInfor_3101101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011011;31011012", "skillEffectIdStrNew": "31011011;31011012"}, {"id": "3101102", "skill_id": "31011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3101102", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101102", "LCompareInfor": "BtlSkill_CompareInfor_3101102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011021;31011012", "skillEffectIdStrNew": "31011021;31011012"}, {"id": "3101103", "skill_id": "31011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3101103", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101103", "LCompareInfor": "BtlSkill_CompareInfor_3101103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011031;31011012", "skillEffectIdStrNew": "31011031;31011012"}, {"id": "3101104", "skill_id": "31011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3101104", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101104", "LCompareInfor": "BtlSkill_CompareInfor_3101104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011041;31011012", "skillEffectIdStrNew": "31011041;31011012"}, {"id": "3101105", "skill_id": "31011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3101105", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101105", "LCompareInfor": "BtlSkill_CompareInfor_3101105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011051;31011012", "skillEffectIdStrNew": "31011051;31011012"}, {"id": "3101106", "skill_id": "31011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3101106", "skillIconId": "skill_battle_4", "LSkillDes": "BtlSkill_SkillDes_3101106", "LCompareInfor": "BtlSkill_CompareInfor_3101106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "3.35", "CD": "6.7", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3101101", "skillEffectIdStr": "31011061;31011012", "skillEffectIdStrNew": "31011061;31011012"}, {"id": "3102001", "skill_id": "31020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3102001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3102001", "LCompareInfor": "BtlSkill_CompareInfor_3102001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102001", "skillEffectIdStr": "3102001", "skillEffectIdStrNew": "3102001"}, {"id": "3102101", "skill_id": "31021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3102101", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102101", "LCompareInfor": "BtlSkill_CompareInfor_3102101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021012;31021011", "skillEffectIdStrNew": "31021012;31021011"}, {"id": "3102102", "skill_id": "31021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3102102", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102102", "LCompareInfor": "BtlSkill_CompareInfor_3102102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021012;31021021", "skillEffectIdStrNew": "31021012;31021021"}, {"id": "3102103", "skill_id": "31021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3102103", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102103", "LCompareInfor": "BtlSkill_CompareInfor_3102103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021032;31021031", "skillEffectIdStrNew": "31021032;31021031"}, {"id": "3102104", "skill_id": "31021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3102104", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102104", "LCompareInfor": "BtlSkill_CompareInfor_3102104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021032;31021041", "skillEffectIdStrNew": "31021032;31021041"}, {"id": "3102105", "skill_id": "31021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3102105", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102105", "LCompareInfor": "BtlSkill_CompareInfor_3102105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021052;31021051", "skillEffectIdStrNew": "31021052;31021051"}, {"id": "3102106", "skill_id": "31021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3102106", "skillIconId": "skill_battle_57", "LSkillDes": "BtlSkill_SkillDes_3102106", "LCompareInfor": "BtlSkill_CompareInfor_3102106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "4.7", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3102101", "skillEffectIdStr": "31021052;31021061", "skillEffectIdStrNew": "31021052;31021061"}, {"id": "3201001", "skill_id": "32010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3201001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3201001", "LCompareInfor": "BtlSkill_CompareInfor_3201001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201001", "skillEffectIdStr": "3201001", "skillEffectIdStrNew": "3201001"}, {"id": "3201101", "skill_id": "32011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3201101", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201101", "LCompareInfor": "BtlSkill_CompareInfor_3201101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011011", "skillEffectIdStrNew": "32011010;32011011"}, {"id": "3201102", "skill_id": "32011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3201102", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201102", "LCompareInfor": "BtlSkill_CompareInfor_3201102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011021", "skillEffectIdStrNew": "32011010;32011021"}, {"id": "3201103", "skill_id": "32011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3201103", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201103", "LCompareInfor": "BtlSkill_CompareInfor_3201103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011031", "skillEffectIdStrNew": "32011010;32011031"}, {"id": "3201104", "skill_id": "32011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3201104", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201104", "LCompareInfor": "BtlSkill_CompareInfor_3201104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011041", "skillEffectIdStrNew": "32011010;32011041"}, {"id": "3201105", "skill_id": "32011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3201105", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201105", "LCompareInfor": "BtlSkill_CompareInfor_3201105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011051", "skillEffectIdStrNew": "32011010;32011051"}, {"id": "3201106", "skill_id": "32011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3201106", "skillIconId": "skill_battle_13", "LSkillDes": "BtlSkill_SkillDes_3201106", "LCompareInfor": "BtlSkill_CompareInfor_3201106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "3.6", "CD": "7.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "2", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3201101", "skillEffectIdStr": "32011010;32011061", "skillEffectIdStrNew": "32011010;32011061"}, {"id": "3201901", "skill_id": "32019", "skill_level": "1", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "800", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019011;32019012", "skillEffectIdStrNew": "32019011;32019012"}, {"id": "3201902", "skill_id": "32019", "skill_level": "2", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019021;32019022", "skillEffectIdStrNew": "32019021;32019022"}, {"id": "3201903", "skill_id": "32019", "skill_level": "3", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019031;32019032", "skillEffectIdStrNew": "32019031;32019032"}, {"id": "3201904", "skill_id": "32019", "skill_level": "4", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019041;32019042", "skillEffectIdStrNew": "32019041;32019042"}, {"id": "3201905", "skill_id": "32019", "skill_level": "5", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019051;32019052", "skillEffectIdStrNew": "32019051;32019052"}, {"id": "3201906", "skill_id": "32019", "skill_level": "6", "LSkillName": "", "skillIconId": "1;1", "LSkillDes": "", "LCompareInfor": "", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3201901", "skillEffectIdStr": "32019061;32019062", "skillEffectIdStrNew": "32019061;32019062"}, {"id": "3202001", "skill_id": "32020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3202001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3202001", "LCompareInfor": "BtlSkill_CompareInfor_3202001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3202001", "skillEffectIdStr": "3202001", "skillEffectIdStrNew": "3202001"}, {"id": "3202101", "skill_id": "32021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3202101", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202101", "LCompareInfor": "BtlSkill_CompareInfor_3202101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021012;32021011", "skillEffectIdStrNew": "32021012;32021011"}, {"id": "3202102", "skill_id": "32021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3202102", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202102", "LCompareInfor": "BtlSkill_CompareInfor_3202102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021012;32021021", "skillEffectIdStrNew": "32021012;32021021"}, {"id": "3202103", "skill_id": "32021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3202103", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202103", "LCompareInfor": "BtlSkill_CompareInfor_3202103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021032;32021031", "skillEffectIdStrNew": "32021032;32021031"}, {"id": "3202104", "skill_id": "32021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3202104", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202104", "LCompareInfor": "BtlSkill_CompareInfor_3202104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021032;32021041", "skillEffectIdStrNew": "32021032;32021041"}, {"id": "3202105", "skill_id": "32021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3202105", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202105", "LCompareInfor": "BtlSkill_CompareInfor_3202105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021052;32021051", "skillEffectIdStrNew": "32021052;32021051"}, {"id": "3202106", "skill_id": "32021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3202106", "skillIconId": "skill_battle_15", "LSkillDes": "BtlSkill_SkillDes_3202106", "LCompareInfor": "BtlSkill_CompareInfor_3202106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.8", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;20", "damageType": "1", "clientEffectId": "3202101", "skillEffectIdStr": "32021052;32021061", "skillEffectIdStrNew": "32021052;32021061"}, {"id": "3301001", "skill_id": "33010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3301001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3301001", "LCompareInfor": "BtlSkill_CompareInfor_3301001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3301001", "skillEffectIdStr": "3301001", "skillEffectIdStrNew": "3301001"}, {"id": "3301101", "skill_id": "33011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3301101", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301101", "LCompareInfor": "BtlSkill_CompareInfor_3301101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011011;33011012", "skillEffectIdStrNew": "33011011;33011012"}, {"id": "3301102", "skill_id": "33011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3301102", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301102", "LCompareInfor": "BtlSkill_CompareInfor_3301102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011021;33011012", "skillEffectIdStrNew": "33011021;33011012"}, {"id": "3301103", "skill_id": "33011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3301103", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301103", "LCompareInfor": "BtlSkill_CompareInfor_3301103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011031;33011032", "skillEffectIdStrNew": "33011031;33011032"}, {"id": "3301104", "skill_id": "33011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3301104", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301104", "LCompareInfor": "BtlSkill_CompareInfor_3301104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011041;33011032", "skillEffectIdStrNew": "33011041;33011032"}, {"id": "3301105", "skill_id": "33011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3301105", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301105", "LCompareInfor": "BtlSkill_CompareInfor_3301105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011051;33011052", "skillEffectIdStrNew": "33011051;33011052"}, {"id": "3301106", "skill_id": "33011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3301106", "skillIconId": "skill_battle_1", "LSkillDes": "BtlSkill_SkillDes_3301106", "LCompareInfor": "BtlSkill_CompareInfor_3301106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.3", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "3301101", "skillEffectIdStr": "33011061;33011052", "skillEffectIdStrNew": "33011061;33011052"}, {"id": "3302001", "skill_id": "33020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3302001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3302001", "LCompareInfor": "BtlSkill_CompareInfor_3302001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302001", "skillEffectIdStr": "3302001", "skillEffectIdStrNew": "3302001"}, {"id": "3302101", "skill_id": "33021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3302101", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302101", "LCompareInfor": "BtlSkill_CompareInfor_3302101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021011;33021012", "skillEffectIdStrNew": "33021011;33021012"}, {"id": "3302102", "skill_id": "33021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3302102", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302102", "LCompareInfor": "BtlSkill_CompareInfor_3302102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021021;33021012", "skillEffectIdStrNew": "33021021;33021012"}, {"id": "3302103", "skill_id": "33021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3302103", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302103", "LCompareInfor": "BtlSkill_CompareInfor_3302103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021031;33021012", "skillEffectIdStrNew": "33021031;33021012"}, {"id": "3302104", "skill_id": "33021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3302104", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302104", "LCompareInfor": "BtlSkill_CompareInfor_3302104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021041;33021012", "skillEffectIdStrNew": "33021041;33021012"}, {"id": "3302105", "skill_id": "33021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3302105", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302105", "LCompareInfor": "BtlSkill_CompareInfor_3302105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021051;33021012", "skillEffectIdStrNew": "33021051;33021012"}, {"id": "3302106", "skill_id": "33021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3302106", "skillIconId": "skill_battle_78", "LSkillDes": "BtlSkill_SkillDes_3302106", "LCompareInfor": "BtlSkill_CompareInfor_3302106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5.4", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "3302101", "skillEffectIdStr": "33021061;33021012", "skillEffectIdStrNew": "33021061;33021012"}, {"id": "3401001", "skill_id": "34010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3401001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3401001", "LCompareInfor": "BtlSkill_CompareInfor_3401001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3401001", "skillEffectIdStr": "3401001", "skillEffectIdStrNew": "3401001"}, {"id": "3401101", "skill_id": "34011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3401101", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401101", "LCompareInfor": "BtlSkill_CompareInfor_3401101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011011;34011012", "skillEffectIdStrNew": "34011011;34011012"}, {"id": "3401102", "skill_id": "34011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3401102", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401102", "LCompareInfor": "BtlSkill_CompareInfor_3401102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011021;34011022", "skillEffectIdStrNew": "34011021;34011022"}, {"id": "3401103", "skill_id": "34011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3401103", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401103", "LCompareInfor": "BtlSkill_CompareInfor_3401103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011031;34011032", "skillEffectIdStrNew": "34011031;34011032"}, {"id": "3401104", "skill_id": "34011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3401104", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401104", "LCompareInfor": "BtlSkill_CompareInfor_3401104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011041;34011042", "skillEffectIdStrNew": "34011041;34011042"}, {"id": "3401105", "skill_id": "34011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3401105", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401105", "LCompareInfor": "BtlSkill_CompareInfor_3401105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011051;34011052", "skillEffectIdStrNew": "34011051;34011052"}, {"id": "3401106", "skill_id": "34011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3401106", "skillIconId": "skill_battle_53", "LSkillDes": "BtlSkill_SkillDes_3401106", "LCompareInfor": "BtlSkill_CompareInfor_3401106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "3.7", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;4;30", "damageType": "1", "clientEffectId": "3401101", "skillEffectIdStr": "34011061;34011062", "skillEffectIdStrNew": "34011061;34011062"}, {"id": "3402001", "skill_id": "34020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3402001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_3402001", "LCompareInfor": "BtlSkill_CompareInfor_3402001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "3.8", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402001", "skillEffectIdStr": "3402001", "skillEffectIdStrNew": "3402001"}, {"id": "3402101", "skill_id": "34021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_3402101", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402101", "LCompareInfor": "BtlSkill_CompareInfor_3402101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021011;34021012", "skillEffectIdStrNew": "34021011;34021012"}, {"id": "3402102", "skill_id": "34021", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_3402102", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402102", "LCompareInfor": "BtlSkill_CompareInfor_3402102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021021;34021012", "skillEffectIdStrNew": "34021021;34021012"}, {"id": "3402103", "skill_id": "34021", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_3402103", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402103", "LCompareInfor": "BtlSkill_CompareInfor_3402103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021031;34021032", "skillEffectIdStrNew": "34021031;34021032"}, {"id": "3402104", "skill_id": "34021", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_3402104", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402104", "LCompareInfor": "BtlSkill_CompareInfor_3402104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021041;34021032", "skillEffectIdStrNew": "34021041;34021032"}, {"id": "3402105", "skill_id": "34021", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_3402105", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402105", "LCompareInfor": "BtlSkill_CompareInfor_3402105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021051;34021052", "skillEffectIdStrNew": "34021051;34021052"}, {"id": "3402106", "skill_id": "34021", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_3402106", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_3402106", "LCompareInfor": "BtlSkill_CompareInfor_3402106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "5", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "3402101", "skillEffectIdStr": "34021061;34021052", "skillEffectIdStrNew": "34021061;34021052"}, {"id": "4101001", "skill_id": "41010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4101001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4101001", "LCompareInfor": "BtlSkill_CompareInfor_4101001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4101001", "skillEffectIdStr": "4101001", "skillEffectIdStrNew": "4101001"}, {"id": "4101101", "skill_id": "41011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4101101", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101101", "LCompareInfor": "BtlSkill_CompareInfor_4101101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011011", "skillEffectIdStrNew": "41011011"}, {"id": "4101102", "skill_id": "41011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_4101102", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101102", "LCompareInfor": "BtlSkill_CompareInfor_4101102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011021", "skillEffectIdStrNew": "41011021"}, {"id": "4101103", "skill_id": "41011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_4101103", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101103", "LCompareInfor": "BtlSkill_CompareInfor_4101103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011031", "skillEffectIdStrNew": "41011031"}, {"id": "4101104", "skill_id": "41011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_4101104", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101104", "LCompareInfor": "BtlSkill_CompareInfor_4101104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011041", "skillEffectIdStrNew": "41011041"}, {"id": "4101105", "skill_id": "41011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_4101105", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101105", "LCompareInfor": "BtlSkill_CompareInfor_4101105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011051", "skillEffectIdStrNew": "41011051"}, {"id": "4101106", "skill_id": "41011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_4101106", "skillIconId": "skill_battle_90", "LSkillDes": "BtlSkill_SkillDes_4101106", "LCompareInfor": "BtlSkill_CompareInfor_4101106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "6", "CD": "8", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;15;360", "damageType": "2", "clientEffectId": "4101101", "skillEffectIdStr": "41011061", "skillEffectIdStrNew": "41011061"}, {"id": "4201001", "skill_id": "42010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4201001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4201001", "LCompareInfor": "BtlSkill_CompareInfor_4201001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "9", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4201001", "skillEffectIdStr": "4201001", "skillEffectIdStrNew": "4201001"}, {"id": "4201101", "skill_id": "42011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4201101", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201101", "LCompareInfor": "BtlSkill_CompareInfor_4201101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011011;42011012", "skillEffectIdStrNew": "42011011;42011012"}, {"id": "4201102", "skill_id": "42011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_4201102", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201102", "LCompareInfor": "BtlSkill_CompareInfor_4201102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011021;42011012", "skillEffectIdStrNew": "42011021;42011012"}, {"id": "4201103", "skill_id": "42011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_4201103", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201103", "LCompareInfor": "BtlSkill_CompareInfor_4201103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011031;42011032", "skillEffectIdStrNew": "42011031;42011032"}, {"id": "4201104", "skill_id": "42011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_4201104", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201104", "LCompareInfor": "BtlSkill_CompareInfor_4201104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011041;42011032", "skillEffectIdStrNew": "42011041;42011032"}, {"id": "4201105", "skill_id": "42011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_4201105", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201105", "LCompareInfor": "BtlSkill_CompareInfor_4201105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011051;42011052", "skillEffectIdStrNew": "42011051;42011052"}, {"id": "4201106", "skill_id": "42011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_4201106", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_4201106", "LCompareInfor": "BtlSkill_CompareInfor_4201106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011061;42011052", "skillEffectIdStrNew": "42011061;42011052"}, {"id": "4301001", "skill_id": "43010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4301001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4301001", "LCompareInfor": "BtlSkill_CompareInfor_4301001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301001", "skillEffectIdStr": "4301001", "skillEffectIdStrNew": "4301001"}, {"id": "4301101", "skill_id": "43011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4301101", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301101", "LCompareInfor": "BtlSkill_CompareInfor_4301101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011011", "skillEffectIdStrNew": "43011011"}, {"id": "4301102", "skill_id": "43011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_4301102", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301102", "LCompareInfor": "BtlSkill_CompareInfor_4301102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011021", "skillEffectIdStrNew": "43011021"}, {"id": "4301103", "skill_id": "43011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_4301103", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301103", "LCompareInfor": "BtlSkill_CompareInfor_4301103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011031", "skillEffectIdStrNew": "43011031"}, {"id": "4301104", "skill_id": "43011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_4301104", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301104", "LCompareInfor": "BtlSkill_CompareInfor_4301104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011041", "skillEffectIdStrNew": "43011041"}, {"id": "4301105", "skill_id": "43011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_4301105", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301105", "LCompareInfor": "BtlSkill_CompareInfor_4301105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011051", "skillEffectIdStrNew": "43011051"}, {"id": "4301106", "skill_id": "43011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_4301106", "skillIconId": "skill_battle_104", "LSkillDes": "BtlSkill_SkillDes_4301106", "LCompareInfor": "BtlSkill_CompareInfor_4301106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "5.9", "CD": "8", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4301101", "skillEffectIdStr": "43011061", "skillEffectIdStrNew": "43011061"}, {"id": "4401001", "skill_id": "44010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4401001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4401001", "LCompareInfor": "BtlSkill_CompareInfor_4401001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "4401001", "skillEffectIdStr": "4401001", "skillEffectIdStrNew": "4401001"}, {"id": "4401101", "skill_id": "44011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4401101", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401101", "LCompareInfor": "BtlSkill_CompareInfor_4401101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011011;44011012", "skillEffectIdStrNew": "44011011;44011012"}, {"id": "4401102", "skill_id": "44011", "skill_level": "2", "LSkillName": "BtlSkill_SkillName_4401102", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401102", "LCompareInfor": "BtlSkill_CompareInfor_4401102", "power": "2200", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011021;44011012", "skillEffectIdStrNew": "44011021;44011012"}, {"id": "4401103", "skill_id": "44011", "skill_level": "3", "LSkillName": "BtlSkill_SkillName_4401103", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401103", "LCompareInfor": "BtlSkill_CompareInfor_4401103", "power": "3000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011031;44011032", "skillEffectIdStrNew": "44011031;44011032"}, {"id": "4401104", "skill_id": "44011", "skill_level": "4", "LSkillName": "BtlSkill_SkillName_4401104", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401104", "LCompareInfor": "BtlSkill_CompareInfor_4401104", "power": "4400", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011041;44011032", "skillEffectIdStrNew": "44011041;44011032"}, {"id": "4401105", "skill_id": "44011", "skill_level": "5", "LSkillName": "BtlSkill_SkillName_4401105", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401105", "LCompareInfor": "BtlSkill_CompareInfor_4401105", "power": "6000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011051;44011052", "skillEffectIdStrNew": "44011051;44011052"}, {"id": "4401106", "skill_id": "44011", "skill_level": "6", "LSkillName": "BtlSkill_SkillName_4401106", "skillIconId": "skill_battle_113", "LSkillDes": "BtlSkill_SkillDes_4401106", "LCompareInfor": "BtlSkill_CompareInfor_4401106", "power": "8000", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "4.6", "CD": "8.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "1;2.5;360", "damageType": "1", "clientEffectId": "4401101", "skillEffectIdStr": "44011061;44011052", "skillEffectIdStrNew": "44011061;44011052"}, {"id": "4402001", "skill_id": "44020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4402001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4402001", "LCompareInfor": "BtlSkill_CompareInfor_4402001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "4402001", "skillEffectIdStr": "4402001", "skillEffectIdStrNew": "4402001"}, {"id": "4403001", "skill_id": "44030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4403001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4403001", "LCompareInfor": "BtlSkill_CompareInfor_4403001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "4403001", "skillEffectIdStr": "4403001", "skillEffectIdStrNew": "4403001"}, {"id": "4404001", "skill_id": "44040", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4404001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_4404001", "LCompareInfor": "BtlSkill_CompareInfor_4404001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "8.5", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4404001", "skillEffectIdStr": "4404001", "skillEffectIdStrNew": "4404001"}, {"id": "5201001", "skill_id": "52010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_5201001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_5201001", "LCompareInfor": "BtlSkill_CompareInfor_5201001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.25", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "9", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4201001", "skillEffectIdStr": "4201001", "skillEffectIdStrNew": "4201001"}, {"id": "5201101", "skill_id": "52011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_4201101", "skillIconId": "skill_battle_2", "LSkillDes": "BtlSkill_SkillDes_5201101", "LCompareInfor": "BtlSkill_CompareInfor_5201101", "power": "800", "LTypeDes": "", "priority": "1", "castType": "1", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "2", "CD": "7.9", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "1", "actTargetParam": "0;1;360", "damageType": "2", "clientEffectId": "4201101", "skillEffectIdStr": "42011011;42011012", "skillEffectIdStrNew": "42011011;42011012"}, {"id": "5203001", "skill_id": "52030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_5203001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_5203001", "LCompareInfor": "BtlSkill_CompareInfor_5203001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "0;2", "logicType": "1;3;4", "selectType": "7", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "2", "clientEffectId": "4404001", "skillEffectIdStr": "4404001", "skillEffectIdStrNew": "4404001"}, {"id": "7001001", "skill_id": "70010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_7001001", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_7001001", "LCompareInfor": "BtlSkill_CompareInfor_7001001", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "7001001", "skillEffectIdStrNew": "7001001"}, {"id": "7001002", "skill_id": "70010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_7001002", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_7001002", "LCompareInfor": "BtlSkill_CompareInfor_7001002", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "7001002", "skillEffectIdStrNew": "7001002"}, {"id": "7001003", "skill_id": "70010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_7001003", "skillIconId": "skill_battle_88", "LSkillDes": "BtlSkill_SkillDes_7001003", "LCompareInfor": "BtlSkill_CompareInfor_7001003", "power": "800", "LTypeDes": "", "priority": "2", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "7001003", "skillEffectIdStrNew": "7001003"}, {"id": "8001001", "skill_id": "80010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_8001001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_8001001", "LCompareInfor": "BtlSkill_CompareInfor_8001001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "8001001", "skillEffectIdStr": "8001001", "skillEffectIdStrNew": "8001001"}, {"id": "8002001", "skill_id": "80020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_8002001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_8002001", "LCompareInfor": "BtlSkill_CompareInfor_8002001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "8002001", "skillEffectIdStr": "8002001", "skillEffectIdStrNew": "8002001"}, {"id": "8003001", "skill_id": "80030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_8003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_8003001", "LCompareInfor": "BtlSkill_CompareInfor_8003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "1.5", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "8003001", "skillEffectIdStr": "8003001", "skillEffectIdStrNew": "8003001"}, {"id": "9001001", "skill_id": "90010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9001001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9001001", "LCompareInfor": "BtlSkill_CompareInfor_9001001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "2", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "9001001", "skillEffectIdStr": "9001001", "skillEffectIdStrNew": "9001001"}, {"id": "9002001", "skill_id": "90020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9002001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9002001", "LCompareInfor": "BtlSkill_CompareInfor_9002001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "2.3", "actCamp": "2", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "9002001", "skillEffectIdStr": "9002001", "skillEffectIdStrNew": "9002001"}, {"id": "9003001", "skill_id": "90030", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4", "selectType": "1", "selectParams": "15;1", "costDis": "9", "actCamp": "3", "actLogicType": "0;1;2;3;4", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "9003001", "skillEffectIdStr": "9003001", "skillEffectIdStrNew": "9003001"}, {"id": "10000001", "skill_id": "100000", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "10000001", "skillEffectIdStrNew": "10000001"}, {"id": "10001001", "skill_id": "100001", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "99999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "10001001", "skillEffectIdStrNew": "10001001"}, {"id": "10001002", "skill_id": "100001", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "10001002", "skillEffectIdStrNew": "10001002"}, {"id": "11001001", "skill_id": "110010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.7", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11001001", "skillEffectIdStr": "11001001", "skillEffectIdStrNew": "11001001"}, {"id": "11001101", "skill_id": "110011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11001101", "skillEffectIdStr": "11001101", "skillEffectIdStrNew": "11001101"}, {"id": "11001201", "skill_id": "110012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.3", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11001201", "skillEffectIdStr": "11001201", "skillEffectIdStrNew": "11001201"}, {"id": "11001301", "skill_id": "110013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.15", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11001301", "skillEffectIdStr": "11001301", "skillEffectIdStrNew": "11001301"}, {"id": "11002001", "skill_id": "110020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.4", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11002001", "skillEffectIdStr": "11002001", "skillEffectIdStrNew": "11002001"}, {"id": "11002101", "skill_id": "110021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.15", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11002101", "skillEffectIdStr": "11002101", "skillEffectIdStrNew": "11002101"}, {"id": "11002201", "skill_id": "110022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0.15", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11002201", "skillEffectIdStr": "11002201", "skillEffectIdStrNew": "11002201"}, {"id": "11002901", "skill_id": "110029", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "3", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "11002901", "skillEffectIdStr": "11002901", "skillEffectIdStrNew": "11002901"}, {"id": "12301001", "skill_id": "123010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "28", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301001", "skillEffectIdStr": "12301001", "skillEffectIdStrNew": "12301001"}, {"id": "12301101", "skill_id": "123011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "28", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301101", "skillEffectIdStr": "12301101", "skillEffectIdStrNew": "12301101"}, {"id": "12301201", "skill_id": "123012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "28", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301201", "skillEffectIdStr": "12301201", "skillEffectIdStrNew": "12301201"}, {"id": "12301301", "skill_id": "123013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "28", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301301", "skillEffectIdStr": "12301301", "skillEffectIdStrNew": "12301301"}, {"id": "12301901", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301901", "skillEffectIdStr": "12301901;12301902", "skillEffectIdStrNew": "12301901;12301902"}, {"id": "12301902", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301902", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "12301903", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "1", "actTargetParam": "0;3;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "12301903;12301904", "skillEffectIdStrNew": "12301903;12301904"}, {"id": "12301911", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301911", "skillEffectIdStr": "12301911;12301912", "skillEffectIdStrNew": "12301911;12301912"}, {"id": "12301912", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301912", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "12301913", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "1", "actTargetParam": "0;3;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "12301913;12301914", "skillEffectIdStrNew": "12301913;12301914"}, {"id": "12301921", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301921", "skillEffectIdStr": "12301921;12301922", "skillEffectIdStrNew": "12301921;12301922"}, {"id": "12301922", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301922", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "12301923", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "1", "actTargetParam": "0;3;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "12301923;12301924", "skillEffectIdStrNew": "12301923;12301924"}, {"id": "12301931", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301931", "skillEffectIdStr": "12301931;12301932", "skillEffectIdStrNew": "12301931;12301932"}, {"id": "12301932", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "3", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301932", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "12301933", "skill_id": "123019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "21", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "1", "actTargetParam": "0;3;360", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "12301933;12301934", "skillEffectIdStrNew": "12301933;12301934"}, {"id": "13301001", "skill_id": "133010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301001", "skillEffectIdStr": "13301001", "skillEffectIdStrNew": "13301001"}, {"id": "13301101", "skill_id": "133011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301101", "skillEffectIdStr": "13301101", "skillEffectIdStrNew": "13301101"}, {"id": "13301201", "skill_id": "133012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301201", "skillEffectIdStr": "13301201", "skillEffectIdStrNew": "13301201"}, {"id": "13301301", "skill_id": "133013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.2", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301301", "skillEffectIdStr": "13301301", "skillEffectIdStrNew": "13301301"}, {"id": "13301901", "skill_id": "133019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301901", "skillEffectIdStr": "13301901", "skillEffectIdStrNew": "13301901"}, {"id": "13301911", "skill_id": "133019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301911", "skillEffectIdStr": "13301911", "skillEffectIdStrNew": "13301911"}, {"id": "13301921", "skill_id": "133019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301921", "skillEffectIdStr": "13301921", "skillEffectIdStrNew": "13301921"}, {"id": "13301931", "skill_id": "133019", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "5", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "13301931", "skillEffectIdStr": "13301931", "skillEffectIdStrNew": "13301931"}, {"id": "13302001", "skill_id": "133020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.5", "CD": "2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;15;2", "damageType": "1", "clientEffectId": "13302001", "skillEffectIdStr": "13302001", "skillEffectIdStrNew": "13302001"}, {"id": "13302101", "skill_id": "133021", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.5", "CD": "2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;15;2", "damageType": "1", "clientEffectId": "13302101", "skillEffectIdStr": "13302101", "skillEffectIdStrNew": "13302101"}, {"id": "13302201", "skill_id": "133022", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.5", "CD": "2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;15;2", "damageType": "1", "clientEffectId": "13302201", "skillEffectIdStr": "13302201", "skillEffectIdStrNew": "13302201"}, {"id": "13302301", "skill_id": "133023", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.5", "CD": "2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;15;2", "damageType": "1", "clientEffectId": "13302301", "skillEffectIdStr": "13302301", "skillEffectIdStrNew": "13302301"}, {"id": "14101001", "skill_id": "141010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "3.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;3", "damageType": "1", "clientEffectId": "14101001", "skillEffectIdStr": "14101001;14101901", "skillEffectIdStrNew": "14101001;14101901"}, {"id": "14101101", "skill_id": "141011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "3.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;3", "damageType": "1", "clientEffectId": "14101101", "skillEffectIdStr": "14101101;14101901", "skillEffectIdStrNew": "14101101;14101901"}, {"id": "14101201", "skill_id": "141012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "3.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;3", "damageType": "1", "clientEffectId": "14101201", "skillEffectIdStr": "14101201;14101901", "skillEffectIdStrNew": "14101201;14101901"}, {"id": "14101301", "skill_id": "141013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "3.2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "15", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;3", "damageType": "1", "clientEffectId": "14101301", "skillEffectIdStr": "14101301;14101901", "skillEffectIdStrNew": "14101301;14101901"}, {"id": "14301001", "skill_id": "143010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "2", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "6", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;4", "damageType": "1", "clientEffectId": "14301001", "skillEffectIdStr": "14301001", "skillEffectIdStrNew": "14301001"}, {"id": "14301101", "skill_id": "143011", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "6", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;4", "damageType": "1", "clientEffectId": "14301101", "skillEffectIdStr": "14301101", "skillEffectIdStrNew": "14301101"}, {"id": "14301201", "skill_id": "143012", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "6", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;4", "damageType": "1", "clientEffectId": "14301201", "skillEffectIdStr": "14301201", "skillEffectIdStrNew": "14301201"}, {"id": "14301301", "skill_id": "143013", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "2", "needTurn": "5", "skillType": "0", "skillParams": "-1", "initCD": "0.1", "CD": "1", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "27", "selectParams": "15;1", "costDis": "6", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "2", "actTargetParam": "1;8;4", "damageType": "1", "clientEffectId": "14301301", "skillEffectIdStr": "14301301", "skillEffectIdStrNew": "14301301"}, {"id": "18211001", "skill_id": "182110", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18211001", "skillEffectIdStrNew": "18211001"}, {"id": "18211101", "skill_id": "182111", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "100", "selectType": "1", "selectParams": "99;1", "costDis": "99", "actCamp": "2", "actLogicType": "100", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "12301901", "skillEffectIdStr": "18211101;18211102", "skillEffectIdStrNew": "18211101;18211102"}, {"id": "18211102", "skill_id": "182111", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "1", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "100", "selectType": "1", "selectParams": "99;1", "costDis": "99", "actCamp": "2", "actLogicType": "100", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "18211102", "skillEffectIdStr": "1", "skillEffectIdStrNew": "1"}, {"id": "18212001", "skill_id": "182120", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18212001", "skillEffectIdStrNew": "18212001"}, {"id": "18212101", "skill_id": "182121", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "5", "selectParams": "99;99", "costDis": "99", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18212101", "skillEffectIdStrNew": "18212101"}, {"id": "18213001", "skill_id": "182130", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18213001", "skillEffectIdStrNew": "18213001"}, {"id": "18213101", "skill_id": "182131", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "5", "selectParams": "99;99", "costDis": "99", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18213101", "skillEffectIdStrNew": "18213101"}, {"id": "18214001", "skill_id": "182140", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18214001", "skillEffectIdStrNew": "18214001"}, {"id": "18214101", "skill_id": "182141", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "5", "selectParams": "99;99", "costDis": "99", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18214101", "skillEffectIdStrNew": "18214101"}, {"id": "18215001", "skill_id": "182150", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18215001", "skillEffectIdStrNew": "18215001"}, {"id": "18215101", "skill_id": "182151", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "5", "selectParams": "99;99", "costDis": "99", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18215101", "skillEffectIdStrNew": "18215101"}, {"id": "18216001", "skill_id": "182160", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "999", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18216001", "skillEffectIdStrNew": "18216001"}, {"id": "18216101", "skill_id": "182161", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "5", "selectParams": "99;99", "costDis": "99", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18216101", "skillEffectIdStrNew": "18216101"}, {"id": "18301001", "skill_id": "183010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "1", "castType": "3", "needTurn": "0", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "0", "commCD": "0", "camp": "1", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "15", "selectParams": "15;1", "costDis": "15", "actCamp": "1", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "1", "skillEffectIdStr": "18301001", "skillEffectIdStrNew": "18301001"}, {"id": "19101001", "skill_id": "191010", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "15;1", "costDis": "1", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "19101001", "skillEffectIdStr": "19101001", "skillEffectIdStrNew": "19101001"}, {"id": "19102001", "skill_id": "191020", "skill_level": "1", "LSkillName": "BtlSkill_SkillName_9003001", "skillIconId": "1;1", "LSkillDes": "BtlSkill_SkillDes_9003001", "LCompareInfor": "BtlSkill_CompareInfor_9003001", "power": "0", "LTypeDes": "", "priority": "0", "castType": "2", "needTurn": "1", "skillType": "0", "skillParams": "-1", "initCD": "0", "CD": "1.5", "commCD": "0", "camp": "2", "priorityLogicType": "-1", "logicType": "0;1;2;3;4;5;6;7", "selectType": "1", "selectParams": "5;1", "costDis": "1", "actCamp": "2", "actLogicType": "0;1;2;3;4;5;6;7", "actTargetType": "0", "actTargetParam": "0", "damageType": "1", "clientEffectId": "19101001", "skillEffectIdStr": "19101001", "skillEffectIdStrNew": "19101001"}]