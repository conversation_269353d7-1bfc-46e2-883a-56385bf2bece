---
description: 
globs: 
alwaysApply: true
---

# 全局开发规则

## 文档编写原则
- 一切文档（包括技术文档、设计文档）都应尽量追求简洁。
- 涉及代码的文档，遵循敏捷宣言原则，若文档与代码冲突，以代码为准。

## 开发文档编写指南
- 如果在开发完一个功能后创建总结文档，请遵循以下格式：`YYYYMMDD-功能名称.md`（英文功能名，使用'-'连接单词），放置在相应模块的docs/development目录下（注意请使用工具来获取真实时间）
- 注意，你无需每个任务都创建总结文档。如有可能，请询问用户是否需要创建总结文档（你可以发起一次获取时间的工具请求，如用户拒绝，则不必创建总结）

##日报周报月报指南
- 请在docs/reports目录下创建时报、日报、周报、月报文档，遵循以下格式：`YYYYMMDD-H-时报.md`、`YYYYMMDD-日报.md`、`YYYYMMDD-周报.md`、`YYYYMMDD-月报.md`（英文功能名，使用'-'连接单词），放置在相应模块的docs/reports目录下（助理请请使用工具来获取真实时间）